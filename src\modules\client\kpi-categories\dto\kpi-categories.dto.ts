import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';
import { NSKPI } from '~/common/enums';

export class UpsertKpiCategoryDtoItem {
    @ApiProperty({ example: NSKPI.ECategoryKey.CUSTOMERS_COUNT })
    @IsString()
    key: NSKPI.ECategoryKey;

    @ApiProperty({ example: 'Số lượng khách hàng' })
    @IsString()
    @MaxLength(255)
    label: string;

    @ApiProperty({ example: 'khách', required: false })
    @IsOptional()
    @IsString()
    @MaxLength(64)
    unit?: string;

    @ApiProperty({ example: NSKPI.ECategoryStatus.ACTIVE })
    @IsEnum(NSKPI.ECategoryStatus)
    status: NSKPI.ECategoryStatus;
}

export class UpsertKpiCategoriesDto {
    @ApiProperty({ type: [UpsertKpiCategoryDtoItem] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpsertKpiCategoryDtoItem)
    items: UpsertKpiCategoryDtoItem[];
}
