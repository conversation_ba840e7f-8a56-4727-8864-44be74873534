import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { BusinessException } from '~/@systems/exceptions';
import { NSSettingOption } from '~/common/enums/setting-option.enum';
import { NSSettingString } from '~/common/enums/setting-string.enum';
import { SettingStringEntity } from '~/domains/primary/setting-string/setting-string.entity';
import { SettingStringRepo } from '~/domains/primary/setting-string/setting-string.repo';
import { clientSessionContext } from '../client-session.context';

@Injectable()
export class SettingStringService {
    constructor(
        @InjectRepo(SettingStringRepo) private readonly settingStringRepo: SettingStringRepo,
    ) {}

    private getDefaults(type: 'string' | 'option') {
        return type === 'option' ? NSSettingOption.EDefaultConfig : NSSettingString.EDefaultConfig;
    }

    /**
     * <PERSON><PERSON><PERSON> cấu hình mặc định hiện tại cho Tenant.
     * Hợp nhất giá trị từ DB (nếu có) với giá trị mặc định hardcode.
     * DB override DEFAULTS.
     */
    async getTenantDefaultConfig(type: 'string' | 'option'): Promise<Record<string, any>> {
        const { tenantId } = clientSessionContext;
        const dbSettings = await this.settingStringRepo.find({ where: { tenantId, type } });

        const dbConfig = dbSettings.reduce(
            (acc, item) => {
                acc[item.key] = item.value;
                return acc;
            },
            {} as Record<string, any>,
        );

        const defaults = this.getDefaults(type);
        return Object.assign({}, defaults, dbConfig);
    }

    /**
     * Lấy giá trị default theo key
     */
    async getDefaultValueByKey(type: 'string' | 'option', key: string) {
        const { tenantId } = clientSessionContext;
        const setting = await this.settingStringRepo.findOne({ where: { key, tenantId, type } });
        if (setting) return setting.value;
        const defaults = this.getDefaults(type);
        return defaults[key as keyof typeof defaults] ?? null;
    }

    /**
     * Lấy nhiều giá trị default theo danh sách keys
     */
    async getDefaultValuesByKeys(
        type: 'string' | 'option',
        keys: string[],
    ): Promise<Record<string, any>> {
        const { tenantId } = clientSessionContext;
        if (!Array.isArray(keys) || keys.length === 0) return {};
        const settings = await this.settingStringRepo.find({ where: { tenantId, type } });
        const setKeys = new Set(keys);
        const filtered = settings.filter(s => setKeys.has(s.key));
        const mapFromDb = new Map(filtered.map(s => [s.key, s.value]));
        const defaults = this.getDefaults(type);

        const result: Record<string, any> = {};
        for (const k of keys) {
            result[k] = mapFromDb.get(k) ?? defaults[k as keyof typeof defaults] ?? null;
        }

        return result;
    }

    /**
     * Upsert cấu hình mặc định cho Tenant từ danh sách key-value.
     * Hạn chế: chỉ chấp nhận keys hợp lệ định nghĩa trong EDefaultConfigKey.
     * Đồng bộ (giữ các key hợp lệ có trong DEFAULTS; không xóa key lạ trong DB khác domain).
     */
    @DefTransaction()
    async saveTenantDefaultConfig(
        type: 'string' | 'option',
        config: { key: string; value: any }[],
    ): Promise<{ success: boolean }> {
        const { tenantId } = clientSessionContext;

        if (!Array.isArray(config)) {
            throw new BusinessException('Payload không hợp lệ');
        }

        if (type === 'string') {
            const uniqueKeys = new Set(config.map(i => i.key));
            if (uniqueKeys.size !== config.length) {
                throw new BusinessException('Danh sách cấu hình có key trùng nhau');
            }
            const validKeys = new Set<string>(
                Object.values(NSSettingString.EDefaultConfigKey) as string[],
            );
            const invalidKey = config.find(i => !validKeys.has(String(i.key)));
            if (invalidKey) {
                throw new BusinessException(`Key không hợp lệ: ${invalidKey.key}`);
            }
            const existing = await this.settingStringRepo.find({
                where: { tenantId, type: 'string' },
            });
            for (const { key, value } of config) {
                const found = existing.find(e => e.key === key);
                if (found) {
                    found.value = value;
                    await this.settingStringRepo.update(found.id, found);
                } else {
                    await this.settingStringRepo.save({
                        key,
                        value,
                        type: 'string',
                        tenantId,
                    } as Partial<SettingStringEntity>);
                }
            }
            return { success: true };
        } else {
            const uniqueValues = new Set(config.map(i => i.value));
            if (uniqueValues.size !== config.length) {
                throw new BusinessException('Danh sách cấu hình có value trùng nhau');
            }
            const existing = await this.settingStringRepo.find({
                where: { tenantId, type: 'option' },
            });
            for (const { key, value } of config) {
                const found = existing.find(e => e.value === value);
                if (found) {
                    found.value = value;
                    await this.settingStringRepo.update(found.id, found);
                } else {
                    await this.settingStringRepo.save({
                        key,
                        value,
                        type: 'option',
                        tenantId,
                    } as Partial<SettingStringEntity>);
                }
            }
            const newValues = Array.from(uniqueValues);
            const toDelete = existing.filter(s => !newValues.includes(s.value));
            if (toDelete.length > 0) {
                await this.settingStringRepo.remove(toDelete);
            }
            return { success: true };
        }
    }
}
