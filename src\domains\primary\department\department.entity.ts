import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('department')
export class DepartmentEntity extends PrimaryBaseEntity {
    @Column({ type: 'uuid', nullable: true })
    tenantId: string;

    @Column({ type: 'varchar', nullable: true })
    name: string;

    @Column({ type: 'varchar', nullable: true })
    code: string;

    @Column({ type: 'varchar', nullable: true })
    description?: string;

    @Column({ type: 'uuid', nullable: true })
    parentId?: string;

    @Column({ type: 'varchar', default: 'ACTIVE' })
    status: string;

    @Column({ type: 'uuid', nullable: true })
    departmentAuthId?: string;
}
