export namespace NSCustomer {
    export enum EType {
        BUSINESS = 'BUSINESS',
        PERSONAL = 'PERSONAL',
    }
    //#region Loại khách hàng
    export enum ECustomerType {
        // Khách hàng chưa định danh
        NEW = 'NEW',
        // Khách hàng tiềm năng
        NURTURE = 'NURTURE',
        // Khách hàng đủ điều kiện
        QUALIFIED = 'QUALIFIED',
        // Khách hàng cơ hội
        OPPORTUNITY = 'OPPORTUNITY',
        // Khách hàng chính thức
        WON = 'WON',
        // Khách hàng chăm sóc & phát triển
        CARE = 'CARE',
        // Khách hàng không thành công (rời bỏ)
        LOST = 'LOST',
    }

    export const ECUSTOMER_TYPE_LOCALE_LABEL = {
        [ECustomerType.NEW]: 'enums.NSCustomer.ECustomerType.NEW',
        [ECustomerType.NURTURE]: 'enums.NSCustomer.ECustomerType.NURTURE',
        [ECustomerType.QUALIFIED]: 'enums.NSCustomer.ECustomerType.QUALIFIED',
        [ECustomerType.OPPORTUNITY]: 'enums.NSCustomer.ECustomerType.OPPORTUNITY',
        [ECustomerType.WON]: 'enums.NSCustomer.ECustomerType.WON',
        [ECustomerType.CARE]: 'enums.NSCustomer.ECustomerType.CARE',
        [ECustomerType.LOST]: 'enums.NSCustomer.ECustomerType.LOST',
    };
    //#endregion

    //#region Chức vụ
    export enum EPosition {
        CEO = 'CEO', // Tổng giám đốc
        CTO = 'CTO', // Giám đốc kỹ thuật
        CMO = 'CMO', // Giám đốc marketing
        CRO = 'CRO', // Giám đốc kinh doanh
        CFO = 'CFO', // Giám đốc tài chính
        HR = 'HR', // Giám đốc nhân sự
        IT = 'IT', // Giám đốc công nghệ thông tin
        OTHER = 'OTHER', // Khác
    }

    export const EPOSITION_LOCALE_LABEL = {
        [EPosition.CEO]: 'enums.NSCustomer.EPosition.CEO',
        [EPosition.CTO]: 'enums.NSCustomer.EPosition.CTO',
        [EPosition.CMO]: 'enums.NSCustomer.EPosition.CMO',
        [EPosition.CRO]: 'enums.NSCustomer.EPosition.CRO',
        [EPosition.CFO]: 'enums.NSCustomer.EPosition.CFO',
        [EPosition.HR]: 'enums.NSCustomer.EPosition.HR',
        [EPosition.IT]: 'enums.NSCustomer.EPosition.IT',
        [EPosition.OTHER]: 'enums.NSCustomer.EPosition.OTHER',
    };
    //#endregion

    //#region Nguồn gốc khách hàng
    export enum ESource {
        WEBSITE = 'WEBSITE',
        FACEBOOK = 'FACEBOOK',
        GOOGLE = 'GOOGLE',
        INTRODUCTION = 'INTRODUCTION',
        REFERRAL = 'REFERRAL',
        OTHER = 'OTHER',
    }

    export const ESOURCE_LOCALE_LABEL = {
        [ESource.WEBSITE]: 'enums.NSCustomer.ESource.WEBSITE',
        [ESource.FACEBOOK]: 'enums.NSCustomer.ESource.FACEBOOK',
        [ESource.GOOGLE]: 'enums.NSCustomer.ESource.GOOGLE',
        [ESource.INTRODUCTION]: 'enums.NSCustomer.ESource.INTRODUCTION',
        [ESource.OTHER]: 'enums.NSCustomer.ESource.OTHER',
    };
    //#endregion

    //#region Thị trường
    export enum EMarket {
        // Trong nước
        DOMESTIC = 'DOMESTIC',
        // Nước ngoài
        FOREIGN = 'FOREIGN',
        // Cả hai
        BOTH = 'BOTH',
    }

    export const EMARKET_LOCALE_LABEL = {
        [EMarket.DOMESTIC]: 'enums.NSCustomer.EMarket.DOMESTIC',
        [EMarket.FOREIGN]: 'enums.NSCustomer.EMarket.FOREIGN',
        [EMarket.BOTH]: 'enums.NSCustomer.EMarket.BOTH',
    };
    //#endregion

    //#region Loại khách hàng
    export enum ECustomerKind {
        UNKNOWN = 'UNKNOWN',
        INDIVIDUAL = 'INDIVIDUAL',
        BUSINESS = 'BUSINESS',
    }

    export const ECUSTOMER_KIND_LOCALE_LABEL = {
        [ECustomerKind.UNKNOWN]: 'enums.NSCustomer.ECustomerKind.UNKNOWN',
        [ECustomerKind.INDIVIDUAL]: 'enums.NSCustomer.ECustomerKind.INDIVIDUAL',
        [ECustomerKind.BUSINESS]: 'enums.NSCustomer.ECustomerKind.BUSINESS',
    };
    //#endregion

    //#region Active status
    export enum EActiveStatus {
        ACTIVE = 'ACTIVE',
        INACTIVE = 'INACTIVE',
    }

    export const EACTIVE_STATUS_LOCALE_LABEL = {
        [EActiveStatus.ACTIVE]: 'enums.NSCustomer.EActiveStatus.ACTIVE',
        [EActiveStatus.INACTIVE]: 'enums.NSCustomer.EActiveStatus.INACTIVE',
    };
    //#endregion
}
