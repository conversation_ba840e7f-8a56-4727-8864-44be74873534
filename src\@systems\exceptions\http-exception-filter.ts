import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { I18nService } from 'nestjs-i18n';
import { ApiException, ValidateException } from './dto';
import { checkTypeClass, ExceptionClassName, formatException } from './helper';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
    constructor(private readonly i18n: I18nService) {}

    catch(exception: any, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        let status = HttpStatus.INTERNAL_SERVER_ERROR;
        console.log('exception', exception);
        console.warn(`[HttpExceptionFilter] ${request.path}`, {
            ...exception,
        });

        // Handle ValidateException
        if (checkTypeClass(exception, ValidateException, ExceptionClassName.ValidateException)) {
            status = exception.getStatus?.() ?? HttpStatus.UNPROCESSABLE_ENTITY;
            const apiException = new ApiException('Value not right', status, exception.messages);
            const responseBody = formatException(apiException, this.i18n, true);
            response.status(status).send(responseBody);
            return;
        }

        // Handle HttpException
        if (checkTypeClass(exception, HttpException, ExceptionClassName.HttpException)) {
            status = exception.getStatus();
            const res = exception.getResponse?.();

            if (typeof res === 'string') {
                const apiException = new ApiException(res, status);
                response.status(status).send(formatException(apiException, this.i18n));
                return;
            }

            const message = res?.['message'];
            if (typeof message === 'string') {
                const apiException = new ApiException(message, status);
                response.status(status).send(formatException(apiException, this.i18n));
                return;
            }

            const apiException = new ApiException('Unknown', status, message || res);
            response.status(status).send(formatException(apiException, this.i18n));
            return;
        }

        // Handle raw string exception
        if (typeof exception === 'string') {
            const apiException = new ApiException(exception, HttpStatus.BAD_REQUEST);
            response.status(HttpStatus.BAD_REQUEST).send(formatException(apiException, this.i18n));
            return;
        }

        // Handle ApiException directly
        if (exception instanceof ApiException) {
            response.status(exception.httpCode).send(formatException(exception, this.i18n));
            return;
        }

        // Unknown fallback
        const apiException = new ApiException('Unknown', status, exception);
        response.status(status).send(formatException(apiException, this.i18n));
    }
}
