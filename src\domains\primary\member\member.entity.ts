import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { NSMember } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('member')
export class MemberEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column('uuid', { nullable: true })
    ssoAccountId: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    ssoToken?: string;

    @ApiProperty({ example: '<EMAIL>' })
    @Column({ nullable: true, unique: true, type: 'varchar', length: 255 })
    username: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    password?: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    fullName?: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    avatar?: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    type?: NSMember.EType;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    departmentId?: string;

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    positionId?: string;

    @ApiProperty()
    @Column({ type: 'varchar', default: NSMember.EStatus.INACTIVE })
    status: NSMember.EStatus;

    @ApiPropertyOptional({ example: ['roleId1', 'roleId2'] })
    @Column({
        type: 'text',
        array: true,
        nullable: true,
        default: '{}', // hoặc có thể dùng `[]` trong code JS
    })
    roleIds: string[];

    @ApiPropertyOptional()
    @Column({ nullable: true, type: 'text' })
    fcmToken?: string;
}
