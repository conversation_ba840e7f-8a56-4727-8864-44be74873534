import { Body, Query, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES } from '~/common/enums/permission-config.enum';
import { PermissionGuard, RequirePermissions } from '../@guards';
import { CustomerService } from './customer.service';
import {
    CreateCustomerDto,
    DetailCustomerDto,
    ListCustomerDto,
    UpdateCustomerDto,
} from './dto/customer.dto';

@UseGuards(PermissionGuard)
@DefController('customer')
export class CustomerController {
    constructor(private readonly customerService: CustomerService) {}

    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.VIEW])
    @DefGet('list')
    async list(@Query() params: ListCustomerDto) {
        return this.customerService.listCustomer(params);
    }

    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.VIEW])
    @DefGet('birthdays')
    async birthdays(
        @Query() params: { month: number; day?: number; pageIndex?: number; pageSize?: number },
    ) {
        return this.customerService.listBirthdays(params);
    }

    @DefGet('select-box')
    async selectBox() {
        return this.customerService.selectBox();
    }

    @DefGet('details')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.VIEW])
    async detail(@Query() params: DetailCustomerDto) {
        return this.customerService.getCustomerById(params);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.CREATE])
    async create(@Body() body: CreateCustomerDto) {
        return this.customerService.createCustomer(body);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.UPDATE])
    async update(@Body() body: UpdateCustomerDto) {
        return this.customerService.updateCustomer(body);
    }

    // @DefPost('delete')
    // @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.UPDATE])
    // async delete(@Body() body: DetailCustomerDto) {
    //     return this.customerService.deleteCustomer(body);
    // }

    @DefPost('set-active')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.UPDATE])
    async setActive(@Body() params: { id: string }) {
        return this.customerService.setActive(params.id);
    }

    @DefPost('get-by-ids')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.VIEW])
    async getCustomerByIds(@Body() body: { ids: string[] }) {
        return this.customerService.getCustomerByIds(body.ids);
    }

    @DefPost('upload')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER.CREATE])
    @UseInterceptors(FileInterceptor('file'))
    async uploadCustomer(@UploadedFile() file: Express.Multer.File) {
        return this.customerService.uploadCustomer(file);
    }
}
