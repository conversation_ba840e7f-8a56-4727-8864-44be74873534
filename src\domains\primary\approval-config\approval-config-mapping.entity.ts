import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { NSApproval } from '~/common/enums/approval.enum';
import { ApprovalConfigEntity } from '../approval-config/approval-config.entity';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('approval_config_mapping')
export class ApprovalConfigMappingEntity extends PrimaryBaseEntity {
    @Column({
        type: 'enum',
        enum: NSApproval.ApprovalBusinessType,
    })
    businessType: string;

    @Column({ type: 'uuid' })
    approvalConfigId: string;

    @Column({ default: true })
    isDefault: boolean;

    @ManyToOne(() => ApprovalConfigEntity, approvalConfig => approvalConfig.requests, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'approvalConfigId' })
    approvalConfig: ApprovalConfigEntity;
}
