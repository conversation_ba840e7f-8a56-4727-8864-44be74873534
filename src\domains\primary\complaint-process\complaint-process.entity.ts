import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity({ name: 'complaint_process' })
export class ComplaintProcessEntity extends PrimaryBaseEntity {
    @Column({ type: 'varchar', nullable: false })
    complaintId: string;

    @Column({ type: 'varchar', nullable: true })
    handlerId: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    processDate: Date;

  @Column({ type: 'int', default: 1 })
  processNumber: number;
}
