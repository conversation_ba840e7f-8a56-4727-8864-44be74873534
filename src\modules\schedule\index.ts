import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'ape-nestjs-typeorm3-kit';
import { join } from 'path';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { ScheduleMiddleware } from './shcedule.middleware';
import { REFIX_MODULE } from '../config-module';

const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);

@ChildModule({
    prefix: REFIX_MODULE.schedule,
    imports: [
        PrimaryRepoModule,
    ],
    providers: [...services],
    controllers: [...controllers],
})
export class ScheduleModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(ScheduleMiddleware).forRoutes(`${REFIX_MODULE.schedule}/*`);
    }
}
