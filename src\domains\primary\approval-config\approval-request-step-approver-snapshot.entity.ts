import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { NSApproval } from '~/common/enums/approval.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApprovalRequestStepEntity } from './approval-request-step.entity';
import { ApprovalRequestEntity } from './approval-request.entity';

/**
 * ApprovalRequestStepApproverSnapshotEntity
 *
 * Lưu snapshot danh sách approvers cụ thể khi tạo request
 * Dùng cho audit trail và history tracking
 */
@Entity('approval_request_step_approver_snapshot')
@Index(['requestId', 'stepId'])
@Index(['requestId', 'snapshotDate'])
export class ApprovalRequestStepApproverSnapshotEntity extends PrimaryBaseEntity {
    @Column({ type: 'uuid' })
    @Index()
    requestId: string;

    @Column({ type: 'uuid' })
    @Index()
    stepId: string;

    @Column({ type: 'uuid' })
    @Index()
    configStepApproverId: string; // Reference đến config approver

    // Snapshot: Lưu danh sách members cụ thể lúc tạo request
    @Column({ type: 'uuid' })
    @Index()
    memberId: string; // Member ID cụ thể lúc snapshot

    // Metadata snapshot
    @Column({ type: 'timestamptz' })
    snapshotDate: Date; // Thời điểm tạo snapshot

    // Thông tin member lúc snapshot (để audit)
    @Column({ type: 'varchar', nullable: true })
    memberFullName?: string;

    @Column({ type: 'uuid', nullable: true })
    memberPositionId?: string;

    @Column({ type: 'varchar', nullable: true })
    memberPositionName?: string;

    @Column({ type: 'uuid', nullable: true })
    memberDepartmentId?: string;

    @Column({ type: 'varchar', nullable: true })
    memberDepartmentName?: string;

    // Config gốc (để trace)
    @Column({ type: 'enum', enum: NSApproval.ApproverType })
    approverType: NSApproval.ApproverType;

    @Column({ type: 'uuid', nullable: true })
    positionRefId?: string;

    @Column({ type: 'uuid', nullable: true })
    departmentRefId?: string;

    @Column({ type: 'uuid', nullable: true })
    roleRefId?: string;

    @ManyToOne(() => ApprovalRequestEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'requestId' })
    request: ApprovalRequestEntity;

    @ManyToOne(() => ApprovalRequestStepEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'stepId' })
    step: ApprovalRequestStepEntity;
}
