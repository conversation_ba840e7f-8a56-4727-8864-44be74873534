import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSGroupCustomer } from '~/common/enums/group-customer.enum';

export class CreateGroupCustomerDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ example: 'Nhóm khách hàng VIP' })
  @MaxLength(255)
  @IsOptional()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  customerIds?: string[];

  @ApiPropertyOptional({ enum: NSGroupCustomer.EStatus, default: NSGroupCustomer.EStatus.ACTIVE })
  @IsOptional()
  @IsEnum(NSGroupCustomer.EStatus)
  status?: NSGroupCustomer.EStatus;
}

export class ListGroupCustomerDto extends PageRequest {
  @ApiPropertyOptional({ description: 'Tìm kiếm theo mã' })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo tên' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: NSGroupCustomer.EStatus })
  @IsOptional()
  status?: NSGroupCustomer.EStatus;
}

export class UpdateGroupCustomerDto extends CreateGroupCustomerDto {
  @ApiPropertyOptional({ description: 'ID nhóm khách hàng' })
  @IsOptional()
  @IsString()
  id: string;
}

export class UpdateGroupStatusDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty({ enum: NSGroupCustomer.EStatus })
  @IsEnum(NSGroupCustomer.EStatus)
  status: NSGroupCustomer.EStatus;
}

export class DetailGroupCustomerDto extends PageRequest {
  @ApiProperty({ description: 'ID nhóm khách hàng' })
  @IsString()
  id: string;
}
