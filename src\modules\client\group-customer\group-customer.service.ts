import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike, In } from 'typeorm';
import { NSGroupCustomer } from '~/common/enums/group-customer.enum';
import { CustomerRepo, MemberRepo } from '~/domains/primary';
import { GroupCustomerRepo } from '~/domains/primary/group-customer/group-customer.repo';
import { clientSessionContext } from '../client-session.context';
import {
    CreateGroupCustomerDto,
    ListGroupCustomerDto,
    UpdateGroupCustomerDto,
} from './dto/group-customer.dto';

@Injectable()
export class GroupCustomerService {
    constructor(
        @InjectRepo(GroupCustomerRepo) private readonly groupRepo: GroupCustomerRepo,
        @InjectRepo(CustomerRepo) private readonly customerRepo: CustomerRepo,
        @InjectRepo(MemberRepo) private readonly memberRepo: MemberRepo,
    ) {}

    async list(params: ListGroupCustomerDto) {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize } = params;
        const where: any = {};

        if (params.code) where.code = ILike(`%${params.code}%`);
        if (params.name) where.name = ILike(`%${params.name}%`);
        if (params.status) where.status = params.status;

        if (pageSize === -1) {
            const [data, total] = await this.groupRepo.findAndCount({
                where: { ...where, tenantId },
                order: { createdDate: 'DESC' },
            });
            const mapped = data.map(item => ({
                ...item,
                customerCount: item.customerIds?.length || 0,
            }));
            return { total, data: mapped } as any;
        }

        const res = await this.groupRepo.findPagination(
            { where: { ...where, tenantId }, order: { createdDate: 'DESC' } },
            { pageIndex, pageSize },
        );
        const mapped = res.data.map(item => ({
            ...item,
            customerCount: item.customerIds?.length || 0,
        }));
        return { total: res.total, data: mapped } as any;
    }

    @DefTransaction()
    async create(data: CreateGroupCustomerDto) {
        const { tenantId, memberId } = clientSessionContext;
        if (!data.code) {
            const existing = await this.groupRepo.find({ where: { tenantId } });
            data.code = `GC${String(existing.length + 1).padStart(5, '0')}`;
        }
        const entity = await this.groupRepo.save({
            ...data,
            tenantId,
            createdBy: memberId,
            status: data.status || NSGroupCustomer.EStatus.ACTIVE,
        } as any);
        return entity;
    }

    async detail(params: { id: string; pageIndex?: number; pageSize?: number }) {
        const { tenantId } = clientSessionContext;
        const { id, pageIndex = 1, pageSize = 10 } = params || ({} as any);
        const entity = await this.groupRepo.findOne({ where: { id, tenantId } });
        if (!entity) return null as any;

        const allIds: string[] = (entity.customerIds || []) as any;
        const customersTotal = allIds.length;
        const offset = Math.max(0, (pageIndex - 1) * pageSize);
        const pageIds = allIds.slice(offset, offset + pageSize);

        const customers = pageIds.length
            ? await this.customerRepo.find({ where: { id: In(pageIds), tenantId } })
            : [];

        const memberIds = Array.from(new Set(customers.flatMap(c => c.salesRep || [])));
        const members = memberIds.length
            ? await this.memberRepo.find({ where: { id: In(memberIds), tenantId } })
            : [];
        const memberDict = Object.fromEntries(members.map(m => [m.id, m.fullName]));
        const customerDetails = customers.map(c => ({
            id: c.id,
            name: c.name,
            customerType: c.customerType,
            address: c.address,
            salesRepNames: (c.salesRep || []).map(id => memberDict[id]).filter(Boolean),
        }));

        return { ...entity, customerCount: customersTotal, customersTotal, customers: customerDetails } as any;
    }

    @DefTransaction()
    async update(body: UpdateGroupCustomerDto) {
        const { tenantId, memberId } = clientSessionContext;
        const found = await this.groupRepo.findOne({ where: { id: body.id, tenantId } });
        if (!found) return { message: 'Không tìm thấy nhóm khách hàng' } as any;
        found.name = body.name;
        found.description = body.description;
        found.customerIds = body.customerIds;
        found.status = body.status || found.status;
        found.updatedBy = memberId;
        found.updatedDate = new Date();
        await this.groupRepo.update(found.id, { ...found, id: found.id });
        return { message: 'Cập nhật nhóm khách hàng thành công' };
    }

    @DefTransaction()
    async updateStatus(id: string, status: NSGroupCustomer.EStatus) {
        const { tenantId } = clientSessionContext;
        const found = await this.groupRepo.findOne({ where: { id, tenantId } });
        if (!found) return { message: 'Không tìm thấy nhóm khách hàng' } as any;
        found.status = status;
        await this.groupRepo.update(found.id, { ...found, id: found.id });
        return { message: 'Cập nhật trạng thái nhóm khách hàng thành công' };
    }
}
