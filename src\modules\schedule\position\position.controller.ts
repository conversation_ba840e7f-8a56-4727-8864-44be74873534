import { DefController, DefPost } from "ape-nestjs-typeorm3-kit";
import { PositionService } from "./position.service";

@DefController("position")
export class PositionController {
    constructor(
        private readonly positionService: PositionService,
    ) {}

    // POST Create Employee
    @DefPost("sync")
    syncPosition() {
        return this.positionService.compareWithAuth();
    }
}
    
