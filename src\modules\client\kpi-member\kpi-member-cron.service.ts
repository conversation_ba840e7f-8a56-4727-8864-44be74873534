import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { In, LessThan } from 'typeorm';
import { NSKPI } from '~/common/enums/kpi.enum';
import { KpiMemberRepo } from '~/domains/primary/kpi-member/kpi-member.repo';

@Injectable()
export class KpiMemberCronService {
    private readonly logger = new Logger(KpiMemberCronService.name);

    constructor(@InjectRepo(KpiMemberRepo) private readonly kpiMemberRepo: KpiMemberRepo) {}

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async markExpiredKpis() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const candidates = await this.kpiMemberRepo.find({
                where: {
                    periodEnd: LessThan(today),
                    status: In([
                        NSKPI.ETargetStatus.NEW,
                        NSKPI.ETargetStatus.SENT,
                        NSKPI.ETargetStatus.APPROVED,
                    ]),
                } as any,
            });

            if (!candidates.length) {
                this.logger.log('No KPI members to expire');
                return;
            }

            for (const kpi of candidates) {
                await this.kpiMemberRepo.update(kpi.id!, {
                    status: NSKPI.ETargetStatus.EXPIRED,
                } as any);
            }

            this.logger.log(`Expired ${candidates.length} KPI members`);
        } catch (error: any) {
            this.logger.error(`Error expiring KPI members: ${error?.message}`, error?.stack);
            throw error;
        }
    }
}
