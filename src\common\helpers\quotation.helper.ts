import * as dayjs from 'dayjs';

export const formatCurrency = (value: any, locale = 'vi-VN') => {
    const num = Number(value || 0);
    return new Intl.NumberFormat(locale).format(num);
};
export const formatDate = (value?: string | Date, locale = 'vi-VN') => {
    if (!value) return '';
    const d = dayjs(value);
    if (locale === 'vi-VN') return d.format('DD/MM/YYYY');
    return d.format('MMMM D, YYYY');
};

export const buildItemsRows = (items: any[], locale = 'vi-VN') => {
    return items
        .map(item => {
            const name = item?.name || '';
            const qty = Number(item?.quantity || 0);
            const unitPrice = formatCurrency(item?.unitPrice, locale);
            const vat = item?.vat ? `${item.vat}%` : '';
            const lineTotal = formatCurrency(item?.totalAfterVat, locale);
            return `<tr><td class="px-6 py-4"><p style="font-weight:600;color:#0f172a">${name}</p></td><td class="center">${qty}</td><td class="right">${unitPrice}</td><td class="right">${vat}</td><td class="right" style="font-weight:600;color:#0f172a">${lineTotal}</td></tr>`;
        })
        .join('');
};
