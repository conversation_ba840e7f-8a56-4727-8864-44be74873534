import { Injectable } from '@nestjs/common';
import { Between, ILike, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSContract } from '~/common/enums/contract.enum';
import { CustomerRepo, MemberRepo, QuoteRepo } from '~/domains/primary';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { clientSessionContext } from '../client-session.context';
import { CommunesService } from '../communes/communes.service';
import { MemberService } from '../member/member.service';
import { ProvincesService } from '../provinces/provinces.service';
import { ListRevenueReportDto } from './dto/revenue-report.dto';

@Injectable()
export class ReportRevenueService {
    constructor(
        private readonly memberRepo: MemberRepo,
        private readonly customerRepo: CustomerRepo,
        private readonly quoteRepo: QuoteRepo,
        private readonly contractRepo: ContractRepo,
        private readonly provinceService: ProvincesService,
        private readonly communeService: CommunesService,
        private readonly memberService: MemberService,
    ) {}

    async listRevenueReport(query: ListRevenueReportDto) {
        const {
            customerCode,
            customerName,
            taxCode,
            phone,
            address,
            revenueFrom,
            revenueTo,
            dateFrom,
            dateTo,
        } = query;
        const { tenantId } = clientSessionContext;

        // Xây dựng điều kiện lọc cho customers
        let customerWhere: any = { tenantId };
        if (customerCode) {
            customerWhere.code = ILike(`%${customerCode}%`);
        }
        if (customerName) {
            customerWhere.name = ILike(`%${customerName}%`);
        }
        if (taxCode) {
            customerWhere.taxNumber = ILike(`%${taxCode}%`);
        }
        if (phone) {
            customerWhere.phone = ILike(`%${phone}%`);
        }
        if (address) {
            customerWhere.address = ILike(`%${address}%`);
        }

        // điều kiện để lấy ra Tổng doanh số trong khoảng thời gian của các hợp đồng có trạng thái Đã Ký
        let contractWhere: any = { tenantId, status: NSContract.EStatus.SIGNED };
        if (dateFrom) {
            contractWhere.createdDate = MoreThanOrEqual(new Date(dateFrom));
        }
        if (dateTo) {
            contractWhere.createdDate = LessThanOrEqual(new Date(dateTo));
        }

        if (dateFrom && dateTo) {
            contractWhere.createdDate = Between(new Date(dateFrom), new Date(dateTo));
        }

        const [customers, quotes, contracts] = await Promise.all([
            this.customerRepo.find({
                where: { ...customerWhere, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            }),
            this.quoteRepo.find({
                where: {
                    tenantId,
                },
            }),
            this.contractRepo.find({
                where: { ...contractWhere, tenantId },
                order: {
                    signingDate: 'DESC',
                },
            }),
        ]);
        try {
            let res = customers.map(customer => {
                const customerContracts = contracts.filter(item => item.customerId === customer.id);
                const totalContract = customerContracts.length;
                const totalRevenue = customerContracts.reduce((acc, contract) => {
                    const quote = quotes.find(item => item.id === contract.quoteId);
                    return acc + Number(quote?.totalAmount || 0);
                }, 0);

                return {
                    customerCode: customer.code,
                    customerName: customer.name,
                    taxCode: customer.taxNumber,
                    phone: customer.phone,
                    totalRevenue,
                    totalContract,
                    address: customer.address,
                };
            });

            if (revenueFrom || revenueTo) {
                res = res.filter(item => {
                    if (revenueFrom && revenueTo) {
                        return (
                            item.totalRevenue >= Number(revenueFrom) &&
                            item.totalRevenue <= Number(revenueTo)
                        );
                    } else if (revenueFrom) {
                        return item.totalRevenue >= Number(revenueFrom);
                    } else if (revenueTo) {
                        return item.totalRevenue <= Number(revenueTo);
                    }
                    return true;
                });
            }

            res.sort((a, b) => b.totalRevenue - a.totalRevenue);

            return {
                data: res,
                total: res.length,
            };
        } catch (error) {
            console.error('Error generating revenue report:', error);
            throw new BusinessException(error.message);
        }
    }
}
