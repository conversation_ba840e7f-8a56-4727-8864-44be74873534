import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMedia } from '~/common/enums';

@Entity('media')
export class MediaEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'URL của file' })
    @Column({ type: 'text', nullable: false })
    url: string;

    @ApiPropertyOptional({ description: 'Tên file gốc' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    fileName?: string;

    @ApiPropertyOptional({ description: 'Content type (MIME type)' })
    @Column({ type: 'varchar', length: 100, nullable: true })
    contentType?: string;

    @ApiProperty({ description: 'Loại media', enum: NSMedia.EType })
    @Column({ type: 'varchar', length: 50, nullable: false })
    mediaType: NSMedia.EType;

    @ApiPropertyOptional({ description: '<PERSON><PERSON>ch thước file (bytes)' })
    @Column({ type: 'bigint', nullable: true })
    size?: number;

    @ApiPropertyOptional({ description: 'Nguồn của media (entity name)' })
    @Column({ type: 'varchar', length: 100, nullable: true })
    @Index()
    entity?: string;

    @ApiPropertyOptional({ description: 'ID của entity nguồn (nếu có)' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    entityId?: string;
}

