import { Injectable } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';

@Injectable()
export class SocketService {
    constructor(private readonly gateway: SocketGateway) {}

    emitToTenant(tenantId: string, event: string, payload: any) {
        this.gateway.server?.to(`tenant:${tenantId}`).emit(event, payload);
    }

    emitToMember(tenantId: string, memberId: string, event: string, payload: any) {
        this.gateway.server?.to(`tenant:${tenantId}:member:${memberId}`).emit(event, payload);
    }

    emitToMembers(tenantId: string, memberIds: string[], event: string, payload: any) {
        for (const mid of memberIds || []) {
            this.emitToMember(tenantId, mid, event, payload);
        }
    }

    emitToRole(tenantId: string, roleId: string, event: string, payload: any) {
        this.gateway.server?.to(`tenant:${tenantId}:role:${roleId}`).emit(event, payload);
    }

    emitToDepartment(tenantId: string, departmentId: string, event: string, payload: any) {
        this.gateway.server?.to(`tenant:${tenantId}:department:${departmentId}`).emit(event, payload);
    }
}
