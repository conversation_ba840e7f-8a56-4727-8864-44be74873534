import { Body, Query } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { ContactCareService } from './contact-care.service';
import {
    CreateCustomerContactDto,
    DeleteCustomerContactDto,
    DeleteListCustomerContactDto,
    ListCustomerContactDto,
    UpdateCustomerContactDto,
    UpdateStatusCustomerContactDto,
} from './dto/contact-care.dto';

@DefController('contact-care')
export class ContactCareController {
    constructor(private contactCareService: ContactCareService) {}

    @DefGet('list')
    async listCustomerContact(@Query() query: ListCustomerContactDto) {
        return this.contactCareService.listCustomerContact(query);
    }

    @DefGet('detail')
    async detailCustomerContact(@Query('id') id: string) {
        return this.contactCareService.detailCustomerContact({ id });
    }

    @DefPost('create')
    async createCustomerContact(@Body() body: CreateCustomerContactDto) {
        return this.contactCareService.createCustomerContact(body);
    }

    @DefPost('update')
    async updateCustomerContact(@Body() body: UpdateCustomerContactDto) {
        return this.contactCareService.updateCustomerContact(body);
    }

    @DefPost('update-status')
    async updateStatus(@Body() body: UpdateStatusCustomerContactDto) {
        return this.contactCareService.updateStatus(body);
    }

    @DefPost('delete')
    async deleteCustomerContact(@Body() body: DeleteCustomerContactDto) {
        return this.contactCareService.deleteCustomerContact(body);
    }

    @DefPost('delete-list')
    async deleteListCustomerContact(@Body() body: DeleteListCustomerContactDto) {
        return this.contactCareService.deleteListCustomerContact(body);
    }
}
