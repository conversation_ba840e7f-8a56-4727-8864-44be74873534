import { Body } from '@nestjs/common';
import { DefController, DefPost } from 'nestjs-typeorm3-kit';

import { MailSenderService } from './mail-sender.service';
import { SendMailOptionsDto } from './mail.types';

@DefController('mail')
export class MailController {
    constructor(private readonly mailSenderService: MailSenderService) {}

    @DefPost('send-test')
    async sendMail(@Body() options: SendMailOptionsDto) {
        return this.mailSenderService.sendMail(options);
    }

    // @DefPost('send-email')
    // async sendMailWithTemplate(@Body() options: SendMailWithTemplateOptionsDto) {
    //     return this.mailSenderService.sendMailWithTemplate(options);
    // }
}
