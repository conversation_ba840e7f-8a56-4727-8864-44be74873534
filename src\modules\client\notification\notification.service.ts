import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { In } from 'typeorm';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSNotification } from '~/common/enums/notification.enum';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { MemberRepo } from '~/domains/primary/member/member.repo';
import { NotificationReceiptEntity } from '~/domains/primary/notification-receipt/notification-receipt.entity';
import { NotificationReceiptRepo } from '~/domains/primary/notification-receipt/notification-receipt.repo';
import { NotificationEntity } from '~/domains/primary/notification/notification.entity';
import { NotificationRepo } from '~/domains/primary/notification/notification.repo';
import { RoleRepo } from '~/domains/primary/role/role.repo';
import { clientSessionContext } from '../client-session.context';
import { MailQueueService } from '../mail/mail-queue.service';
import { MailSenderService } from '../mail/mail-sender.service';
import { SocketService } from '../socket/socket.service';
import { ListMyReceiptsQuery, ListNotificationQuery } from './dto/notification.dto';

export interface CreateNotificationInput {
    type: NSNotification.EType;
    severity: NSNotification.ESeverity;
    title: string;
    content: string;
    source: NSNotification.ESource;
    tenantId?: string;
    createdBy?: string | null;
    resourceType?: string;
    resourceId?: string;
    scheduleDate?: Date;
    sendInApp?: boolean;
    sendEmail?: boolean;
    recipientType: NSNotification.ERecipientType;
    memberIds?: string[];
    roleIds?: string[];
    departmentIds?: string[];
    linkPath?: string;
    linkId?: string;
}

@Injectable()
export class NotificationService {
    constructor(
        @InjectRepo(NotificationRepo) private readonly notificationRepo: NotificationRepo,
        @InjectRepo(NotificationReceiptRepo) private readonly receiptRepo: NotificationReceiptRepo,
        @InjectRepo(MemberRepo) private readonly memberRepo: MemberRepo,
        @InjectRepo(RoleRepo) private readonly roleRepo: RoleRepo,
        @InjectRepo(DepartmentRepo) private readonly departmentRepo: DepartmentRepo,
        private readonly mailSender: MailSenderService,
        private readonly mailQueue: MailQueueService,
        private readonly socketService: SocketService,
    ) {}

    async createNotification(input: CreateNotificationInput) {
        const { tenantId: ctxTenantId, memberId: ctxMemberId } = clientSessionContext;
        const sendInApp = input.sendInApp ?? true;
        const sendEmail = input.sendEmail ?? false;
        const entity: Partial<NotificationEntity> = {
            tenantId: input.tenantId ?? ctxTenantId,
            type: input.type,
            severity: input.severity,
            title: input.title,
            content: input.content,
            source: input.source,
            scheduleDate: input.scheduleDate ? input.scheduleDate : new Date(),
            sendInApp,
            sendEmail,
            status: input.scheduleDate
                ? NSNotification.EStatus.QUEUED
                : NSNotification.EStatus.SENT,
            createdBy: input.createdBy ?? ctxMemberId,
            createdDate: new Date(),
            metadata:
                input.linkPath && input.linkId
                    ? { link: { path: input.linkPath, id: input.linkId } }
                    : undefined,
        };
        const saved = await this.notificationRepo.save(entity as any);
        const receipts = await this.expandRecipients(saved, input);
        const savedReceipts: NotificationReceiptEntity[] = [];
        if (receipts.length) {
            for (const r of receipts) {
                const sr = await this.receiptRepo.save(r as any);
                savedReceipts.push(sr as any);
            }
        }
        if (sendInApp) {
            const { tenantId } = clientSessionContext;
            await Promise.all(
                savedReceipts.map(r =>
                    this.receiptRepo.save({
                        id: r.id,
                        notificationId: saved.id!,
                        tenantId,
                        deliveryStatus: NSNotification.EDeliveryStatus.SENT,
                        deliveredAt: new Date(),
                    } as any),
                ),
            ).catch(() => {});
            const tenantAll = savedReceipts.some(
                r => r.recipientType === NSNotification.ERecipientType.TENANT_ALL,
            );
            const basePayload = {
                id: saved.id,
                type: saved.type,
                severity: saved.severity,
                title: saved.title,
                content: saved.content,
                source: saved.source,
                scheduleDate: saved.scheduleDate,
                metadata: saved.metadata,
            };
            if (tenantAll) {
                this.socketService.emitToTenant(tenantId, 'notification.created', basePayload);
            }
            const roleIds = Array.from(
                new Set(
                    savedReceipts
                        .filter(
                            r => r.recipientType === NSNotification.ERecipientType.ROLE && r.roleId,
                        )
                        .map(r => r.roleId as string),
                ),
            );
            for (const rid of roleIds) {
                this.socketService.emitToRole(tenantId, rid, 'notification.created', basePayload);
            }
            const depIds = Array.from(
                new Set(
                    savedReceipts
                        .filter(
                            r =>
                                r.recipientType === NSNotification.ERecipientType.DEPARTMENT &&
                                r.departmentId,
                        )
                        .map(r => r.departmentId as string),
                ),
            );
            for (const did of depIds) {
                this.socketService.emitToDepartment(
                    tenantId,
                    did,
                    'notification.created',
                    basePayload,
                );
            }
            const memberIds = Array.from(
                new Set(
                    savedReceipts
                        .filter(
                            r =>
                                r.recipientType === NSNotification.ERecipientType.MEMBER &&
                                r.memberId,
                        )
                        .map(r => r.memberId as string),
                ),
            );
            if (!tenantAll && roleIds.length === 0 && depIds.length === 0 && memberIds.length) {
                this.socketService.emitToMembers(
                    tenantId,
                    memberIds,
                    'notification.created',
                    basePayload,
                );
            }
        }
        return saved;
    }

    async listNotifications(filter?: ListNotificationQuery) {
        const { tenantId } = clientSessionContext;
        const where: any = {};
        if (filter?.status) where.status = filter.status;
        if (filter?.type) where.type = filter.type;
        if (filter?.severity) where.severity = filter.severity;
        if (filter?.source) where.source = filter.source;

        const res = await this.notificationRepo.findPaginationByTenant(
            {
                where,
                order: { createdDate: 'DESC' },
            },
            {
                pageIndex: Number(filter?.pageIndex),
                pageSize: Number(filter?.pageSize),
            },
        );
        const createdByIds = Array.from(
            new Set((res.data || []).map(n => n.createdBy).filter(Boolean) as string[]),
        );
        const members = await this.memberRepo.find({ where: { tenantId } });
        let mapAvatar: Record<string, string> = {};
        if (createdByIds.length) {
            members.forEach(m => {
                if (createdByIds.includes(m.id)) mapAvatar[m.id] = m.avatar;
            });
        }

        return {
            data: (res.data || []).map(n => ({
                ...n,
                createdByAvatar: mapAvatar[n.createdBy],
                createdByName: members.find(m => m.id === n.createdBy)?.fullName,
            })),
            total: res.total || 0,
        };
    }

    async listReceipts(
        page: PageRequest,
        filter?: {
            notificationId?: string;
            isRead?: boolean;
            deliveryStatus?: NSNotification.EDeliveryStatus;
            memberId?: string;
        },
    ) {
        const { tenantId } = clientSessionContext;
        const where: any = {};
        if (filter?.notificationId) where.notificationId = filter.notificationId;
        if (filter?.memberId) where.memberId = filter.memberId;
        if (typeof filter?.isRead === 'boolean') where.isRead = filter.isRead;
        if (filter?.deliveryStatus) where.deliveryStatus = filter.deliveryStatus;
        const options = { order: { createdDate: 'DESC' as const }, where: { ...where, tenantId } };
        return this.receiptRepo.findPaginationByTenant(options as any, page);
    }

    async listMyReceipts(filter?: ListMyReceiptsQuery) {
        const { memberId, tenantId } = clientSessionContext;
        const where: any = { memberId };
        if (filter?.isRead) where.isRead = filter.isRead;
        if (filter?.deliveryStatus) where.deliveryStatus = filter.deliveryStatus;

        const page = await this.receiptRepo.findPaginationByTenant(
            {
                where: { ...where, tenantId },
                order: { createdDate: 'DESC' },
            },
            {
                pageIndex: Number(filter?.pageIndex),
                pageSize: Number(filter?.pageSize),
            },
        );

        const notificationIds = Array.from(
            new Set((page.data || []).map(r => r.notificationId).filter(Boolean) as string[]),
        );
        let notifications: NotificationEntity[] = [] as any;
        if (notificationIds.length) {
            notifications = await this.notificationRepo.find({
                where: { tenantId, id: In(notificationIds) } as any,
            });
        }
        const createdByIds = Array.from(
            new Set((notifications || []).map(n => n.createdBy).filter(Boolean) as string[]),
        );
        const members = await this.memberRepo.find({ where: { tenantId } });
        const mapAvatar: Record<string, string> = {};
        if (createdByIds.length) {
            members.forEach(m => {
                if (createdByIds.includes(m.id)) mapAvatar[m.id] = m.avatar;
            });
        }
        const mapNoti = Object.fromEntries((notifications || []).map(n => [n.id, n]));
        return {
            data: (page.data || []).map(r => {
                const n = mapNoti[r.notificationId] as NotificationEntity | undefined;
                return {
                    ...r,
                    notificationTitle: n?.title,
                    notificationContent: n?.content,
                    notificationCreatedDate: n?.createdDate,
                    notificationSeverity: n?.severity,
                    notificationSource: n?.source,
                    notificationType: n?.type,
                    createdByAvatar: n?.createdBy ? mapAvatar[n.createdBy] : undefined,
                    metadata: n?.metadata,
                } as any;
            }),
            total: page.total || 0,
        };
    }

    async markReadAll() {
        const { tenantId, memberId } = clientSessionContext;
        if (!tenantId || !memberId) return { updated: 0 };
        const unread = await this.receiptRepo.find({
            where: { tenantId, memberId, isRead: false },
            order: { createdDate: 'DESC' },
        });
        if (!unread.length) return { updated: 0 };
        const readAt = new Date();
        for (const r of unread) {
            await this.receiptRepo.save({ id: r.id, isRead: true, readAt } as any);
            this.socketService.emitToMember(tenantId, memberId, 'notification.updated', {
                receiptId: r.id,
                notificationId: r.notificationId,
                isRead: true,
                readAt,
            });
        }
        return { updated: unread.length };
    }

    private async expandRecipients(
        notification: NotificationEntity,
        input: CreateNotificationInput,
    ) {
        const { tenantId: ctxTenantId } = clientSessionContext;
        const result: Partial<NotificationReceiptEntity>[] = [];
        if (input.recipientType === NSNotification.ERecipientType.MEMBER) {
            const allMembers = await this.memberRepo.find({ where: { tenantId: ctxTenantId } });
            const validIds = new Set(
                (input.memberIds || []).filter(id => allMembers.some(m => m.id === id)),
            );
            for (const mid of validIds) {
                result.push({
                    tenantId: notification.tenantId ?? ctxTenantId,
                    notificationId: notification.id!,
                    recipientType: NSNotification.ERecipientType.MEMBER,
                    memberId: mid,
                    deliveryStatus: NSNotification.EDeliveryStatus.PENDING,
                    isRead: false,
                    createdDate: new Date(),
                });
            }
        }
        if (input.recipientType === NSNotification.ERecipientType.ROLE) {
            const validRoles = await this.roleRepo.find({ where: { tenantId: ctxTenantId } });
            const roleSet = new Set(
                (input.roleIds || []).filter(id => validRoles.some(r => r.id === id)),
            );
            const allMembers = await this.memberRepo.find({ where: { tenantId: ctxTenantId } });
            for (const rid of roleSet) {
                allMembers
                    .filter(m => Array.isArray(m.roleIds) && m.roleIds.includes(rid))
                    .forEach(m =>
                        result.push({
                            tenantId: notification.tenantId ?? ctxTenantId,
                            notificationId: notification.id!,
                            recipientType: NSNotification.ERecipientType.ROLE,
                            roleId: rid,
                            memberId: m.id!,
                            deliveryStatus: NSNotification.EDeliveryStatus.PENDING,
                            isRead: false,
                            createdDate: new Date(),
                        }),
                    );
            }
        }
        if (input.recipientType === NSNotification.ERecipientType.DEPARTMENT) {
            const validDeps = await this.departmentRepo.find({ where: { tenantId: ctxTenantId } });
            const depSet = new Set(
                (input.departmentIds || []).filter(id => validDeps.some(d => d.id === id)),
            );
            const allMembers = await this.memberRepo.find({ where: { tenantId: ctxTenantId } });
            for (const did of depSet) {
                allMembers
                    .filter(m => m.departmentId === did)
                    .forEach(m =>
                        result.push({
                            tenantId: notification.tenantId ?? ctxTenantId,
                            notificationId: notification.id!,
                            recipientType: NSNotification.ERecipientType.DEPARTMENT,
                            departmentId: did,
                            memberId: m.id!,
                            deliveryStatus: NSNotification.EDeliveryStatus.PENDING,
                            isRead: false,
                            createdDate: new Date(),
                        }),
                    );
            }
        }
        if (input.recipientType === NSNotification.ERecipientType.TENANT_ALL) {
            const allMembers = await this.memberRepo.find({ where: { tenantId: ctxTenantId } });
            for (const m of allMembers) {
                result.push({
                    tenantId: notification.tenantId ?? ctxTenantId,
                    notificationId: notification.id!,
                    recipientType: NSNotification.ERecipientType.TENANT_ALL,
                    memberId: m.id!,
                    deliveryStatus: NSNotification.EDeliveryStatus.PENDING,
                    isRead: false,
                    createdDate: new Date(),
                });
            }
        }
        return result;
    }

    async processDueNotificationsForTenant(tenantId: string) {
        const now = new Date();
        const list = await this.notificationRepo.find({
            where: { tenantId, status: NSNotification.EStatus.QUEUED },
        });
        for (const n of list.filter(x => !x.scheduleDate || x.scheduleDate <= now)) {
            await this.notificationRepo.save({
                id: n.id,
                status: NSNotification.EStatus.SENDING,
            } as any);
            const receipts = await this.receiptRepo.find({
                where: { tenantId, notificationId: n.id },
            });
            const basePayload = {
                id: n.id,
                type: n.type,
                severity: n.severity,
                title: n.title,
                content: n.content,
                source: n.source,
                scheduleDate: n.scheduleDate,
                metadata: n.metadata,
            };
            for (const r of receipts) {
                try {
                    if (n.sendInApp) {
                        await this.receiptRepo.save({
                            id: r.id,
                            deliveryStatus: NSNotification.EDeliveryStatus.SENT,
                            deliveredAt: new Date(),
                        } as any);
                    }
                    if (n.sendEmail && r.memberId) {
                        const member = await this.memberRepo.findOne({
                            where: { id: r.memberId, tenantId },
                        });
                        const to = member?.username || '';
                        if (to) {
                            this.mailQueue.enqueue({
                                to,
                                subject: n.title,
                                text: n.content,
                                from: undefined,
                            } as any);
                        }
                    }
                } catch (err: any) {
                    await this.receiptRepo.save({
                        id: r.id,
                        deliveryStatus: NSNotification.EDeliveryStatus.FAILED,
                        lastError: err?.message || 'ERROR',
                    } as any);
                }
            }
            if (n.sendInApp) {
                const tenantAll = receipts.some(
                    r => r.recipientType === NSNotification.ERecipientType.TENANT_ALL,
                );
                if (tenantAll) {
                    this.socketService.emitToTenant(tenantId, 'notification.created', basePayload);
                }
                const roleIds = Array.from(
                    new Set(
                        receipts
                            .filter(
                                r =>
                                    r.recipientType === NSNotification.ERecipientType.ROLE &&
                                    r.roleId,
                            )
                            .map(r => r.roleId as string),
                    ),
                );
                for (const rid of roleIds) {
                    this.socketService.emitToRole(
                        tenantId,
                        rid,
                        'notification.created',
                        basePayload,
                    );
                }
                const depIds = Array.from(
                    new Set(
                        receipts
                            .filter(
                                r =>
                                    r.recipientType === NSNotification.ERecipientType.DEPARTMENT &&
                                    r.departmentId,
                            )
                            .map(r => r.departmentId as string),
                    ),
                );
                for (const did of depIds) {
                    this.socketService.emitToDepartment(
                        tenantId,
                        did,
                        'notification.created',
                        basePayload,
                    );
                }
                const memberIds = Array.from(
                    new Set(
                        receipts
                            .filter(
                                r =>
                                    r.recipientType === NSNotification.ERecipientType.MEMBER &&
                                    r.memberId,
                            )
                            .map(r => r.memberId as string),
                    ),
                );
                if (!tenantAll && roleIds.length === 0 && depIds.length === 0) {
                    this.socketService.emitToMembers(
                        tenantId,
                        memberIds,
                        'notification.created',
                        basePayload,
                    );
                }
            }
            await this.notificationRepo.save({
                id: n.id,
                status: NSNotification.EStatus.SENT,
            } as any);
        }
    }

    async processDueNotifications() {
        const { tenantId } = clientSessionContext;
        if (!tenantId) return;
        return this.processDueNotificationsForTenant(tenantId);
    }

    async markRead(receiptId: string) {
        const { tenantId } = clientSessionContext;
        const receipt = await this.receiptRepo.findOne({ where: { id: receiptId, tenantId } });
        if (!receipt) return;
        const readAt = new Date();
        await this.receiptRepo.save({ id: receiptId, isRead: true, readAt } as any);
        if (receipt.memberId) {
            this.socketService.emitToMember(tenantId, receipt.memberId, 'notification.updated', {
                receiptId,
                notificationId: receipt.notificationId,
                isRead: true,
                readAt,
            });
        }
    }
}
