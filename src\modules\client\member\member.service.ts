import { Injectable } from '@nestjs/common';
import { Between, ILike, In } from 'typeorm';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import { apeAuthApiConnector } from '~/connectors/api.connector';
import { MemberRepo, RoleRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';

import { DefTransaction } from 'nestjs-typeorm3-kit';
import { NSMember, NSRole } from '~/common/enums';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { PositionRepo } from '~/domains/primary/position/position.repo';
import { DepartmentService } from '../department/department.service';
import { ChangePasswordReq } from '../member-auth/dto';
import { PositionService } from '../position/position.service';
import {
    AssignRoleDto,
    CreateMemberDto,
    MemberPaginationDto,
    UpdateMemberDto,
    UpdateStatusDto,
} from './dto/member.dto';

const { APE_CLIENT_ID } = configEnv();
@Injectable()
export class MemberService {
    constructor(
        private readonly memberRepo: MemberRepo,
        private readonly roleRepo: RoleRepo,
        private readonly departmentService: DepartmentService,
        private readonly positionService: PositionService,
        private readonly positionRepo: PositionRepo,
        private readonly departmentRepo: DepartmentRepo,
    ) {}

    async pagination(params: MemberPaginationDto) {
        try {
            const { pageSize, pageIndex, ...rest } = params;
            const { tenantId } = clientSessionContext;
            let where: any = {};

            if (rest.status) {
                where.status = rest.status;
            }
            if (rest.fullName) {
                where.fullName = ILike(`%${rest.fullName}%`);
            }
            if (rest.departmentId) {
                where.departmentId = rest.departmentId;
            }
            if (rest.positionId) {
                where.positionId = rest.positionId;
            }
            if (rest.dateFrom && rest.dateTo) {
                where.createdDate = Between(rest.dateFrom, rest.dateTo);
            }

            const departments = await this.departmentService.list({ pageIndex: 1, pageSize: -1 });

            const positions = await this.positionService.pagination({ pageIndex: 1, pageSize: -1 });

            let res: any = {};
            if (pageSize === -1) {
                const [data, total] = await this.memberRepo.findAndCount({
                    where: {
                        ...where,
                        tenantId,
                    },
                    order: {
                        createdDate: 'DESC',
                    },
                });
                res = {
                    data,
                    total,
                };
            } else {
                const { data, total } = await this.memberRepo.findPaginationByTenant(
                    {
                        where: {
                            ...where,
                            tenantId,
                        },
                        order: {
                            createdDate: 'DESC',
                        },
                    },
                    {
                        pageSize,
                        pageIndex,
                    },
                );
                res = {
                    data,
                    total,
                };
            }

            const reMapData = res.data.map((item: any) => ({
                ...item,

                departmentName: departments?.data?.find(d => d.id === item.departmentId)?.name,
                positionName: positions?.data?.find(p => p.id === item.positionId)?.name,
            }));

            return {
                data: reMapData,
                total: res.total,
            };
        } catch (error) {
            throw new BusinessException(error);
        }
    }

    async getMemberByIds(body: string[]) {
        try {
            const { tenantId } = clientSessionContext;
            const departments = await this.departmentService.list({ pageIndex: 1, pageSize: -1 });
            const positions = await this.positionService.pagination({ pageIndex: 1, pageSize: -1 });
            console.log(body);
            const res = await this.memberRepo.find({
                where: {
                    id: In(body),
                    tenantId,
                },
            });
            return res.map(item => ({
                ...item,
                departmentName: departments?.data?.find(d => d.id === item.departmentId)?.name,
                positionName: positions?.data?.find(p => p.id === item.positionId)?.name,
            }));
        } catch (error) {
            throw new BusinessException(error);
        }
    }

    /**
     * Một member có nhiều role
     * @param body
     * @returns
     */
    async assignRoleMember(body: AssignRoleDto) {
        try {
            const { memberId, roleIds } = body;
            // Kiểm tra member Id
            const member = await this.memberRepo.findOneByTenant({ where: { id: memberId } });
            if (!member) {
                throw new BusinessException('Member not found');
            }

            // Kiểm tra role Id
            const role = await this.roleRepo.findPaginationByTenant(
                {
                    where: {
                        id: In(roleIds),
                        status: NSRole.EStatus.ACTIVE,
                    },
                },
                { pageSize: -1, pageIndex: 1 },
            );
            if (!role || role.data.length === 0) {
                throw new BusinessException('Role not found');
            }

            // Cập nhật role cho member
            await this.memberRepo.update(
                {
                    id: memberId,
                },
                {
                    roleIds,
                },
            );

            return member;
        } catch (error) {
            throw new BusinessException(error);
        }
    }

    @DefTransaction()
    async create(body: CreateMemberDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;
            let authRes;
            if (body.type === NSMember.EType.TENANT_USER) {
                try {
                    const position = await this.positionRepo.findOneByTenant({
                        where: {
                            id: body.positionId,
                            tenantId,
                        },
                    });
                    if (!position && body.positionId) {
                        throw new BusinessException('Position not found');
                    }
                    const department = await this.departmentRepo.findOneByTenant({
                        where: {
                            id: body.departmentId,
                            tenantId,
                        },
                    });
                    if (!department && body.departmentId) {
                        throw new BusinessException('Department not found');
                    }
                    const positionAuthId = position?.positionAuthId;

                    authRes = await apeAuthApiConnector.post('/api/public/crm/member/create', {
                        tenantId,
                        clientId: APE_CLIENT_ID,
                        departmentId: department?.departmentAuthId,
                        positionId: positionAuthId,
                        username: body.username,
                        password: body.password,
                        fullName: body.fullName,
                        type: body.type,
                    });
                } catch (err) {
                    throw new BusinessException(
                        'Lỗi khi gọi Auth API tạo member: ' + (err?.message || err),
                    );
                }
                if (authRes) {
                    await this.memberRepo.save({
                        username: authRes.username,
                        avatar: authRes.avatar,
                        fullName: authRes.fullName,
                        type: authRes.type,
                        departmentId: authRes.departmentId,
                        positionId: body.positionId,
                        status: authRes.status,
                        roleIds: body.roleIds,
                        tenantId,
                        createdBy: memberId,
                        createdDate: new Date(),
                    });
                }
            } else {
                try {
                    authRes = await apeAuthApiConnector.post('/api/public/crm/member/create', {
                        tenantId,
                        clientId: APE_CLIENT_ID,
                        username: body.username,
                        password: body.password,
                        fullName: body.fullName,
                        type: body.type,
                    });
                } catch (err) {
                    throw new BusinessException(
                        'Lỗi khi gọi Auth API tạo member: ' + (err?.message || err),
                    );
                }
                if (authRes) {
                    await this.memberRepo.save({
                        username: authRes.username,
                        avatar: authRes.avatar,
                        fullName: authRes.fullName,
                        type: authRes.type,
                        status: authRes.status,
                        roleIds: body.roleIds,
                        tenantId,
                        createdBy: memberId,
                        createdDate: new Date(),
                    });
                }
            }
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }

    async update(body: UpdateMemberDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;
            const { positionId, departmentId, ...rest } = body;
            // 1️⃣ Kiểm tra member có tồn tại trong tenant
            const member = await this.memberRepo.findOne({
                where: { id: body.id, tenantId },
            });
            if (!member) {
                throw new BusinessException(
                    `Member ${body.id} không tồn tại trong tenant hiện tại`,
                );
            }
            const department = await this.departmentRepo.findOneByTenant({
                where: {
                    id: departmentId,
                    tenantId,
                },
            });
            if (!department && body.type === NSMember.EType.TENANT_USER && departmentId) {
                throw new BusinessException('Department not found');
            }

            const position = await this.positionRepo.findOneByTenant({
                where: {
                    id: positionId,
                    tenantId,
                },
            });
            if (!position && body.type === NSMember.EType.TENANT_USER && positionId) {
                throw new BusinessException('Position not found');
            }

            // 2️⃣ Gọi API Auth để update thông tin bên Auth Server
            const authRes = await apeAuthApiConnector.post('/api/public/crm/member/update', {
                tenantId,
                clientId: APE_CLIENT_ID,
                departmentId: body.departmentId ? department?.departmentAuthId : undefined,
                positionId: body.positionId ? position?.positionAuthId : undefined,
                ...rest,
            });

            if (!authRes) {
                throw new BusinessException('Cập nhật bên Auth thất bại hoặc không trả dữ liệu');
            }

            // 4️⃣ Cập nhật entity
            member.username = authRes.username;
            member.avatar = authRes.avatar;
            member.fullName = authRes.fullName;
            member.type = authRes.type;
            member.departmentId = authRes.departmentId;
            member.positionId = authRes.positionId;
            member.status = authRes.status;
            member.updatedBy = memberId;
            member.updatedDate = new Date();
            member.roleIds = body.roleIds;

            // 5️⃣ Lưu lại
            await this.memberRepo.update(member.id, { ...member, id: member.id });

            return member;
        } catch (error) {
            throw new BusinessException(error);
        }
    }

    async setActive(body: UpdateStatusDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;
            await apeAuthApiConnector.post('/api/public/crm/member/set-active', {
                tenantId,
                username: body.username,
                status: body.status,
            });
            await this.memberRepo.update(
                {
                    id: body.memberId,
                },
                {
                    status: body.status,
                    updatedBy: memberId,
                    updatedDate: new Date(),
                    id: body.memberId,
                },
            );
        } catch (error) {
            throw new BusinessException(error);
        }
    }

    async changePassword(data: ChangePasswordReq) {
        try {
            const { ssoAccountId } = clientSessionContext;
            if (!ssoAccountId) {
                throw new BusinessException('Không tìm thấy thông tin người dùng');
            }

            await apeAuthApiConnector.post(`/api/public/crm/member/change-password`, {
                id: ssoAccountId,
                oldPassword: data.oldPassword,
                newPassword: data.newPassword,
            });
        } catch (error) {
            throw new BusinessException(
                error.response?.data?.message || 'Cập nhật mật khẩu thất bại',
            );
        }
    }
}
