export namespace NSKPI {
    export enum EPeriodType {
        DAY = 'DAY',
        WEEK = 'WEEK',
        MONTH = 'MONTH',
        QUARTER = 'QUARTER',
        YEAR = 'YEAR',
        OTHER = 'OTHER',
    }

    export enum ESubjectType {
        MEMBER = 'MEMBER',
        DEPARTMENT = 'DEPARTMENT',
        ROLE = 'ROLE',
    }

    export enum ECategoryKey {
        CUSTOMERS_COUNT = 'CUSTOMERS_COUNT',
        CONTRACTS_COUNT = 'CONTRACTS_COUNT',
    }

    export enum ECategoryStatus {
        ACTIVE = 'ACTIVE',
        INACTIVE = 'INACTIVE',
    }

    export enum ETargetStatus {
        NEW = 'NEW',
        SENT = 'SENT',
        APPROVED = 'APPROVED',
        REJECTED = 'REJECTED',
        EXPIRED = 'EXPIRED',
    }

    export const EDefaultCategories: Record<ECategoryKey, { label: string; unit?: string }> = {
        [ECategoryKey.CUSTOMERS_COUNT]: {
            label: 'Số lượng khách hàng',
            unit: 'Khách',
        },
        [ECategoryKey.CONTRACTS_COUNT]: {
            label: 'Số lượng hợp đồng',
            unit: 'Hợp đồng',
        },
    };
}
