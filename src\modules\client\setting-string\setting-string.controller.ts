import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { SettingStringUpdateDto } from './dto/setting-string.dto';
import { SettingStringService } from './setting-string.service';

@UseGuards(PermissionGuard)
@DefController('setting-strings')
export class SettingStringController {
    constructor(private readonly settingStringService: SettingStringService) {}

    @DefPost('get-default-config')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    async getDefaultConfig(@Query('type') type: 'string' | 'option') {
        return this.settingStringService.getTenantDefaultConfig(type ?? 'string');
    }

    @DefGet('default-value/:key')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    async getDefaultValueByKey(
        @Query('type') type: 'string' | 'option',
        @Param('key') key: string,
    ) {
        return this.settingStringService.getDefaultValueByKey(type ?? 'string', key);
    }

    @DefGet('default-values')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    async getDefaultValuesByKeys(
        @Query('type') type: 'string' | 'option',
        @Query('keys') keys: string,
    ) {
        const keysArray = keys ? keys.split(',') : [];
        return this.settingStringService.getDefaultValuesByKeys(type ?? 'string', keysArray);
    }

    @DefPost('save-default-config')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.UPDATE])
    async saveTenantDefaultConfig(@Body() dto: SettingStringUpdateDto) {
        return this.settingStringService.saveTenantDefaultConfig(dto.type ?? 'string', dto.config);
    }
}
