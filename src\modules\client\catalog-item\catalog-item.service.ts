import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike } from 'typeorm';
import * as XLSX from 'xlsx';
import { BusinessException } from '~/@systems/exceptions';
import { NSCatalog } from '~/common/enums';
import { CatalogItemRepo, CatalogRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';
import { UploadService } from '../upload/upload.service';
import {
    CreateCatalogItemDto,
    ListCatalogItemDto,
    UpdateCatalogItemDto,
} from './dto/catalog-item.dto';

@Injectable()
export class CatalogService {
    constructor(
        @InjectRepo(CatalogItemRepo) private catalogItemRepo: CatalogItemRepo,
        @InjectRepo(CatalogRepo) private catalogRepo: CatalogRepo,
        private readonly uploadService: UploadService,
    ) {}

    async listCatalog(params: ListCatalogItemDto) {
        const where: any = {};
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }

        if (params.catalogId) {
            where.catalogId = params.catalogId;
        }

        if (params.type) {
            where.type = params.type;
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.createdFrom && params.createdTo) {
            where.createdDate = Between(params.createdFrom, params.createdTo);
        }
        const { tenantId } = clientSessionContext;
        const catalogs = await this.catalogRepo.find({
            where: {
                tenantId,
            },
        });

        const res = await this.catalogItemRepo.findPaginationByTenant(
            {
                where,
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex: params.pageIndex,
                pageSize: params.pageSize,
            },
        );
        const reMapRes = res.data.map(item => ({
            ...item,
            catalogName: catalogs.find(cat => cat.id === item.catalogId)?.name,
        }));
        return {
            data: reMapRes,
            total: res.total,
        };
    }

    async detailCatalog(id: string) {
        const check = await this.catalogItemRepo.findOneByTenant({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        return check;
    }

    @DefTransaction()
    async createCatalog(catalog: CreateCatalogItemDto, file?: Express.Multer.File) {
        let imageUrl: string | undefined;
        if (file && file.size > 0) {
            try {
                const uploaded = await this.uploadService.uploadSingle(file);
                imageUrl = uploaded?.fileUrl;
            } catch (error) {
                throw new BusinessException('Lỗi upload ảnh');
            }
        }
        return this.catalogItemRepo.save({ ...catalog, imageUrl });
    }

    @DefTransaction()
    async updateCatalog(catalog: UpdateCatalogItemDto, file?: Express.Multer.File) {
        const { id, ...catalogData } = catalog;
        const check = await this.catalogItemRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        let imageUrl: string | undefined;
        if (file && file.size > 0) {
            try {
                const uploaded = await this.uploadService.uploadSingle(file);
                imageUrl = uploaded?.fileUrl;
            } catch (error) {
                throw new BusinessException('Lỗi upload ảnh');
            }
        }
        const updatedCatalog = await this.catalogItemRepo.update(id, {
            ...catalogData,
            ...(imageUrl ? { imageUrl } : {}),
            id,
        });
        return updatedCatalog;
    }

    //Active Catalog
    @DefTransaction()
    async activeCatalog(id: string) {
        const check = await this.catalogItemRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogItemRepo.update(id, {
            status: NSCatalog.EStatus.ACTIVE,
            id,
        });
        return updatedCatalog;
    }

    //Inactive Catalog
    @DefTransaction()
    async inActiveCatalog(id: string) {
        const check = await this.catalogItemRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogItemRepo.update(id, {
            status: NSCatalog.EStatus.INACTIVE,
            id,
        });
        return updatedCatalog;
    }

    // Select Box
    async selectBox(pageSize: number) {
        const data = await this.catalogItemRepo.findPaginationByTenant(
            { where: { status: NSCatalog.EStatus.ACTIVE } },
            { pageIndex: 1, pageSize },
        );
        return data;
    }

    @DefTransaction()
    async uploadCatalogItem(file: Express.Multer.File) {
        let catalogErrorItem = [];

        try {
            if (!file) {
                throw new BusinessException('Không có file upload');
            }

            let workbook: XLSX.WorkBook;

            // Đọc từ bộ nhớ (multer.memoryStorage)
            if (file.buffer && file.buffer.length > 0) {
                workbook = XLSX.read(file.buffer, { type: 'buffer' });
            }
            // Hoặc đọc từ disk (multer.diskStorage)
            else if ((file as any).path) {
                const fs = await import('fs');
                const data = fs.readFileSync((file as any).path);
                workbook = XLSX.read(data, { type: 'buffer' });
            } else {
                throw new BusinessException('Không đọc được nội dung file');
            }

            if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                throw new BusinessException('File Excel không có sheet');
            }

            // Hàm chuẩn hóa tên sheet
            const normalize = (s: string) => s?.normalize('NFC').trim().toLowerCase() ?? '';

            // Tìm sheet theo tên, fallback về sheet đầu tiên nếu không tìm thấy
            const findSheet = (expected: string) => {
                const idx = workbook.SheetNames.findIndex(
                    n => normalize(n) === normalize(expected),
                );
                return idx >= 0 ? workbook.Sheets[workbook.SheetNames[idx]] : undefined;
            };

            const catalogItemWorksheet =
                findSheet('Sản phẩm') ?? workbook.Sheets[workbook.SheetNames[0]];
            if (!catalogItemWorksheet) {
                throw new BusinessException('Không tìm thấy sheet "Sản phẩm"');
            }

            // Đọc dữ liệu từ sheet theo dạng mảng (header: 1)
            const rows = XLSX.utils.sheet_to_json<any[]>(catalogItemWorksheet, {
                header: 1,
                defval: '',
            }) as any[][];

            if (!rows || rows.length === 0) {
                throw new BusinessException('Sheet "Sản phẩm" không có dữ liệu');
            }

            // Định nghĩa tiêu đề cột bắt buộc
            const expectedHeaders = [
                'Mã Danh Mục (*)',
                'Mã sản phẩm (*)',
                'Tên sản phẩm (*)',
                'Đơn vị tính',
            ];

            // Kiểm tra tiêu đề cột
            const headerRow = (rows[0] || []).map(h => (typeof h === 'string' ? h.trim() : h));
            const headerIndexMap: Record<string, number> = {};

            for (const h of expectedHeaders) {
                let idx = headerRow.indexOf(h);
                if (idx === -1) {
                    // Thử khớp không phân biệt hoa/thường
                    idx = headerRow.findIndex(
                        x => String(x).trim().toLowerCase() === h.toLowerCase(),
                    );
                }
                if (idx === -1) {
                    throw new BusinessException(`Thiếu cột bắt buộc: ${h}`);
                }
                headerIndexMap[h] = idx;
            }

            const optionalHeaders = ['Đơn giá', 'Mô tả'];
            for (const h of optionalHeaders) {
                let idx = headerRow.indexOf(h);
                if (idx === -1) {
                    idx = headerRow.findIndex(
                        x => String(x).trim().toLowerCase() === h.toLowerCase(),
                    );
                }
                if (idx !== -1) {
                    headerIndexMap[h] = idx;
                }
            }

            // Hàm chuyển đổi giá trị sang số
            const toNumber = (val: any) => {
                if (val === null || val === undefined || val === '') return undefined;
                const cleaned = String(val).replace(/[^\d.-]/g, '');
                const n = Number(cleaned);
                return Number.isFinite(n) ? n : undefined;
            };

            //  kiểm tra trung lặp catalogCode và code
            const [catalog, catalogItem] = await Promise.all([
                this.catalogRepo.find({
                    where: {
                        tenantId: clientSessionContext.tenantId,
                    },
                }),
                this.catalogItemRepo.find({
                    where: {
                        tenantId: clientSessionContext.tenantId,
                    },
                }),
            ]);

            // Xử lý dữ liệu từ các dòng (bỏ dòng tiêu đề)
            const dataRows = rows.slice(1);
            const data = dataRows
                .filter(row => row && row.some(cell => String(cell ?? '').trim().length > 0))
                .map((row, i) => {
                    const catalogCode = String(row[headerIndexMap['Mã Danh Mục (*)']] ?? '').trim();
                    const code = String(row[headerIndexMap['Mã sản phẩm (*)']] ?? '').trim();
                    const name = String(row[headerIndexMap['Tên sản phẩm (*)']] ?? '').trim();
                    const unit = String(row[headerIndexMap['Đơn vị tính']] ?? '').trim();
                    const price =
                        headerIndexMap['Đơn giá'] !== undefined
                            ? toNumber(row[headerIndexMap['Đơn giá']])
                            : 0;
                    const description =
                        headerIndexMap['Mô tả'] !== undefined
                            ? String(row[headerIndexMap['Mô tả']] ?? '').trim()
                            : '';
                    const catalogId = catalog.find(c => c.code === catalogCode)?.id;
                    if (!catalogId) {
                        catalogErrorItem.push({
                            rowNumber: i + 2, // Số dòng trong Excel, +1 do bỏ header
                            message: `Mã danh mục ${catalogCode} không tồn tại`,
                        });
                        return null; // Bỏ qua dòng không hợp lệ
                    }
                    // Kiểm tra các cột bắt buộc
                    const missing: string[] = [];
                    if (!code) missing.push('Mã sản phẩm');
                    if (!name) missing.push('Tên sản phẩm');
                    if (!catalogCode) missing.push('Mã Danh Mục');
                    if (missing.length > 0) {
                        catalogErrorItem.push({
                            rowNumber: i + 2,
                            message: `Thiếu dữ liệu cột: ${missing.join(', ')}`,
                        });
                        return null; // Bỏ qua dòng không hợp lệ
                    }

                    // Kiểm tra catalogCode có tồn tại không
                    const catalogExist = catalog.find(c => c.code === catalogCode);
                    if (!catalogExist) {
                        catalogErrorItem.push({
                            rowNumber: i + 2, // Số dòng trong Excel, +1 do bỏ header
                            message: `Mã danh mục ${catalogCode} không tồn tại`,
                        });
                        return null; // Bỏ qua dòng không hợp lệ
                    }

                    // Kiểm tra code có tồn tại không
                    const catalogItemExist = catalogItem.find(c => c.code === code);
                    if (catalogItemExist) {
                        catalogErrorItem.push({
                            rowNumber: i + 2, // Số dòng trong Excel, +1 do bỏ header
                            message: `Mã sản phẩm ${code} đã tồn tại`,
                        });
                        return null; // Bỏ qua dòng không hợp lệ
                    }

                    return {
                        catalogId,
                        code,
                        name,
                        unit,
                        price,
                        description,
                        rowNumber: i + 2, // Số dòng trong Excel, +1 do bỏ header
                    };
                })
                .filter(item => item !== null); // Loại bỏ các dòng không hợp lệ

            // Nếu có lỗi, ném ngoại lệ chi tiết trước
            if (catalogErrorItem.length > 0) {
                throw new BusinessException(
                    `Lỗi khi upload catalog item: dòng ${catalogErrorItem.map(item => item.rowNumber).join(', ')}, ${catalogErrorItem.map(item => item.message).join(', ')}`,
                );
            }

            if (data.length === 0) {
                throw new BusinessException('Không có dữ liệu hợp lệ trong sheet "Sản phẩm"');
            }
            // Tạo các catalog item
            for (const item of data) {
                await this.catalogItemRepo.save({
                    ...item,
                    tenantId: clientSessionContext.tenantId,
                });
            }

            return data; // Trả về dữ liệu đã xử lý
        } catch (error: any) {
            throw new BusinessException(error?.message || 'Upload catalog item thất bại');
        }
    }
}
