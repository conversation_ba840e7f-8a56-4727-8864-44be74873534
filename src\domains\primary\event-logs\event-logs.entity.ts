import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { NSEventLogs } from '~/common/enums/event-logs.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('event_logs')
export class EventLogsEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID của entity liên quan (Shipment, Order...)' })
    @Column({ type: 'uuid' })
    relatedEntityId: string;

    @ApiProperty({
        example: NSEventLogs.EntityTypes.QUOTE,
        description: '<PERSON><PERSON><PERSON> thực thể bị ảnh hưởng',
    })
    @Column({ type: 'enum', enum: NSEventLogs.EntityTypes })
    relatedEntityType: NSEventLogs.EntityTypes;

    @ApiProperty({ example: NSEventLogs.EventTypes.CREATED })
    @Column({ type: 'varchar', enum: NSEventLogs.EventTypes })
    eventType: NSEventLogs.EventTypes;

    @ApiPropertyOptional({ description: '<PERSON> tiết sự kiện (dạng JSON)' })
    @Column({ type: 'jsonb', nullable: true })
    details: Record<string, any>;
}
