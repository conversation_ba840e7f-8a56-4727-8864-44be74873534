import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
export class RevenueReportDto {
    @ApiProperty({ description: 'Doanh thu', example: 1000000 })
    @IsNumber()
    revenue: number;

    @ApiProperty({ description: 'Mã khách hàng', example: 'KH001' })
    @IsString()
    customerCode: string;

    @ApiProperty({ description: 'Tên khách hàng', example: 'Công ty ABC' })
    @IsString()
    customerName: string;

    @ApiProperty({ description: 'Mã số thuế', example: '0123456789' })
    @IsString()
    taxCode: string;

    @ApiProperty({ description: 'Số điện thoại', example: '0909123456' })
    @IsString()
    phone: string;

    @ApiProperty({ description: 'Địa chỉ', example: '123 Nguyễn <PERSON>, Q1, TP.HCM' })
    @IsString()
    address: string;

    @ApiProperty({ description: 'Tổng số báo giá', example: 5 })
    @IsNumber()
    totalQuote: number;

    @ApiProperty({ description: 'Tổng số hợp đồng', example: 3 })
    @IsNumber()
    totalContract: number;
}

export class ListRevenueReportDto {
    @ApiProperty({ description: 'Ngày bắt đầu doanh thu (YYYY-MM-DD)', example: '2023-01-01' })
    @IsString()
    @IsOptional()
    revenueFrom?: string;

    @ApiProperty({ description: 'Ngày kết thúc doanh thu (YYYY-MM-DD)', example: '2023-12-31' })
    @IsString()
    @IsOptional()
    revenueTo?: string;

    @ApiProperty({
        description: 'Ngày bắt đầu hợp đồng (YYYY-MM-DD)',
        example: '2023-01-01',
        required: false,
    })
    @IsString()
    @IsOptional()
    dateFrom?: string;

    @ApiProperty({
        description: 'Ngày kết thúc hợp đồng (YYYY-MM-DD)',
        example: '2023-12-31',
        required: false,
    })
    @IsString()
    @IsOptional()
    dateTo?: string;

    @ApiProperty({ description: 'Mã khách hàng', example: 'KH001', required: false })
    @IsString()
    @IsOptional()
    customerCode?: string;

    @ApiProperty({ description: 'Tên khách hàng', example: 'Công ty ABC', required: false })
    @IsString()
    @IsOptional()
    customerName?: string;

    @ApiProperty({ description: 'Mã số thuế', example: '0123456789', required: false })
    @IsString()
    @IsOptional()
    taxCode?: string;

    @ApiProperty({ description: 'Số điện thoại', example: '0909123456', required: false })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiProperty({
        description: 'Địa chỉ',
        example: '123 Nguyễn Trãi, Q1, TP.HCM',
        required: false,
    })
    @IsString()
    @IsOptional()
    address?: string;
}
