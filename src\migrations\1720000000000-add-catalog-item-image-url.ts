import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCatalogItemImageUrl1720000000000 implements MigrationInterface {
    name = 'AddCatalogItemImageUrl1720000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE catalog_item ADD COLUMN image_url varchar(255) NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE catalog_item DROP COLUMN image_url`);
    }
}

