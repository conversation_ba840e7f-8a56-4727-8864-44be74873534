// Helper type
export const PERMISSION_CODES = {
    SETTING_ALERTS: {
        VIEW: 'SETTING_ALERTS.VIEW',
        CREATE: 'SETTING_ALERTS.CREATE',
        UPDATE: 'SETTING_ALERTS.UPDATE',
    },
    SETTING_CAMPAIGN: {
        VIEW: 'SETTING_CAMPAIGN.VIEW',
        CREATE: 'SETTING_CAMPAIGN.CREATE',
        UPDATE: 'SETTING_CAMPAIGN.UPDATE',
    },
    SETTING_COMPLAINT: {
        VIEW: 'SETTING_COMPLAINT.VIEW',
        CREATE: 'SETTING_COMPLAINT.CREATE',
        UPDATE: 'SETTING_COMPLAINT.UPDATE',
    },
    // C<PERSON>u hình - Quản lý vai trò
    SETTING_ROLE: {
        VIEW: 'SETTING_ROLE.VIEW',
        CREATE: 'SETTING_ROLE.CREATE',
        UPDATE: 'SETTING_ROLE.UPDATE',
    },
    SETTING_PERMISSION: {
        VIEW: 'SETTING_PERMISSION.VIEW',
        CREATE: 'SETTING_PERMISSION.CREATE',
        UPDATE: 'SETTING_PERMISSION.UPDATE',
    },
    SETTING_TEMPLATE_CONTRACT: {
        VIEW: 'SETTING_TEMPLATE_CONTRACT.VIEW',
        CREATE: 'SETTING_TEMPLATE_CONTRACT.CREATE',
        UPDATE: 'SETTING_TEMPLATE_CONTRACT.UPDATE',
    },
    SETTING_CATALOG: {
        VIEW: 'SETTING_CATALOG.VIEW',
        CREATE: 'SETTING_CATALOG.CREATE',
        UPDATE: 'SETTING_CATALOG.UPDATE',
    },
    SETTING_CATALOG_ITEM: {
        VIEW: 'SETTING_CATALOG_ITEM.VIEW',
        CREATE: 'SETTING_CATALOG_ITEM.CREATE',
        UPDATE: 'SETTING_CATALOG_ITEM.UPDATE',
    },
    SETTING_CONTACT: {
        VIEW: 'SETTING_CONTACT.VIEW',
        CREATE: 'SETTING_CONTACT.CREATE',
        UPDATE: 'SETTING_CONTACT.UPDATE',
    },
    SETTING_CUSTOMER_CONTACT: {
        VIEW: 'SETTING_CUSTOMER_CONTACT.VIEW',
        CREATE: 'SETTING_CUSTOMER_CONTACT.CREATE',
        UPDATE: 'SETTING_CUSTOMER_CONTACT.UPDATE',
    },
    SETTING_CUSTOMER_ADDRESS: {
        VIEW: 'SETTING_CUSTOMER_ADDRESS.VIEW',
        CREATE: 'SETTING_CUSTOMER_ADDRESS.CREATE',
        UPDATE: 'SETTING_CUSTOMER_ADDRESS.UPDATE',
    },
    SETTING_CONTRACT: {
        VIEW: 'SETTING_CONTRACT.VIEW',
        CREATE: 'SETTING_CONTRACT.CREATE',
        UPDATE: 'SETTING_CONTRACT.UPDATE',
    },
    SETTING_CUSTOMER: {
        VIEW: 'SETTING_CUSTOMER.VIEW',
        CREATE: 'SETTING_CUSTOMER.CREATE',
        UPDATE: 'SETTING_CUSTOMER.UPDATE',
    },
    SETTING_QUOTATION: {
        VIEW: 'SETTING_QUOTATION.VIEW',
        CREATE: 'SETTING_QUOTATION.CREATE',
        UPDATE: 'SETTING_QUOTATION.UPDATE',
    },
    SETTING_DEPARTMENT: {
        VIEW: 'SETTING_DEPARTMENT.VIEW',
        CREATE: 'SETTING_DEPARTMENT.CREATE',
        UPDATE: 'SETTING_DEPARTMENT.UPDATE',
    },
    SETTING_MEMBER: {
        VIEW: 'SETTING_MEMBER.VIEW',
        CREATE: 'SETTING_MEMBER.CREATE',
        UPDATE: 'SETTING_MEMBER.UPDATE',
    },
    SETTING_POSITION: {
        VIEW: 'SETTING_POSITION.VIEW',
        CREATE: 'SETTING_POSITION.CREATE',
        UPDATE: 'SETTING_POSITION.UPDATE',
    },
    SETTING_STRING: {
        VIEW: 'SETTING_STRING.VIEW',
        CREATE: 'SETTING_STRING.CREATE',
        UPDATE: 'SETTING_STRING.UPDATE',
    },
    TASK: {
        VIEW: 'TASK.VIEW',
        CREATE: 'TASK.CREATE',
        UPDATE: 'TASK.UPDATE',
        DELETE: 'TASK.DELETE',
    },
} as const;

type Values<T> = T[keyof T];
type DeepValues<T> = Values<{ [K in keyof T]: Values<T[K]> }>;
export type PermissionCode = DeepValues<typeof PERMISSION_CODES>;
export type ParentRoleCode = keyof typeof PERMISSION_CODES;
