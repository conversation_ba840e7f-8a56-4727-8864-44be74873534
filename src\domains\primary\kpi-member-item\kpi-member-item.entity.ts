import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSKPI } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('kpi_member_item')
@Index('uniq_kpi_member_item_target_category', ['tenantId', 'targetId', 'categoryKey'], {
    unique: true,
})
export class KpiMemberItemEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'kpi-member-id' })
    @Column({ type: 'uuid' })
    targetId: string;

    @ApiProperty({ example: NSKPI.ECategoryKey.CUSTOMERS_COUNT })
    @Column({ type: 'varchar', length: 64 })
    categoryKey: NSKPI.ECategoryKey;

    @ApiProperty({ example: 100 })
    @Column({ type: 'int' })
    targetValue: number;

    @ApiPropertyOptional({ example: 0 })
    @Column({ type: 'int', default: 0, nullable: true })
    achievedValue?: number;

    @ApiPropertyOptional({ example: 'khách' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    unit?: string;

    @ApiPropertyOptional({ example: 50 })
    @Column({ type: 'int', nullable: true })
    weight?: number;

    @ApiPropertyOptional({ example: { 'member-id-1': 60, 'member-id-2': 40 } })
    @Column({ type: 'jsonb', nullable: true })
    memberWeights?: Record<string, number>;
}
