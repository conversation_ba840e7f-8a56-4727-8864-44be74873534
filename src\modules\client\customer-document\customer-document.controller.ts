import { Body, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { CustomerDocumentService } from './customer-document.service';
import {
    CreateCustomerDocumentDto,
    ListCustomerDocumentDto,
    PayloadCreateCustomerDocumentDto,
    PayloadUpdateCustomerDocumentDto,
    SetActiveCustomerDocumentDto,
    UpdateCustomerDocumentDto,
} from './dto/customer-document.dto';

@DefController('customer-document')
export class CustomerDocumentController {
    constructor(private readonly customerDocumentService: CustomerDocumentService) {}

    @DefGet('pagination')
    async listCustomerDocument(@Query() params: ListCustomerDocumentDto) {
        return await this.customerDocumentService.listCustomerDocument(params);
    }

    @DefPost('create')
    @UseInterceptors(FileInterceptor('file'))
    async createCustomerDocument(
        @Body('payload') payload: string,
        @UploadedFile() file: Express.Multer.File,
    ) {
        const parsedPayload: PayloadCreateCustomerDocumentDto = JSON.parse(payload);

        const dto: CreateCustomerDocumentDto = {
            payload: parsedPayload,
        };

        return await this.customerDocumentService.createCustomerDocument(dto, file);
    }

    @DefPost('update')
    @UseInterceptors(FileInterceptor('file'))
    async updateCustomerDocument(
        @UploadedFile() file: Express.Multer.File,
        @Body('payload') payload: string,
    ) {
        const parsedPayload: PayloadUpdateCustomerDocumentDto = JSON.parse(payload);

        const dto: UpdateCustomerDocumentDto = {
            payload: parsedPayload,
        };
        return await this.customerDocumentService.updateCustomerDocument(dto, file);
    }

    @DefPost('set-active')
    async setActiveCustomerDocument(@Body() body: SetActiveCustomerDocumentDto) {
        return await this.customerDocumentService.setActiveCustomerDocument(body);
    }
}
