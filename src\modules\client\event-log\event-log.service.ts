import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'ape-nestjs-typeorm3-kit';
import { Between, FindManyOptions, ILike, In } from 'typeorm';
import { PageRequest } from '~/@systems/utils';

import { NSEventLogs } from '~/common/enums/event-logs.enum';
import { DateHelper } from '~/common/helpers/date.helper';
import { MemberRepo } from '../../../domains/primary';
import { EventLogsEntity } from '../../../domains/primary/event-logs/event-logs.entity';
import { EventLogsRepo } from '../../../domains/primary/event-logs/event-logs.repo';
import { clientSessionContext } from '../client-session.context';
import { ListEventLogDto, LogEventDto } from './dto/event-log.dto';

@Injectable()
export class EventLogsService {
    constructor(
        @InjectRepo(EventLogsRepo)
        private eventLogRepo: EventLogsRepo,
        @InjectRepo(MemberRepo)
        private memberRepo: MemberRepo,
    ) {}

    /**
     * <PERSON><PERSON> một sự kiện vào nhật ký hệ thống
     */
    public async logEvent(body: LogEventDto): Promise<EventLogsEntity> {
        const { tenantId, memberId } = clientSessionContext;
        const entity: Partial<EventLogsEntity> = {
            relatedEntityType: body.relatedEntityType,
            relatedEntityId: body.relatedEntityId,
            eventType: body.eventType,
            details: body.details ?? {},
            tenantId,
            createdBy: memberId,
            createdDate: new Date(),
        } as any;
        return this.eventLogRepo.save(entity as EventLogsEntity);
    }

    /**
     * Lấy lịch sử sự kiện theo ID của một thực thể (ví dụ: LPN, Outbound, Inbound).
     * @param entityType Loại thực thể (ENUM)
     * @param entityId ID của thực thể
     * @param pageRequest Thông tin phân trang
     * @returns Danh sách các sự kiện theo trình tự thời gian có phân trang
     */
    public async getHistoryByEntity(
        entityType: NSEventLogs.EntityTypes,
        entityId: string,
        pageRequest?: PageRequest,
    ): Promise<{ data: EventLogsEntity[]; total: number }> {
        const options: FindManyOptions<EventLogsEntity> = {
            where: { relatedEntityType: entityType, relatedEntityId: entityId },
            order: { createdDate: 'DESC' },
        };

        if (pageRequest) {
            const { data, total } = await this.eventLogRepo.findPaginationByTenant(
                options,
                pageRequest,
            );
            const members = await this.memberRepo.find({
                where: { id: In(data.map(d => d.createdBy)) },
            });
            const mapName: Record<string, string> = {};
            members.forEach(m => (mapName[m.id] = (m as any).fullName));
            const mapped = data.map(d => ({ ...d, createdByName: mapName[d.createdBy] }));
            return { data: mapped as any, total };
        }

        // Nếu không có phân trang, trả về tất cả
        const data = await this.eventLogRepo.findByTenant(options);
        const members = await this.memberRepo.find({
            where: { id: In(data.map(d => d.createdBy)) },
        });
        const mapName: Record<string, string> = {};
        members.forEach(m => (mapName[m.id] = (m as any).fullName));
        const mapped = data.map(d => ({ ...d, createdByName: mapName[d.createdBy] }));
        return { data: mapped as any, total: data.length };
    }

    /**
     * Lấy lịch sử theo nhiều IDs của một loại thực thể.
     * @param entityType Loại thực thể
     * @param entityIds Mảng IDs
     */
    public async getHistoryByEntityIds(
        entityType: NSEventLogs.EntityTypes,
        entityIds: string[],
    ): Promise<EventLogsEntity[]> {
        const options: FindManyOptions<EventLogsEntity> = {
            where: { relatedEntityType: entityType, relatedEntityId: In(entityIds) },
            order: { createdDate: 'ASC' },
        };
        const data = await this.eventLogRepo.findByTenant(options);
        const members = await this.memberRepo.find({
            where: { id: In(data.map(d => d.createdBy)) },
        });
        const mapName: Record<string, string> = {};
        members.forEach(m => (mapName[m.id] = (m as any).fullName));
        return data.map(d => ({ ...d, createdByName: mapName[d.createdBy] })) as any;
    }

    /**
     * Lấy toàn bộ event logs phân trang có filter
     * @param params Thông tin filter và phân trang
     * @returns Danh sách event logs có phân trang
     */
    public async list(
        params: ListEventLogDto,
    ): Promise<{ data: EventLogsEntity[]; total: number }> {
        const where: any = {};

        // Filter theo loại thực thể
        if (params.relatedEntityType) {
            where.relatedEntityType = params.relatedEntityType;
        }

        // Filter theo loại sự kiện (tìm kiếm một phần)
        if (params.eventType) {
            where.eventType = ILike(`%${params.eventType}%`);
        }

        // Filter theo người tạo
        if (params.userId) {
            where.createdBy = ILike(`%${params.userId}%`);
        }

        // Filter theo khoảng thời gian
        if (params.startDate && params.endDate) {
            const [startDate, endDate] = DateHelper.createBetweenRange(
                params.startDate,
                params.endDate,
            );
            where.createdDate = Between(startDate, endDate);
        } else if (params.startDate) {
            const startDate = DateHelper.setStartOfDay(new Date(params.startDate));
            where.createdDate = Between(startDate, new Date());
        } else if (params.endDate) {
            const endDate = DateHelper.setEndOfDay(new Date(params.endDate));
            where.createdDate = Between(new Date(0), endDate);
        }

        const options: FindManyOptions<EventLogsEntity> = {
            where,
            order: { createdDate: 'DESC' },
        };

        const { data, total } = await this.eventLogRepo.findPagination(options, {
            pageIndex: params.pageIndex || 1,
            pageSize: params.pageSize || 10,
        });
        const members = await this.memberRepo.find({
            where: { id: In(data.map(d => d.createdBy)) },
        });
        const mapName: Record<string, string> = {};
        members.forEach(m => (mapName[m.id] = (m as any).fullName));
        const mapped = data.map(d => ({ ...d, createdByName: mapName[d.createdBy] }));
        return { data: mapped as any, total };
    }

    // thông tin tri tiết của một event log
    public async getEventLogDetail(id: string): Promise<EventLogsEntity> {
        return this.eventLogRepo.findOne({ where: { id } });
    }
}
