import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { <PERSON>lert } from '~/common/enums/alert.enum';

/**
 * DTO cho request lấy danh sách cảnh báo
 */
export class ListAlertsRequestDto extends PageRequest {
    @ApiPropertyOptional({ enum: NSAlert.EType })
    @IsOptional()
    @IsEnum(NSAlert.EType)
    type?: NSAlert.EType;

    @ApiPropertyOptional({ enum: NSAlert.ESeverity })
    @IsOptional()
    @IsEnum(NSAlert.ESeverity)
    severity?: NSAlert.ESeverity;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    isRead?: boolean;

    @ApiPropertyOptional({ description: 'Start date for filtering (ISO string)' })
    @IsOptional()
    @IsString()
    startDate?: string;

    @ApiPropertyOptional({ description: 'End date for filtering (ISO string)' })
    @IsOptional()
    @IsString()
    endDate?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    customerId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    complaintId?: string;
}

/**
 * DTO cho response cảnh báo
 */
export interface IAlert {
    id: string;
    type: NSAlert.EType;
    severity: NSAlert.ESeverity;
    title: string;
    description: string;
    source: NSAlert.ESource;
    dueDate?: string;
    createdDate: string;
    isRead: boolean;
    metadata?: Record<string, any>;
}

/**
 * DTO cho response tổng hợp cảnh báo
 */
export interface IAlertSummary {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    byType: Record<NSAlert.EType, number>;
}

/**
 * DTO cho response danh sách cảnh báo
 */
export interface IListAlertsResponse {
    items: IAlert[];
    summary: IAlertSummary;
    total: number;
    page: number;
    pageSize: number;
}

/**
 * DTO cho request đánh dấu đã đọc
 */
export class MarkAsReadRequestDto {
    @ApiProperty({ type: [String], description: 'Array of alert IDs' })
    @IsArray()
    @IsUUID(4, { each: true })
    alertIds: string[];
}

/**
 * DTO cho response đánh dấu đã đọc
 */
export interface IMarkAsReadResponse {
    success: boolean;
    count: number;
}
