import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSQuote } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('quote')
export class QuoteEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'Công ty TNHH XYZ' })
    @Column({ type: 'uuid', nullable: true })
    customerId: string;

    @ApiProperty()
    @Column({ type: 'uuid', nullable: true })
    memberId: string;

    @ApiProperty({ example: 'QT-2024-001' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    @Index()
    quotationNumber: string;

    @ApiProperty({ description: 'Ngày báo giá', example: '2024-01-15' })
    @Column({ type: 'timestamptz', nullable: true })
    quotationDate: string;

    @ApiProperty({ example: 'Địa chỉ B', description: 'Địa chỉ khách hàng' })
    @Column({ type: 'varchar', length: 100, nullable: true })
    customerAddressId: string;

    @ApiProperty({ description: 'Tên khách hàng' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    customerName: string;

    @ApiProperty({ description: 'Địa chỉ khách hàng' })
    @Column({ type: 'text', nullable: true })
    customerAddress: string;

    @ApiProperty({ description: 'Điện thoại khách hàng' })
    @Column({ type: 'varchar', length: 24, nullable: true })
    customerPhone: string;

    @ApiProperty({ description: 'Mã số thuế khách hàng' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    customerTaxCode: string;

    @ApiProperty({ example: 'Trần Thị B', description: 'Người liên hệ khách hàng' })
    @Column({ type: 'varchar', length: 100, nullable: true })
    customerContactId: string;

    @ApiProperty({ description: 'Ngày giao hàng', example: '2024-02-15' })
    @Column({ type: 'timestamptz', nullable: true })
    deliveryDate: string;

    @ApiProperty({ description: 'Địa chỉ giao hàng', example: 'Tại công ty khách hàng' })
    @Column({ type: 'text', nullable: true })
    deliveryLocation: string;

    @ApiProperty({ example: 30, description: 'Số ngày hiệu lực' })
    @Column({ type: 'int', nullable: true })
    validityDays: number;

    @ApiProperty({ example: '10 ngày', description: 'Thời hạn thanh toán' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    dueDate: string;

    @ApiProperty({ example: 'Báo giá có hiệu lực 30 ngày', description: 'Ghi chú' })
    @Column({ type: 'text', nullable: true })
    notes?: string;

    @ApiProperty({ example: 'Tổng giá trị báo giá', description: 'Tổng giá trị báo giá' })
    @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
    totalAmount: number;

    @ApiPropertyOptional({ description: 'Tệp đính kèm' })
    @Column({ type: 'text', array: true, nullable: true })
    documentsUrls?: string[];

    @ApiPropertyOptional({ description: 'Hình ảnh' })
    @Column({ type: 'text', array: true, nullable: true })
    imagesUrls?: string[];

    @ApiProperty({ example: 'DRAFT', description: 'Trạng thái' })
    @Column({ type: 'enum', enum: NSQuote.EStatus, default: NSQuote.EStatus.NEW })
    status: NSQuote.EStatus;
}
