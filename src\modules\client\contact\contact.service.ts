import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { generateCodeHelper } from '~/common/helpers';

import { CustomerContactRepo, CustomerRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';
import {
    CreateContactDto,
    DeleteListContactDto,
    DetailContactDto,
    ListContactDto,
    ListCreateContactDto,
    ListUpdateContactDto,
    UpdateContactDto,
} from './dto/contact';

@Injectable()
export class ContactService {
    constructor(
        @InjectRepo(CustomerContactRepo) private customerContactRepo: CustomerContactRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
    ) {}

    async listContact(params: ListContactDto) {
        const where: any = {};

        if (params.customerId) {
            where.customerId = params.customerId;
        }

        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }

        if (params.email) {
            where.email = ILike(`%${params.email}%`);
        }

        if (params.phone) {
            where.phone = ILike(`%${params.phone}%`);
        }

        if (params.position) {
            where.position = params.position;
        }

        if (params.pageSize === -1) {
            const [data, total] = await this.customerContactRepo.findAndCount({
                where: { ...where, tenantId: clientSessionContext.tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                total,
                data,
            };
        } else {
            return this.customerContactRepo.findPagination(
                {
                    where: { ...where, tenantId: clientSessionContext.tenantId },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                {
                    pageIndex: params.pageIndex,
                    pageSize: params.pageSize,
                },
            );
        }
    }

    // create contact
    @DefTransaction()
    async createContact(contact: CreateContactDto) {
        const check = await this.customerRepo.findOne({
            where: {
                id: contact.customerId,
            },
        });
        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin khách hàng');
        }
        const newContact = await this.customerContactRepo.insert({
            ...contact,
            code: generateCodeHelper.generateCustomerCode('CONT'),
            createdBy: clientSessionContext.memberId,
        });

        return {
            message: 'Tạo liên hệ thành công',
            data: newContact,
        };
    }

    @DefTransaction()
    async createListContact(contact: ListCreateContactDto) {
        //check customer exist
        const check = await this.customerRepo.find({
            where: {
                id: In(contact.contacts.map(c => c?.customerId)),
            },
        });

        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin khách hàng');
        }
        const newContact = await this.customerContactRepo.insert(
            contact.contacts.map(c => ({
                ...c,
                code: generateCodeHelper.generateCustomerCode('CONT'),
                createdBy: clientSessionContext.memberId,
            })),
        );
        return {
            message: 'Tạo liên hệ thành công',
            data: newContact,
        };
    }

    // detail contact
    @DefTransaction()
    async detailContact(contact: DetailContactDto) {
        const { id } = contact;

        if (!id) {
            throw new BusinessException('ID is required');
        }

        const check = await this.customerContactRepo.findOne({
            where: {
                id: id,
            },
        });

        return check;
    }

    // update contact
    @DefTransaction()
    async updateContact(contact: UpdateContactDto) {
        const { id, ...contactData } = contact;
        const check = await this.customerContactRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Contact not found');
        }

        const updatedContact = await this.customerContactRepo.update(id, { ...contactData, id });
        return {
            message: 'Cập nhật liên hệ thành công',
            data: updatedContact,
        };
    }

    @DefTransaction()
    async updateListContact(contact: ListUpdateContactDto) {
        contact.contacts.forEach((c: any) => {
            if (!c.id) {
                c.code = generateCodeHelper.generateCustomerCode('CONT');
                c.createdBy = clientSessionContext.memberId;
            }
        });
        //update hoặc tạo cả data cũ và tạo data mới
        const updatedContact = await this.customerContactRepo.saves(contact.contacts);
        return {
            message: 'Cập nhật liên hệ thành công',
            data: updatedContact,
        };
    }

    // delete contact
    @DefTransaction()
    async deleteContact(contact: DetailContactDto) {
        const { id } = contact;
        await this.customerContactRepo.delete(id);
        return { message: 'Xóa liên hệ thành công' };
    }

    @DefTransaction()
    async deleteListContact(contact: DeleteListContactDto) {
        const { ids } = contact;
        await this.customerContactRepo.delete({ id: In(ids) });
        return { message: 'Xóa liên hệ thành công' };
    }
}
