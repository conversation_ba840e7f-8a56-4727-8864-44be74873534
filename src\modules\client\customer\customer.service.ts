import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike, In, Raw } from 'typeorm';
import * as XLSX from 'xlsx';
import { BusinessException } from '~/@systems/exceptions';
import { NSCustomer, NSMember } from '~/common/enums';
import { NSSettingOption } from '~/common/enums/setting-option.enum';
import { generateCodeHelper } from '~/common/helpers';
import { CustomerContactRepo, CustomerRepo, MemberRepo } from '~/domains/primary';
import { CustomerAddressRepo } from '~/domains/primary/customer-address/customer-address.repo';
import { clientSessionContext } from '../client-session.context';
import { CommunesService } from '../communes/communes.service';
import { MemberService } from '../member/member.service';
import { ProvincesService } from '../provinces/provinces.service';
import { SettingStringService } from '../setting-string/setting-string.service';
import {
    CreateCustomerDto,
    DetailCustomerDto,
    ListCustomerDto,
    UpdateCustomerDto,
} from './dto/customer.dto';

@Injectable()
export class CustomerService {
    constructor(
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        @InjectRepo(CustomerContactRepo) private customerContactRepo: CustomerContactRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        @InjectRepo(CustomerAddressRepo) private customerAddressRepo: CustomerAddressRepo,
        private readonly memberService: MemberService,
        private readonly provincesService: ProvincesService,
        private readonly communesService: CommunesService,
        private readonly settingStringService: SettingStringService,
    ) {}

    // List Customer
    async listCustomer(params: ListCustomerDto) {
        const { tenantId, memberId } = clientSessionContext;
        const where: any = {};
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }
        if (params.email) {
            where.email = ILike(`%${params.email}%`);
        }
        if (params.phone) {
            where.phone = ILike(`%${params.phone}%`);
        }
        if (params.customerType) {
            where.customerType = params.customerType;
        }
        if (params.source) {
            where.source = ILike(`%${params.source}%`);
        }
        if (params.market) {
            where.market = ILike(`%${params.market}%`);
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.provinceCode) {
            where.provinceCode = params.provinceCode;
        }
        if (params.communeCode) {
            where.communeCode = params.communeCode;
        }
        if (params.address) {
            where.address = ILike(`%${params.address}%`);
        }
        if (params.type) {
            where.type = params.type;
        }

        if (params.birthdayFrom && params.birthdayTo) {
            where.birthday = Between(new Date(params.birthdayFrom), new Date(params.birthdayTo));
        } else if (params.birthdayFrom) {
            where.birthday = Between(new Date(params.birthdayFrom), new Date(params.birthdayFrom));
        } else if (params.birthdayTo) {
            where.birthday = Between(new Date(params.birthdayTo), new Date(params.birthdayTo));
        }

        if (params.createdFrom && params.createdTo) {
            where.createdDate = Between(params.createdFrom, params.createdTo);
        }
        if (params.createdBy && params.createdBy) {
            where.createdBy = params.createdBy;
        }

        // Áp dụng quyền xem dữ liệu theo loại member và salesRep
        const currentType = clientSessionContext.sessionData?.member?.type;
        const isMaster = currentType === NSMember.EType.TENANT_MASTER;
        if (!isMaster) {
            if (params.salesRep) {
                where.salesRep = Raw(
                    alias =>
                        `${alias} @> '["${params.salesRep}"]'::jsonb AND ${alias} @> '["${memberId}"]'::jsonb`,
                );
            } else {
                where.salesRep = Raw(alias => `${alias} @> '["${memberId}"]'::jsonb`);
            }
        } else if (params.salesRep) {
            where.salesRep = Raw(alias => `${alias} @> '["${params.salesRep}"]'::jsonb`);
        }

        let res: any = {};

        if (Number(params.pageSize) === -1) {
            const [data, total] = await this.customerRepo.findAndCount({
                where: {
                    ...where,
                    tenantId,
                },
                order: {
                    createdDate: 'DESC',
                },
            });
            res = {
                data,
                total,
            };
        } else {
            const { data, total } = await this.customerRepo.findPagination(
                {
                    where: {
                        ...where,
                        tenantId,
                    },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                {
                    pageIndex: params.pageIndex,
                    pageSize: params.pageSize,
                },
            );
            res = {
                data,
                total,
            };
        }

        //find all salesRep name
        const dictSalesRep = {};

        {
            const salesRep = await this.memberService.pagination({ pageSize: -1, pageIndex: 1 });
            salesRep.data.forEach(item => {
                dictSalesRep[item.id] = item.fullName;
            });
        }
        //find createBy name
        const createdBy = await this.memberRepo.find({
            where: {
                id: In(res.data.map(item => item.createdBy)),
            },
        });

        const members = await this.memberRepo.find({
            where: {
                tenantId,
                status: NSMember.EStatus.ACTIVE,
            },
        });
        //map
        const finalData = res.data.map(item => {
            const createdByName = createdBy.find(c => c.id === item.createdBy)?.fullName;
            const salesRepName = item.salesRep.map(id => dictSalesRep[id]).join(', ');
            const listSalesRep = members.map(mem => ({
                value: mem.id,
                label: mem.fullName,
            }));

            return { ...item, createdByName, salesRepName, listSalesRep };
        });
        return {
            data: finalData,
            total: res.total,
        };
    }

    async listBirthdays(params: {
        month: number;
        day?: number;
        pageIndex?: number;
        pageSize?: number;
    }) {
        const { tenantId, memberId } = clientSessionContext;
        const month = Number(params.month);
        const day = params.day ? Number(params.day) : undefined;
        const where: any = {
            tenantId,
            birthday: Raw(
                alias =>
                    `EXTRACT(MONTH FROM ${alias}) = ${month}` +
                    (day ? ` AND EXTRACT(DAY FROM ${alias}) = ${day}` : ''),
            ),
        };

        if (Number(params.pageSize) === -1) {
            const [data, total] = await this.customerRepo.findAndCount({
                where: {
                    ...where,
                    status: NSCustomer.EActiveStatus.ACTIVE,
                    salesRep: Raw(alias => `${alias} @> '["${memberId}"]'::jsonb`),
                },
                order: { createdDate: 'DESC' },
            });

            return { data, total };
        }

        const { data, total } = await this.customerRepo.findPagination(
            {
                where: {
                    ...where,
                    status: NSCustomer.EActiveStatus.ACTIVE,
                    salesRep: Raw(alias => `${alias} @> '["${memberId}"]'::jsonb`),
                },
                order: { createdDate: 'DESC' },
            },
            { pageIndex: params.pageIndex ?? 1, pageSize: params.pageSize ?? 10 },
        );
        return { data, total };
    }

    async getCustomerByIds(ids: string[]) {
        if (typeof ids === 'string') {
            ids = (ids as string).split(',');
            const customers = await this.customerRepo.find({
                where: { id: In(ids), tenantId: clientSessionContext.tenantId },
            });
            return customers;
        }
        return await this.customerRepo.find({
            where: { id: In(ids), tenantId: clientSessionContext.tenantId },
        });
    }

    // Select Box
    async selectBox() {
        const customers = await this.customerRepo.find({
            where: { status: NSCustomer.EActiveStatus.ACTIVE },
        });

        const data = customers.map(async customer => {
            const contacts = await this.customerContactRepo.find({
                where: { customerId: customer.id },
                order: { isDecisionMaker: 'DESC' },
            });
            return {
                label: customer.name,
                value: customer.id,
                customerAddress: customer.address,
                customerTaxCode: customer.taxNumber,
                contacts: contacts.map(contact => ({
                    value: contact.id,
                    label: contact.name,
                    phone: contact.phone,
                    isDecisionMaker: contact.isDecisionMaker,
                })),
            };
        });

        return data;
    }

    // Lấy chi tiết khách hàng và danh sách liên hệ
    async getCustomerById(params: DetailCustomerDto) {
        const { id } = params;

        if (!id) {
            throw new BusinessException('ID khách hàng không hợp lệ');
        }

        const customer = await this.customerRepo.findOne({ where: { id } });
        const creator = await this.memberRepo.findOne({ where: { id: customer?.createdBy } });
        if (!customer) {
            throw new BusinessException('Customer not found');
        }
        const contacts = await this.customerContactRepo.find({ where: { customerId: id } });

        const addresses = await this.customerAddressRepo.find({ where: { customerId: id } });
        const provinceCodes = addresses.map(item => item.provinceCode).join(', ');
        const provinces = await this.provincesService.getProvincesByCode(provinceCodes);
        const communeCodes = addresses.map(item => item.communeCode).join(', ');
        const communes = await this.communesService.getCommunesByCode(communeCodes);

        return {
            ...customer,
            createdByName: creator?.fullName,
            contacts,
            addresses: addresses.map(item => ({
                ...item,
                provinceName: provinces.find(p => p.code === item.provinceCode)?.name,
                communeName: communes.find(c => c.code === item.communeCode)?.name,
            })),
        };
    }

    // Tạo khách hàng
    @DefTransaction()
    async createCustomer(customer: CreateCustomerDto) {
        try {
            const { provinces, communes, ...customerData } = customer;
            const { tenantId } = clientSessionContext;
            const getCustomers = await this.customerRepo.find({
                where: {
                    tenantId,
                },
            });
            const code = `C-00000${getCustomers.length + 1}`;
            const newCustomer = await this.customerRepo.save({
                ...customerData,
                code,
                createdBy: clientSessionContext.memberId,
            });

            // if (contacts) {
            //     // Kiểm tra xem có contact nào có dữ liệu không
            //     const hasValidContact = contacts.some(
            //         contact => contact.name || contact.email || contact.phone || contact.position,
            //     );

            //     if (!hasValidContact) {
            //         throw new BusinessException(
            //             'Contact phải có ít nhất tên, email hoặc số điện thoại',
            //         );
            //     }

            //     const newContacts = contacts.map((contact, index) => ({
            //         customerId: newCustomer.id,
            //         code: generateCodeHelper.generateCustomerCode('CONT'),
            //         name: contact.name,
            //         email: contact.email,
            //         phone: contact.phone,
            //         position: contact.position,
            //         note: contact.note || null,
            //         isDecisionMaker: contact.isDecisionMaker || false,
            //     }));

            //     for (const contact of newContacts) {
            //         await this.contactService.createContact({
            //             ...contact,
            //             customerId: newCustomer.id,
            //         });
            //     }
            // }
            return {
                message: 'Tạo khách hàng thành công',
                data: newCustomer,
            };
        } catch (error) {
            throw new BusinessException('Không thể tạo khách hàng: ' + error.message);
        }
    }

    // Cập nhật khách hàng
    @DefTransaction()
    async updateCustomer(customer: UpdateCustomerDto) {
        const { id, ...customerData } = customer;
        const check = await this.customerRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Customer not found');
        }
        try {
            await this.customerRepo.update(id, {
                ...customerData,
                id: check.id,
            });

            return { message: 'Cập nhật khách hàng thành công' };
        } catch (error) {
            throw new BusinessException('Không thể cập nhật khách hàng: ' + error.message);
        }
    }

    // Xóa khách hàng
    // @DefTransaction()
    // async deleteCustomer(params: DetailCustomerDto) {
    //     const { id } = params;
    //     if (!id) {
    //         throw new BusinessException('ID khách hàng không hợp lệ');
    //     }

    //     const check = await this.customerRepo.findOne({ where: { id } });
    //     if (!check) {
    //         throw new BusinessException('Customer not found');
    //     }

    //     // update isActive = false
    //     await this.customerRepo.update(id, { status: NSCustomer.EActiveStatus.INACTIVE });

    //     return { message: 'Xóa khách hàng thành công' };
    // }

    @DefTransaction()
    async setActive(id: string) {
        //find
        try {
            const customer = await this.customerRepo.findOneBy({ id });
            if (!customer) {
                throw new Error('Customer not found');
            }
            if (customer.status === NSCustomer.EActiveStatus.ACTIVE) {
                await this.customerRepo.update(id, {
                    status: NSCustomer.EActiveStatus.INACTIVE,
                    id: customer.id,
                });
            }
            if (customer.status === NSCustomer.EActiveStatus.INACTIVE) {
                await this.customerRepo.update(id, {
                    status: NSCustomer.EActiveStatus.ACTIVE,
                    id: customer.id,
                });
            }

            return { message: 'Cập nhật trạng thái khách hàng thành công' };
        } catch (error) {
            throw new BusinessException(
                'Không thể cập nhật trạng thái khách hàng: ' + error.message,
            );
        }
    }

    @DefTransaction()
    async uploadCustomer(file: Express.Multer.File) {
        const errorItems: { rowNumber: number; message: string }[] = [];
        try {
            if (!file) {
                throw new BusinessException('Không có file upload');
            }
            let workbook: XLSX.WorkBook;
            if (file.buffer && file.buffer.length > 0) {
                workbook = XLSX.read(file.buffer, { type: 'buffer' });
            } else if ((file as any).path) {
                const fs = await import('fs');
                const data = fs.readFileSync((file as any).path);
                workbook = XLSX.read(data, { type: 'buffer' });
            } else {
                throw new BusinessException('Không đọc được nội dung file');
            }
            if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                throw new BusinessException('File Excel không có sheet');
            }
            const normalize = (s: string) => s?.trim().toLowerCase() ?? '';
            const findSheet = (expected: string) => {
                const idx = workbook.SheetNames.findIndex(
                    n => normalize(n) === normalize(expected),
                );
                return idx >= 0 ? workbook.Sheets[workbook.SheetNames[idx]] : undefined;
            };
            const worksheet = findSheet('Khách hàng') ?? workbook.Sheets[workbook.SheetNames[0]];
            if (!worksheet) {
                throw new BusinessException('Không tìm thấy sheet dữ liệu');
            }
            const rows = XLSX.utils.sheet_to_json<any[]>(worksheet, {
                header: 1,
                defval: '',
            }) as any[][];
            if (!rows || rows.length === 0) {
                throw new BusinessException('Sheet không có dữ liệu');
            }
            const expectedHeaders = [
                'Tên khách hàng (*)',
                'Mã số thuế (*)',
                'Số điện thoại (*)',
                'Email',
                'Fax',
                'Loại địa chỉ',
                'Tỉnh thành(*)',
                'Phường xã(*)',
                'Địa chỉ chi tiết (*)',
                'Trụ sở chính',
                'Loại khách hàng',
                'Nguồn khách hàng',
                'Thị trường',
                'Nhân viên phụ trách',
                'Tên liên hệ (*)',
                'Chức vụ (*)',
                'Người quyết định',
                'Số điện thoại (*)',
                'Email người liên hệ',
            ];
            const headerRow = (rows[0] || []).map(h => (typeof h === 'string' ? h.trim() : h));
            const headerIndexMap: Record<string, number> = {};
            for (const h of expectedHeaders) {
                let idx = headerRow.indexOf(h);
                if (idx === -1) {
                    idx = headerRow.findIndex(
                        x => String(x).trim().toLowerCase() === h.toLowerCase(),
                    );
                }
                if (idx === -1) {
                    throw new BusinessException(`Thiếu cột bắt buộc: ${h}`);
                }
                headerIndexMap[h] = idx;
            }
            const existingCustomers = await this.customerRepo.find({
                where: { tenantId: clientSessionContext.tenantId },
            });
            const optionValues = await this.settingStringService.getDefaultValuesByKeys('option', [
                NSSettingOption.EDefaultConfigKey.CUSTOMER_TYPE,
                NSSettingOption.EDefaultConfigKey.CUSTOMER_SOURCE,
                NSSettingOption.EDefaultConfigKey.CUSTOMER_MARKET,
            ]);
            const rawCustomerTypes =
                optionValues[NSSettingOption.EDefaultConfigKey.CUSTOMER_TYPE] ??
                NSSettingOption.EDefaultConfig[NSSettingOption.EDefaultConfigKey.CUSTOMER_TYPE] ??
                [];
            const rawSources =
                optionValues[NSSettingOption.EDefaultConfigKey.CUSTOMER_SOURCE] ??
                NSSettingOption.EDefaultConfig[NSSettingOption.EDefaultConfigKey.CUSTOMER_SOURCE] ??
                [];
            const rawMarkets =
                optionValues[NSSettingOption.EDefaultConfigKey.CUSTOMER_MARKET] ??
                NSSettingOption.EDefaultConfig[NSSettingOption.EDefaultConfigKey.CUSTOMER_MARKET] ??
                [];
            const toArray = (v: any) => (Array.isArray(v) ? v : v != null ? [v] : []);
            const normalizeList = (arr: any[]) =>
                arr.map((v: any) => String(v).trim().toLowerCase());
            const allowedCustomerTypes = normalizeList(toArray(rawCustomerTypes));
            const allowedSources = normalizeList(toArray(rawSources));
            const allowedMarkets = normalizeList(toArray(rawMarkets));

            const members = await this.memberRepo.find({
                where: { tenantId: clientSessionContext.tenantId },
            });
            const memberByName: Record<string, string> = {};
            members.forEach(m => {
                if (m.fullName) memberByName[m.fullName.trim().toLowerCase()] = m.id;
            });
            const dataRows = rows.slice(1);
            const toBool = (val: any) => {
                const s = String(val).trim().toLowerCase();
                return s === 'có' || s === 'yes' || s === 'true' || s === '1';
            };

            const customersToCreate: any[] = [];
            const addressesToCreate: any[] = [];
            const contactsToCreate: any[] = [];

            const [provinceRes, communeRes] = await Promise.all([
                this.provincesService.pagination({ pageIndex: 1, pageSize: -1 }),
                this.communesService.pagination({ pageIndex: 1, pageSize: -1 }),
            ]);
            for (let i = 0; i < dataRows.length; i++) {
                const row = dataRows[i] || [];
                const name = String(row[headerIndexMap['Tên khách hàng (*)']] ?? '').trim();
                const taxNumber = String(row[headerIndexMap['Mã số thuế (*)']] ?? '').trim();
                const phone = String(row[headerIndexMap['Số điện thoại (*)']] ?? '').trim();
                const email = String(row[headerIndexMap['Email']] ?? '').trim();
                const fax = String(row[headerIndexMap['Fax']] ?? '').trim();
                const addressName = String(row[headerIndexMap['Loại địa chỉ']] ?? '').trim();
                const provinceName = String(row[headerIndexMap['Tỉnh thành(*)']] ?? '').trim();
                const communeName = String(row[headerIndexMap['Phường xã(*)']] ?? '').trim();
                const addressDetail = String(
                    row[headerIndexMap['Địa chỉ chi tiết (*)']] ?? '',
                ).trim();
                const isDefault = toBool(row[headerIndexMap['Trụ sở chính']] ?? '');
                const customerTypeName = String(
                    row[headerIndexMap['Loại khách hàng']] ?? '',
                ).trim();
                const sourceName = String(row[headerIndexMap['Nguồn khách hàng']] ?? '').trim();
                const marketName = String(row[headerIndexMap['Thị trường']] ?? '').trim();
                const salesRepNames = String(row[headerIndexMap['Nhân viên phụ trách']] ?? '')
                    .split(',')
                    .map(s => s.trim())
                    .filter(s => s.length > 0);
                const contactName = String(row[headerIndexMap['Tên liên hệ (*)']] ?? '').trim();
                const contactPosition = String(row[headerIndexMap['Chức vụ (*)']] ?? '').trim();
                const isDecisionMaker = toBool(row[headerIndexMap['Người quyết định']] ?? '');
                const contactPhone = String(row[headerIndexMap['Số điện thoại (*)']] ?? '').trim();
                const contactEmail = String(
                    row[headerIndexMap['Email người liên hệ']] ?? '',
                ).trim();
                const missing: string[] = [];
                if (!name) missing.push('Tên khách hàng');
                if (!taxNumber) missing.push('Mã số thuế');
                if (!phone) missing.push('Số điện thoại');
                if (!provinceName) missing.push('Tỉnh thành');
                if (!communeName) missing.push('Phường xã');
                if (!addressDetail) missing.push('Địa chỉ chi tiết');
                if (!contactName) missing.push('Tên liên hệ');
                if (!contactPosition) missing.push('Chức vụ');
                if (!contactPhone) missing.push('SĐT liên hệ');
                if (missing.length > 0) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: `Thiếu dữ liệu: ${missing.join(', ')}`,
                    });
                    continue;
                }
                if (
                    customerTypeName &&
                    !allowedCustomerTypes.includes(normalize(customerTypeName))
                ) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: `Loại khách hàng không hợp lệ: ${customerTypeName}`,
                    });
                    continue;
                }
                if (sourceName && !allowedSources.includes(normalize(sourceName))) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: `Nguồn khách hàng không hợp lệ: ${sourceName}`,
                    });
                    continue;
                }
                if (marketName && !allowedMarkets.includes(normalize(marketName))) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: `Thị trường không hợp lệ: ${marketName}`,
                    });
                    continue;
                }
                // const dup = existingCustomers.find(
                //     c => (taxNumber && c.taxNumber === taxNumber) || (phone && c.phone === phone),
                // );
                // if (dup) {
                //     errorItems.push({
                //         rowNumber: i + 2,
                //         message: 'Khách hàng trùng MST/SDT',
                //     });
                //     continue;
                // }

                const provincesData = provinceRes?.data ?? [];
                const provinceItem = provincesData.find(
                    (p: any) => normalize(p?.name) === normalize(provinceName),
                );
                const provinceCode = provinceItem?.code;
                const communesData = communeRes?.data ?? [];
                const communeItem = communesData.find(
                    (c: any) =>
                        normalize(c?.name) === normalize(communeName) &&
                        (!provinceCode || c?.provinceCode === provinceCode),
                );
                const communeCode = communeItem?.code;
                if (!provinceCode) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: 'Không tìm thấy mã tỉnh ' + provinceName,
                    });
                }
                if (!communeCode) {
                    errorItems.push({
                        rowNumber: i + 2,
                        message: 'Không tìm thấy mã phường/xã ' + communeName,
                    });
                }
                if (!provinceCode || !communeCode) {
                    continue;
                }
                const salesRep: string[] = [];
                for (const nm of salesRepNames) {
                    const id = memberByName[nm.toLowerCase()];
                    if (!id) {
                        errorItems.push({
                            rowNumber: i + 2,
                            message: `Không tìm thấy nhân viên: ${nm}`,
                        });
                        continue;
                    }
                    salesRep.push(id);
                }
                if (row && row.some(cell => String(cell ?? '').trim().length > 0)) {
                    customersToCreate.push({
                        name,
                        taxNumber,
                        phone,
                        email,
                        fax,
                        customerType: customerTypeName,
                        source: sourceName,
                        market: marketName,
                        salesRep,
                        provinceCode,
                        communeCode,
                        address: `${addressDetail}, ${communeName}, ${provinceName} `,
                    });
                    addressesToCreate.push({
                        addressName,
                        address: `${addressDetail}, ${communeName}, ${provinceName} `,
                        provinceCode,
                        communeCode,
                        isDefault,
                    });
                    contactsToCreate.push({
                        name: contactName,
                        position: contactPosition,
                        phone: contactPhone,
                        email: contactEmail,
                        isDecisionMaker,
                    });
                }
            }
            if (errorItems.length > 0) {
                throw new BusinessException(
                    `Lỗi import: dòng ${errorItems.map(e => e.rowNumber).join(', ')}; ${errorItems.map(e => e.message).join(', ')}`,
                );
            }
            const { tenantId } = clientSessionContext;
            for (let i = 0; i < customersToCreate.length; i++) {
                const c = customersToCreate[i];
                const getCustomers = await this.customerRepo.find({ where: { tenantId } });
                const code = `C-00000${getCustomers.length + 1}`;
                const q = `${c.name}${c.email ?? ''}${c.phone ?? ''}`;
                const saved = await this.customerRepo.save({
                    ...c,
                    code,
                    q,
                    tenantId,
                    createdBy: clientSessionContext.memberId,
                });
                const addr = addressesToCreate[i];
                if (addr && (addr.addressName || addr.address)) {
                    await this.customerAddressRepo.insert({
                        ...addr,
                        customerId: saved.id,
                        createdBy: clientSessionContext.memberId,
                    });
                    if (addr.isDefault) {
                        await this.customerRepo.update(saved.id, {
                            address: addr.address,
                            provinceCode: addr.provinceCode,
                            communeCode: addr.communeCode,
                        });
                    }
                }
                const ct = contactsToCreate[i];
                if (ct && ct.name) {
                    await this.customerContactRepo.insert({
                        ...ct,
                        customerId: saved.id,
                        code: generateCodeHelper.generateCustomerCode('CONT'),
                        createdBy: clientSessionContext.memberId,
                    });
                }
            }
            return { message: 'Import khách hàng thành công', total: customersToCreate.length };
        } catch (error: any) {
            throw new BusinessException(error?.message || 'Upload khách hàng thất bại');
        }
    }
}
