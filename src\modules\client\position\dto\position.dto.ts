import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSPosition } from '~/common/enums/position.enum';

export class PaginationPositionDto extends PageRequest {
    @ApiProperty({ description: 'Tenant ID' })
    @IsOptional()
    tenantId?: string;

    @ApiProperty({ description: 'Position name' })
    @IsOptional()
    name?: string;

    @ApiProperty({ description: 'Position code' })
    @IsOptional()
    code?: string;

    @ApiProperty({ description: 'Department ID' })
    @IsOptional()
    departmentId?: string;

    @ApiProperty({ description: 'Position status' })
    @IsOptional()
    status?: string;

    @ApiProperty({ description: 'Position creation date' })
    @IsOptional()
    createdForm?: string;

    @ApiProperty({ description: 'Position creation date' })
    @IsOptional()
    createdTo?: string;
}

export class CreatePositionDto {
    @ApiProperty({ description: 'Tên chức vụ' })
    @IsNotEmpty({ message: 'Vui lòng nhập tên chức vụ' })
    name: string;

    @ApiProperty({ description: 'Mã chức vụ' })
    @IsNotEmpty({ message: 'Vui lòng nhập mã chức vụ' })
    code: string;

    @ApiProperty({ description: 'Mô tả chức vụ' })
    @IsOptional()
    description?: string;

    @ApiProperty({ description: 'ID phòng ban' })
    @IsNotEmpty({ message: 'Vui lòng chọn phòng ban' })
    departmentId: string;
}

export class UpdatePositionDto extends CreatePositionDto {
    @ApiProperty({ description: 'ID chức vụ' })
    @IsNotEmpty({ message: 'ID chức vụ không được để trống' })
    id: string;

    @ApiProperty({ description: 'Trạng thái chức vụ' })
    @IsNotEmpty({ message: 'Vui lòng chọn trạng thái chức vụ' })
    status: NSPosition.EStatus;
}
export class FindPositionByIdDto {
    @ApiProperty({ description: 'ID chức vụ' })
    @IsNotEmpty({ message: 'ID chức vụ không được để trống' })
    id: string;
}
