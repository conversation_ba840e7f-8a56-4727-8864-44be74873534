import { DefController, DefPost } from "ape-nestjs-typeorm3-kit";
import { DepartmentService } from "./department.service";

@DefController("department")
export class DepartmentController {
    constructor(
        private readonly departmentService: DepartmentService,
    ) {}

    // POST Create Employee
    @DefPost("sync")
    syncDepartment() {
        return this.departmentService.compareWithAuth();
    }
}
    
