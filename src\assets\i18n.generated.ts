/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "enum": {
        "NSMember": {
            "EAddressType": {
                "HOME": string;
                "COMPANY": string;
            };
            "EMembershipType": {
                "PERSONAL": string;
                "ENTERPRISE": string;
            };
            "EMemberType": {
                "MEMBER": string;
                "COLLABORATOR": string;
                "POST_OFFICE": string;
            };
            "EStatus": {
                "INACTIVE": string;
                "ACTIVE": string;
                "WAITING_FOR_VERIFY": string;
                "DELETED": string;
                "WAITING_FOR_APPROVE": string;
            };
            "EBusinessType": {
                "LED_ADVERTISEMENT": string;
                "SOCIAL_SECURITY_CARD": string;
                "TOURISM": string;
                "HEALTH": string;
                "SME360": string;
            };
            "ECustomerKind": {
                "UNKNOWN": string;
                "INDIVIDUAL": string;
                "BUSINESS": string;
            };
        };
        "NSCustomer": {
            "ECustomerType": {
                "NEW": string;
                "NURTURE": string;
                "QUALIFIED": string;
                "OPPORTUNITY": string;
                "WON": string;
                "CARE": string;
                "LOST": string;
                "ARCHIVED": string;
            };
            "EMarket": {
                "DOMESTIC": string;
                "FOREIGN": string;
            };
            "EPosition": {
                "CEO": string;
                "CTO": string;
                "CMO": string;
                "CRO": string;
                "CFO": string;
                "HR": string;
                "IT": string;
                "OTHER": string;
            };
            "ESource": {
                "WEBSITE": string;
                "FACEBOOK": string;
                "GOOGLE": string;
                "INTRODUCTION": string;
                "OTHER": string;
            };
        };
        "NSQuote": {
            "EStatus": {
                "INACTIVE": string;
                "ACTIVE": string;
                "WAITING_FOR_VERIFY": string;
                "DELETED": string;
                "WAITING_FOR_APPROVE": string;
            };
        };
        "NSCatalog": {
            "EStatus": {
                "INACTIVE": string;
                "ACTIVE": string;
            };
            "EType": {
                "SERVICE": string;
                "PRODUCT": string;
            };
        };
        "NSComplaint": {
            "EComplaintType": {
                "PRODUCT": string;
                "SERVICE": string;
                "SHIPPING": string;
                "PRICE": string;
                "OTHER": string;
            };
            "EComplaintStatus": {
                "NEW": string;
                "IN_PROGRESS": string;
                "PENDING": string;
                "RESOLVED": string;
                "CANCELLED": string;
            };
        };
    };
    "member_auth": {
        "login": {
            "error": {
                "member_not_existed": string;
                "wrong_password": string;
            };
        };
        "register": {
            "error": {
                "member_existed": string;
            };
        };
    };
    "validation": {
        "NOT_EMPTY": string;
        "INVALID_EMAIL": string;
        "INVALID_BOOLEAN": string;
        "MIN": string;
        "MAX": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
