PORT=4001
TZ=UTC
REQUEST_TIMEOUT=180000
APPLICATION_CODE='crm'
#Swagger Config
SWAGGER_TITLE="APE CRM API"
SWAGGER_DESCRIPTION="The APE CRM API"
SWAGGER_VERSION="1.0"
# Primary Database
DB_PRIMARY_TYPE="postgres"
DB_PRIMARY_HOST="localhost"
DB_PRIMARY_PORT=5432
DB_PRIMARY_USERNAME="postgres"
DB_PRIMARY_PASSWORD="Abc12345"
DB_PRIMARY_DATABASE="ape-crm-dev"
DB_PRIMARY_SYNCHRONIZE=true
DB_PRIMARY_SSL=false
DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=true
# JWT HS256 config
JWT_SECRET="/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo="
JWT_EXPIRY="100d"
JWT_REFRESH_TOKEN_SECRET="/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi="
JWT_REFRESH_TOKEN_EXPIRY="300d"
# APE AUTH
APE_SSO_URL=http://localhost:4501
APE_CLIENT_ID=ape_b808f7c5-8b29-47e0-bf3f-b9c5a7fefd61_CRM_225a43df
APE_CLIENT_SECRET=82f713e84a0c9632c57b44ccd7d0d03ddab78d08d50f5b8dbea070eb639f4611
APE_CALLBACK_URL=http://localhost:4001/api/client/auth/ape/callback
APE_PUBLIC_API_KEY='$yHvX4EN&i*GC_V()DFER4A%TR!Vx4Ya#GOq@jyL<DsXFawsS(j=txdj12KEw@'
#S3
LINK_UPLOAD_S3=ape-bl-dev
AWS_S3_BUCKET_NAME=ape-devs-co
AWS_S3_ACCESS_KEY_ID=xxxxxxxxx
AWS_S3_SECRET_ACCESS_KEY=xxxxxxxxx

# APE AUTH & SYNC
# API KEY để gọi qua AUTHENTICATOR SSO
APE_CRM_API_KEY= 'xxxxxxxxx'
# API KEY để cho AUTHENTICATOR hoặc Schedule gọi vào CRM
SCHEDULE_CRM_KEY = "e46dc3de-5334-475c-xxxxxx-xxxxxxxxx"