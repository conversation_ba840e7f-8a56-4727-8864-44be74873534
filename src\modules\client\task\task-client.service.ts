import { Injectable, NotFoundException } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { NSMember } from '~/common/enums';
import { TaskEntity } from '~/domains/primary/task/task.entity';
import { TaskRepo } from '~/domains/primary/task/task.repo';
import { clientSessionContext } from '../client-session.context';
import { CreateTaskDto } from './dtos/create-task.dto';

@Injectable()
export class TaskClientService {
    constructor(@InjectRepo(TaskRepo) private readonly taskRepo: TaskRepo) {}

    async list() {
        const { memberId, sessionData, tenantId } = clientSessionContext;
        const isMaster = sessionData?.member?.type === NSMember.EType.TENANT_MASTER;

        if (isMaster) {
            // Master: hiển thị toàn bộ task trong tenant
            return this.taskRepo.find({
                where: { tenantId },
                order: { createdDate: 'DESC' },
            });
        }

        // User thường: chỉ hiển thị task do mình tạo HOẶC được giao (đi<PERSON><PERSON> kiện OR)
        return this.taskRepo.find({
            where: [
                { createdBy: memberId, tenantId },
                { assigneeId: memberId, tenantId },
            ],
            order: { createdDate: 'DESC' },
        });
    }

    async detail(id: string) {
        const task = await this.taskRepo.findOneByTenant({
            where: { id },
        });
        if (!task) {
            throw new NotFoundException(`Task not found: ${id}`);
        }
        return task;
    }

    @DefTransaction()
    async create(dto: CreateTaskDto) {
        const { tenantId, sessionData, memberId } = clientSessionContext;
        const isMaster = sessionData?.member?.type === NSMember.EType.TENANT_MASTER;

        const task = new TaskEntity();
        task.tenantId = tenantId;
        task.name = dto.name;
        task.description = dto.description;
        task.status = dto.status;
        task.type = dto.type;
        task.priority = dto.priority;
        task.startDate = dto.startDate ? new Date(dto.startDate) : undefined;
        task.deadline = dto.deadline ? new Date(dto.deadline) : undefined;

        // Chỉ Master mới được quyền assign cho người khác
        if (isMaster && dto.assigneeId) {
            task.assigneeId = dto.assigneeId;
        } else {
            task.assigneeId = null;
        }

        task.createdBy = memberId;
        await this.taskRepo.save(task);
        return;
    }

    @DefTransaction()
    async update(id: string, dto: any) {
        const { sessionData, memberId } = clientSessionContext;
        const isMaster = sessionData?.member?.type === NSMember.EType.TENANT_MASTER;

        const task = await this.detail(id); // Check exist & tenant

        task.name = dto.name;
        task.description = dto.description;
        task.status = dto.status;
        task.type = dto.type;
        task.priority = dto.priority;
        task.startDate = dto.startDate ? new Date(dto.startDate) : undefined;
        task.deadline = dto.deadline ? new Date(dto.deadline) : undefined;
        task.updatedBy = memberId;

        // Chỉ Master mới được thay đổi người được giao
        if (isMaster) {
            // Nếu có gửi assigneeId lên thì cập nhật (kể cả null để unassign)
            if (dto.assigneeId !== undefined) {
                task.assigneeId = dto.assigneeId;
            }
        }
        // Nếu không phải Master, giữ nguyên assigneeId cũ (không update trường này)

        await this.taskRepo.update(id, task);
        return;
    }

    @DefTransaction()
    async delete(id: string) {
        const { tenantId } = clientSessionContext;
        const task = await this.taskRepo.findOne({ where: { id, tenantId } });
        if (!task) {
            throw new NotFoundException(`Task not found: ${id}`);
        }
        return this.taskRepo.delete(id);
    }
}
