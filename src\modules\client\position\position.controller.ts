import { Body, Controller, Query, UseGuards } from '@nestjs/common';
import { DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { CreatePositionDto, PaginationPositionDto, UpdatePositionDto } from './dto/position.dto';
import { PositionService } from './position.service';

@UseGuards(PermissionGuard)
@Controller('position')
export class PositionController {
    constructor(private readonly positionService: PositionService) {}

    @DefGet('pagination')
    @RequirePermissions([PERMISSION_CODES.SETTING_POSITION.VIEW])
    async pagination(@Query() dto: PaginationPositionDto) {
        return this.positionService.pagination(dto);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_POSITION.CREATE])
    async create(@Body() dto: CreatePositionDto) {
        return this.positionService.create(dto);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_POSITION.UPDATE])
    async update(@Body() dto: UpdatePositionDto) {
        return this.positionService.update(dto);
    }

    @DefPost('set-active')
    @RequirePermissions([PERMISSION_CODES.SETTING_POSITION.UPDATE])
    async setActive(@Body() dto: { id: string }) {
        return this.positionService.setActive(dto);
    }
}
