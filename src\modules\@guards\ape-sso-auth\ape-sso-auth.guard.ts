import { ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

export const GUARD_CODE = 'ape-sso';

@Injectable()
export class ApeSsoAuthGuard extends AuthGuard(GUARD_CODE) {
    canActivate(context: ExecutionContext) {
        const req = context.switchToHttp().getRequest();
        const redirectUri = req.query.redirectUri;
        if (redirectUri) {
            req.session = req.session || {};
            req.session.redirectUri = redirectUri;
        }

        return super.canActivate(context);
    }

    getAuthenticateOptions(context: ExecutionContext) {
        const req = context.switchToHttp().getRequest();
        return {
            state: req.session?.redirectUri || '',
        };
    }
}
