import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { NSApproval } from '~/common/enums/approval.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApprovalConfigStepApproverEntity } from './approval-config-step-approver.entity';
import { ApprovalRequestStepEntity } from './approval-request-step.entity';

@Entity('approval_request_step_approver')
// Index cho query listMyApprovalRequests và check quyền
@Index(['stepId', 'approverType', 'status'])
@Index(['stepId', 'memberRefId', 'status'])
@Index(['stepId', 'positionRefId', 'departmentRefId', 'status'])
@Index(['stepId', 'departmentRefId', 'status'])
@Index(['stepId', 'roleRefId', 'status'])
export class ApprovalRequestStepApproverEntity extends PrimaryBaseEntity {
    @Column({ type: 'uuid' })
    @Index()
    stepId: string;

    @ManyToOne(
        () => ApprovalRequestStepEntity,
        step => step.approvers,
        { onDelete: 'CASCADE' }, // xóa step thì xóa luôn approver runtime
    )
    @JoinColumn({ name: 'stepId' })
    step: ApprovalRequestStepEntity;

    // Tham chiếu config approver gốc (nếu cần trace)
    @Column({ type: 'uuid', nullable: true })
    @Index()
    configStepApproverId: string | null;

    @ManyToOne(() => ApprovalConfigStepApproverEntity, {
        onDelete: 'SET NULL',
    })
    @JoinColumn({ name: 'configStepApproverId' })
    configStepApprover: ApprovalConfigStepApproverEntity;

    // Lưu nguyên config (không expand)
    @Column({
        type: 'enum',
        enum: NSApproval.ApproverType,
        default: NSApproval.ApproverType.MEMBER,
    })
    @Index()
    approverType: NSApproval.ApproverType;

    // Tách biệt RefId cho từng loại (giống config)
    @Column({ type: 'uuid', nullable: true })
    @Index()
    memberRefId?: string;

    @Column({ type: 'uuid', nullable: true })
    @Index()
    positionRefId?: string;

    @Column({ type: 'uuid', nullable: true })
    @Index()
    roleRefId?: string;

    @Column({ type: 'uuid', nullable: true })
    @Index()
    departmentRefId?: string; // Cho "Position trong Department"

    // Deprecated: Giữ lại để backward compatibility
    @Column({ type: 'uuid', nullable: true })
    approverRefId?: string;

    // Trạng thái duyệt của từng người
    @Column({
        type: 'enum',
        enum: NSApproval.ApprovalStepStatus,
        default: NSApproval.ApprovalStepStatus.PENDING,
    })
    @Index()
    status: NSApproval.ApprovalStepStatus;

    @Column({ type: 'timestamptz', nullable: true })
    actedAt?: Date;

    @Column({ type: 'text', nullable: true })
    comment?: string;
}
