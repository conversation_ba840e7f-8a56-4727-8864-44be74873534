import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { NSRole } from '~/common/enums';

import { PERMISSION_CODES, PermissionCode } from '~/common/enums/permission-config.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('role')
// @Unique(['tenantId', 'name'])
export class RoleEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'Admin' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiProperty({ example: 'Quản trị viên hệ thống' })
    @Column({ type: 'varchar', length: 500, nullable: true })
    description: string;

    @ApiProperty({ example: NSRole.EStatus.ACTIVE })
    @Column({ type: 'enum', enum: NSRole.EStatus, default: NSRole.EStatus.ACTIVE })
    status: NSRole.EStatus;

    @ApiProperty({
        type: 'string',
        isArray: true,
        example: [PERMISSION_CODES.SETTING_CAMPAIGN.VIEW, PERMISSION_CODES.SETTING_CATALOG.CREATE],
    })
    @Column({ type: 'jsonb', nullable: false, default: () => "'[]'" })
    permissionCodes: PermissionCode[];
}
