import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSKPI } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('kpi_categories')
@Index('uniq_kpi_categories_tenant_key', ['tenantId', 'key'], { unique: true })
export class KpiCategoriesEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: NSKPI.ECategoryKey.CUSTOMERS_COUNT })
    @Column({ type: 'varchar', length: 64 })
    key: NSKPI.ECategoryKey;

    @ApiProperty({ example: 'Số lượng khách hàng' })
    @Column({ type: 'varchar', length: 255 })
    label: string;

    @ApiPropertyOptional({ example: 'khách' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    unit?: string;

    @ApiProperty({ example: NSKPI.ECategoryStatus.ACTIVE })
    @Column({ type: 'varchar', length: 16, default: NSKPI.ECategoryStatus.ACTIVE })
    status: NSKPI.ECategoryStatus;

    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    metadata?: object;
}
