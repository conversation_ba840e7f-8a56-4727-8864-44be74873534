import { Body } from '@nestjs/common';
import { DefController, DefPost } from 'ape-nestjs-typeorm3-kit';
import {
    GetHistoryByEntityDto,
    GetHistoryByEntityIdsDto,
    ListEventLogDto,
    LogEventDto,
} from './dto/event-log.dto';
import { EventLogsService } from './event-log.service';

@DefController('event-logs')
export class EventLogController {
    constructor(private readonly eventLogsService: EventLogsService) {}

    /**
     * <PERSON><PERSON><PERSON> lịch sử sự kiện theo ID của một thực thể
     */
    @DefPost('history')
    async getHistoryByEntity(@Body() params: GetHistoryByEntityDto) {
        return this.eventLogsService.getHistoryByEntity(params.entityType, params.entityId, {
            pageSize: params.pageSize,
            pageIndex: params.pageIndex,
        });
    }

    /**
     * <PERSON><PERSON><PERSON> lịch sử theo nhiều IDs của một loại thực thể
     */
    @DefPost('history-by-ids')
    async getHistoryByEntityIds(@Body() params: GetHistoryByEntityIdsDto) {
        return this.eventLogsService.getHistoryByEntityIds(params.entityType, params.entityIds);
    }

    /**
     * Lấy danh sách event logs với filter và phân trang
     */
    @DefPost('list')
    async list(@Body() params: ListEventLogDto) {
        return this.eventLogsService.list(params);
    }

    /**
     * Lấy thông tin tri tiết của một event log
     */
    @DefPost('detail')
    async getEventLogDetail(@Body() params: { id: string }) {
        return this.eventLogsService.getEventLogDetail(params.id);
    }

    @DefPost('create')
    async create(@Body() body: LogEventDto) {
        return this.eventLogsService.logEvent(body);
    }
}
