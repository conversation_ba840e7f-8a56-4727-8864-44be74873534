import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
export class MemberRevenueDto {
    @ApiProperty({ description: 'Doanh thu', example: 1000000 })
    @IsNumber()
    revenue: number;

    @ApiProperty({ description: 'Tên thành viên', example: 'KH001' })
    @IsString()
    memberName: string;

    @ApiProperty({ description: 'Avatar', example: 'https://example.com/avatar.jpg' })
    @IsString()
    avatar: string;

    @ApiProperty({ description: 'Trạng thái', example: 'ACTIVE' })
    @IsString()
    status: string;

    @ApiProperty({ description: 'Tổng số báo giá', example: 5 })
    @IsNumber()
    totalQuote: number;

    @ApiProperty({ description: 'Tổng số hợp đồng', example: 3 })
    @IsNumber()
    totalContract: number;
}

export class ListMemberRevenueDto extends PageRequest {
    @ApiProperty({ description: 'Ng<PERSON>y bắt đầu doanh thu (YYYY-MM-DD)', example: '2023-01-01' })
    @IsString()
    @IsOptional()
    revenueFrom?: string;

    @ApiProperty({ description: 'Ngày kết thúc doanh thu (YYYY-MM-DD)', example: '2023-12-31' })
    @IsString()
    @IsOptional()
    revenueTo?: string;

    @ApiProperty({
        description: 'Ngày bắt đầu hợp đồng (YYYY-MM-DD)',
        example: '2023-01-01',
        required: false,
    })
    @IsString()
    @IsOptional()
    dateFrom?: string;

    @ApiProperty({
        description: 'Ngày kết thúc hợp đồng (YYYY-MM-DD)',
        example: '2023-12-31',
        required: false,
    })
    @IsString()
    @IsOptional()
    dateTo?: string;

    @ApiProperty({ description: 'Tên thành viên', example: 'Công ty ABC', required: false })
    @IsString()
    @IsOptional()
    memberName?: string;

    @ApiProperty({ description: 'Trạng thái', example: 'ACTIVE', required: false })
    @IsString()
    @IsOptional()
    status?: string;
}
