import { Body, Req, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import * as express from 'express';
import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { RegisterFcmTokenDto } from './dto/fcm.dto';
import { FcmService } from './fcm.service';

@ApiTags('FCM')
@DefController('fcm')
export class FcmController {
    constructor(private readonly fcmService: FcmService) { }

    @DefPost('register-token')
    @UseGuards(ClientAuthGuard)
    @ApiOperation({ summary: 'Đăng ký FCM token cho user' })
    async registerFcmToken(
        @Req() req: express.Request & { user: any },
        @Body() dto: RegisterFcmTokenDto,
    ) {
        const userId = req.user?.sub;
        return this.fcmService.registerFcmToken(userId, dto);
    }
}