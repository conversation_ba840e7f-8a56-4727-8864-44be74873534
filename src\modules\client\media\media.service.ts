import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { nanoid } from 'nanoid';
import { MediaRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';
import { UploadService } from '../upload/upload.service';
import { ListMediaDto, MediaItemResponse } from './dto/media.dto';

@Injectable()
export class MediaService {
    constructor(
        @InjectRepo(MediaRepo) private mediaRepo: MediaRepo,
        private uploadService: UploadService,
    ) { }

    /**
     * Lấy media từ bảng media
     */
    async listMedia(params: ListMediaDto) {
        const { tenantId } = clientSessionContext;

        // Lấy từ media table
        const where: any = { tenantId };
        if (params.type) {
            where.mediaType = params.type;
        }

        // Pagination
        const pageIndex = params.pageIndex || 1;
        const pageSize = params.pageSize || 10;
        const skip = (pageIndex - 1) * pageSize;

        const [mediaItems, total] = await this.mediaRepo.findAndCount({
            where,
            order: { createdDate: 'DESC' },
            skip,
            take: pageSize,
        });

        const allMedia: MediaItemResponse[] = mediaItems.map(item => ({
            id: item.id || nanoid(),
            url: item.url,
            fileName: item.fileName,
            contentType: item.contentType,
            mediaType: item.mediaType,
            size: item.size,
            createdAt: item.createdDate || new Date(),
            entity: item.entity || 'media',
        }));

        return {
            data: allMedia,
            total: total,
        };
    }

    /**
     * Upload file và lưu vào media table
     */
    async uploadMedia(file: Express.Multer.File) {
        const { tenantId, memberId } = clientSessionContext;

        // Upload file lên S3
        const uploadResult = await this.uploadService.uploadSingle(file);

        // Tạo và lưu MediaEntity vào database
        const mediaEntity = this.mediaRepo.create({
            tenantId,
            url: uploadResult.fileUrl,
            fileName: uploadResult.fileName || file.originalname,
            contentType: uploadResult.contentType,
            mediaType: uploadResult.mediaType,
            size: file.size,
            entity: 'media',
            createdBy: memberId,
            createdDate: new Date(),
        });

        const savedMedia = await this.mediaRepo.save(mediaEntity);

        return {
            id: savedMedia.id,
            url: savedMedia.url,
            fileName: savedMedia.fileName,
            contentType: savedMedia.contentType,
            mediaType: savedMedia.mediaType,
            size: savedMedia.size,
        };
    }
}

