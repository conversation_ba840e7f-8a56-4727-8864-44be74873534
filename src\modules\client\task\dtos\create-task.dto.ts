import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { NSTask } from '~/common/enums/task.enum';

export class CreateTaskDto {
    @ApiProperty({ description: 'Tiêu đề task' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: 'Mô tả chi tiết' })
    @IsString()
    @IsOptional()
    description: string;

    @ApiPropertyOptional({ description: 'Trạng thái', enum: NSTask.EStatus })
    @IsEnum(NSTask.EStatus)
    @IsOptional()
    status: NSTask.EStatus;

    @ApiPropertyOptional({ description: 'Loại task', enum: NSTask.EType })
    @IsEnum(NSTask.EType)
    @IsOptional()
    type: NSTask.EType;

    @ApiPropertyOptional({ description: 'Độ ưu tiên', enum: NSTask.EPriority })
    @IsEnum(NSTask.EPriority)
    @IsOptional()
    priority: NSTask.EPriority;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu' })
    @IsDateString()
    @IsOptional()
    startDate: Date;

    @ApiPropertyOptional({ description: 'Hạn chót' })
    @IsDateString()
    @IsOptional()
    deadline: Date;

    @ApiPropertyOptional({ description: 'ID người được giao việc (chỉ Master mới có quyền set)' })
    @IsUUID(4)
    @IsOptional()
    assigneeId: string;
}

export class UpdateTaskDto {
    @ApiProperty()
    @IsUUID()
    id: string;

    @ApiProperty({ description: 'Tiêu đề task' })
    @IsString()
    @IsOptional()
    name: string;

    @ApiPropertyOptional({ description: 'Mô tả chi tiết' })
    @IsString()
    @IsOptional()
    description: string;

    @ApiPropertyOptional({ description: 'Trạng thái', enum: NSTask.EStatus })
    @IsEnum(NSTask.EStatus)
    @IsOptional()
    status: NSTask.EStatus;

    @ApiPropertyOptional({ description: 'Loại task', enum: NSTask.EType })
    @IsEnum(NSTask.EType)
    @IsOptional()
    type: NSTask.EType;

    @ApiPropertyOptional({ description: 'Độ ưu tiên', enum: NSTask.EPriority })
    @IsEnum(NSTask.EPriority)
    @IsOptional()
    priority: NSTask.EPriority;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu' })
    @IsDateString()
    @IsOptional()
    startDate: Date;

    @ApiPropertyOptional({ description: 'Hạn chót' })
    @IsDateString()
    @IsOptional()
    deadline: Date;

    @ApiPropertyOptional({ description: 'ID người được giao việc (chỉ Master mới có quyền set)' })
    @IsUUID(4)
    @IsOptional()
    assigneeId: string;
}
