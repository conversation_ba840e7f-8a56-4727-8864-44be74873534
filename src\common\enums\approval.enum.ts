export namespace NSApproval {
    export enum ApprovalBusinessType {
        QUOTATION = 'QUOTATION',
        CONTRACT = 'CONTRACT',
        COMPLAINT = 'COMPLAINT',
        CONTACT_CARE = 'CONTACT_CARE',
        CAMPAIGN = 'CAMPAIGN',
    }

    export const ApprovalBusinessTypeTitle: Record<NSApproval.ApprovalBusinessType, string> = {
        [ApprovalBusinessType.QUOTATION]: 'Duyệt yêu cầu báo giá',
        [ApprovalBusinessType.CONTRACT]: 'Duyệt hợp đồng',
        [ApprovalBusinessType.COMPLAINT]: 'Duyệt khiếu nại',
        [ApprovalBusinessType.CONTACT_CARE]: 'Duyệt chăm sóc khách hàng',
        [ApprovalBusinessType.CAMPAIGN]: 'Duyệt chiến dịch marketing',
    };

    // Kiểu cấp duyệt: 1 người, b<PERSON><PERSON> kỳ, tất cả
    export enum ApprovalLevelType {
        SINGLE = 'SINGLE', // Duyệt 1 người cố định
        ANY = 'ANY', // Có nhiều người, chỉ cần 1 người bất kỳ duyệt
        ALL = 'ALL', // Có nhiều người, tất cả đều phải duyệt
    }

    // Kiểu “người duyệt”: user cụ thể, role, position
    export enum ApproverType {
        MEMBER = 'MEMBER', // Duyệt theo user cụ thể (memberId)
        ROLE = 'ROLE', // Duyệt theo role
        DEPARTMENT = 'DEPARTMENT', // Duyệt theo phòng ban
        POSITION = 'POSITION', // Duyệt theo chức vụ
    }

    export const ApproverTypeTitle: Record<NSApproval.ApproverType, string> = {
        [ApproverType.MEMBER]: 'Duyệt theo user cụ thể',
        [ApproverType.ROLE]: 'Duyệt theo role',
        [ApproverType.DEPARTMENT]: 'Duyệt theo phòng ban',
        [ApproverType.POSITION]: 'Duyệt theo chức vụ',
    };

    // Trạng thái tổng của 1 request duyệt
    export enum ApprovalRequestStatus {
        PENDING = 'PENDING', // Mới tạo, chưa ai xử lý
        IN_PROGRESS = 'IN_PROGRESS', // Đang duyệt
        APPROVED = 'APPROVED', // Đã duyệt hoàn toàn
        REJECTED = 'REJECTED', // Bị từ chối
        CANCELLED = 'CANCELLED', // Hủy bởi người tạo / hệ thống
    }

    // Trạng thái của từng step trong request
    export enum ApprovalStepStatus {
        PENDING = 'PENDING', // Chưa đến lượt / chưa kích hoạt
        IN_PROGRESS = 'IN_PROGRESS', // Đang chờ duyệt
        APPROVED = 'APPROVED',
        REJECTED = 'REJECTED',
        SKIPPED = 'SKIPPED', // Bỏ qua (ví dụ do điều kiện)
    }

    // Hành động trên 1 step
    export enum ApprovalActionType {
        APPROVE = 'APPROVE',
        REJECT = 'REJECT',
        // Có thể mở rộng: RETURN, CANCEL, FORWARD, ...
    }

    // Constants cho leadtime warning
    export enum ApprovalLeadtimeWarningSetting {
        OVERDUE_ONLY = 'OVERDUE_ONLY', // Chỉ lấy các step đã quá hạn
        WARNING_AND_OVERDUE = 'WARNING_AND_OVERDUE', // Lấy cả cảnh báo và quá hạn
    }
}
