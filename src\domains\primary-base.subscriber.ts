import {
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    RemoveEvent,
    UpdateEvent,
} from 'typeorm';
import { TransactionCommitEvent } from 'typeorm/subscriber/event/TransactionCommitEvent';
import { TransactionRollbackEvent } from 'typeorm/subscriber/event/TransactionRollbackEvent';
import { TransactionStartEvent } from 'typeorm/subscriber/event/TransactionStartEvent';
import { BusinessException } from '~/@systems/exceptions';
import { NSEventLogs } from '~/common/enums/event-logs.enum';
import {
    buildCreatedDetails,
    buildUpdatedDetails,
    sanitizeObject,
} from '~/common/helpers/event-log.helper';
import { EventLogsEntity } from '~/domains/primary/event-logs/event-logs.entity';
import { clientSessionContext } from '~/modules/client/client-session.context';
import { PrimaryBaseEntity } from './primary/primary-base.entity';
//import { logger} from '';

@EventSubscriber()
export class BasePrimarySubscriber implements EntitySubscriberInterface {
    private _prevSnapshots = new Map<string, any>();
    private mapEntityType(name?: string): NSEventLogs.EntityTypes | undefined {
        switch (name) {
            case 'CampaignEntity':
                return NSEventLogs.EntityTypes.CAMPAIGN;
            case 'CatalogServiceEntity':
                return NSEventLogs.EntityTypes.CATALOG;
            case 'CatalogItemEntity':
                return NSEventLogs.EntityTypes.CATALOG_ITEM;
            case 'ContactCareEntity':
                return NSEventLogs.EntityTypes.CONTACT_CARE;
            case 'ContractEntity':
                return NSEventLogs.EntityTypes.CONTRACT;
            case 'ContractTemplateEntity':
                return NSEventLogs.EntityTypes.CONTRACT_TEMPLATE;
            case 'CustomerEntity':
                return NSEventLogs.EntityTypes.CUSTOMER;
            case 'CustomerAddressEntity':
                return NSEventLogs.EntityTypes.CUSTOMER_ADDRESS;
            case 'CustomerContactEntity':
                return NSEventLogs.EntityTypes.CUSTOMER_CONTACT;
            case 'CustomerDocumentEntity':
                return NSEventLogs.EntityTypes.CUSTOMER_DOCUMENT;
            case 'DepartmentEntity':
                return NSEventLogs.EntityTypes.DEPARTMENT;
            case 'GroupCustomerEntity':
                return NSEventLogs.EntityTypes.GROUP_CUSTOMER;
            case 'KpiCategoriesEntity':
                return NSEventLogs.EntityTypes.KPI_CATEGORIES;
            case 'KpiMemberEntity':
                return NSEventLogs.EntityTypes.KPI_MEMBER;
            case 'KpiMemberItemEntity':
                return NSEventLogs.EntityTypes.KPI_MEMBER_ITEM;
            case 'MemberEntity':
                return NSEventLogs.EntityTypes.MEMBER;
            case 'PositionEntity':
                return NSEventLogs.EntityTypes.POSITION;
            case 'QuoteEntity':
                return NSEventLogs.EntityTypes.QUOTE;
            case 'QuoteItemEntity':
                return NSEventLogs.EntityTypes.QUOTE_ITEM;
            case 'RoleEntity':
                return NSEventLogs.EntityTypes.ROLE;
            case 'SettingStringEntity':
                return NSEventLogs.EntityTypes.SETTING_STRING;
            case 'CampaignCustomerEntity':
                return NSEventLogs.EntityTypes.CAMPAIGN_CUSTOMER;
            case 'ApprovalConfigEntity':
                return NSEventLogs.EntityTypes.APPROVAL_CONFIG;
            case 'ApprovalConfigMappingEntity':
                return NSEventLogs.EntityTypes.APPROVAL_CONFIG_MAPPING;
            case 'ApprovalConfigStepEntity':
                return NSEventLogs.EntityTypes.APPROVAL_CONFIG_STEP;
            case 'ApprovalConfigStepApproverEntity':
                return NSEventLogs.EntityTypes.APPROVAL_CONFIG_STEP_APPROVER;
            case 'ApprovalRequestEntity':
                return NSEventLogs.EntityTypes.APPROVAL_REQUEST;
            case 'ApprovalRequestActionEntity':
                return NSEventLogs.EntityTypes.APPROVAL_REQUEST_ACTION;
            case 'ApprovalRequestStepEntity':
                return NSEventLogs.EntityTypes.APPROVAL_REQUEST_STEP;
            case 'ApprovalRequestStepApproverEntity':
                return NSEventLogs.EntityTypes.APPROVAL_REQUEST_STEP_APPROVER;
            case 'TaskEntity':
                return NSEventLogs.EntityTypes.TASK;
            default:
                return undefined;
        }
    }
    /**
     * Indicates that this subscriber only listen to primary events.
     */
    // listenTo() {
    //     console.log(`=====LISTEN TO PRIMARY=====`);
    //     return PrimaryBaseEntity;
    // }

    /**
     * Called after entity is loaded.
     */
    afterLoad(entity: any) {
        // console.log(`AFTER ENTITY LOADED: `);
    }

    /**
     * Called before entity insertion.
     */
    async beforeInsert(event: InsertEvent<PrimaryBaseEntity>) {
        const { entity, metadata } = event;
        //Logger.log(['BEFORE ENTITY INSERT: ', metadata?.name]);
        try {
            const tenantId = clientSessionContext?.tenantId;
            if (tenantId) {
                entity.tenantId = tenantId;
            }
        } catch (error) {}
    }
    /**
     * Called before entity update.
     */
    async beforeUpdate(event: UpdateEvent<PrimaryBaseEntity>) {
        const { metadata, manager } = event;
        const entity = event?.entity || event?.databaseEntity;
        // Logger.log([
        //     'BEFORE ENTITY UPDATE entity [',
        //     metadata?.name,
        //     '] with ID: ',
        //     entity?.id,
        // ]);
        try {
            const tenantId = clientSessionContext?.tenantId;
            if (tenantId) {
                entity.tenantId = tenantId;
            }
            const id = (entity as any)?.id;
            if (id) {
                const repo: any = manager.getRepository(metadata.target as any);
                const loaded = await repo.findOne({ where: { id } });
                if (loaded) {
                    const key = `${metadata?.name}:${id}`;
                    this._prevSnapshots.set(key, sanitizeObject(loaded as any));
                }
            }
        } catch (error) {}
    }

    async afterInsert(event: InsertEvent<PrimaryBaseEntity>) {
        const { entity, manager, metadata } = event;
        if (!entity || !entity.id) return;
        if (metadata?.name === 'EventLogsEntity') return;
        const entityType = this.mapEntityType(metadata?.name);
        if (!entityType) return;
        try {
            await manager.getRepository(EventLogsEntity).save({
                relatedEntityType: entityType,
                relatedEntityId: entity.id,
                eventType: NSEventLogs.EventTypes.CREATED,
                details: buildCreatedDetails(sanitizeObject(entity as any)),
                tenantId: clientSessionContext?.tenantId,
                createdBy: clientSessionContext?.memberId,
            } as any);
        } catch (error) {}
    }
    async afterUpdate(event: UpdateEvent<PrimaryBaseEntity>) {
        const { entity, metadata, databaseEntity, manager } = event as any;
        const target = entity || databaseEntity;
        if (!target) {
            throw new BusinessException('Entity is required');
        }
        if (!target.id) {
            return;
        }
        if (
            metadata?.name === 'EventLogsEntity' ||
            metadata?.name === 'NotificationEntity' ||
            metadata?.name === 'NotificationReceiptEntity' ||
            metadata?.name === 'AlertEntity' ||
            metadata?.name === 'ComplaintEntity' ||
            metadata?.name === 'ComplaintHistoryEntity' ||
            metadata?.name === 'ComplaintProcessEntity'
        )
            return;
        const entityType = this.mapEntityType(metadata?.name);
        if (!entityType) {
            throw new BusinessException('Entity type is not found');
        }
        const key = `${metadata?.name}:${target.id}`;
        const snap = this._prevSnapshots.get(key);
        this._prevSnapshots.delete(key);
        let beforeCandidate: any = snap ?? databaseEntity;
        if (!beforeCandidate) {
            try {
                const repo: any = manager.getRepository(metadata.target as any);
                const loaded = await repo.findOne({ where: { id: target.id } });
                if (loaded) beforeCandidate = loaded;
            } catch (error) {}
        }
        const before = sanitizeObject(beforeCandidate as any) || {};
        const after = sanitizeObject(entity as any) || {};
        const IGNORED = new Set([
            'createdDate',
            'createdBy',
            'updatedDate',
            'updatedBy',
            'version',
            'tenantId',
            'id',
        ]);
        const updatedKeys = (event?.updatedColumns || [])
            .map((c: any) => c.propertyName)
            .filter((k: string) => !IGNORED.has(k));
        const afterForDiff =
            updatedKeys.length > 0
                ? Object.fromEntries(updatedKeys.map((k: string) => [k, (after as any)[k]]))
                : after;
        const changed = this.computeChangedKeys(before as any, afterForDiff as any, IGNORED);
        if (changed.length === 0) return;
        const isOnlyStatusChange = changed.length === 1 && changed[0] === 'status';
        try {
            const payload: Partial<EventLogsEntity> = {
                relatedEntityType: entityType,
                relatedEntityId: target.id,
                tenantId: clientSessionContext?.tenantId,
                createdBy: clientSessionContext?.memberId,
            } as any;
            if (isOnlyStatusChange) {
                (payload as any).eventType = NSEventLogs.EventTypes.STATUS_CHANGE;
                (payload as any).details = {
                    fromStatus: before?.status,
                    toStatus: after?.status,
                };
            } else {
                (payload as any).eventType = NSEventLogs.EventTypes.UPDATED;
                (payload as any).details = buildUpdatedDetails(before, after, changed);
            }
            await manager.getRepository(EventLogsEntity).save(payload as any);
        } catch (error) {}
    }

    private isEmptyObject(v: any): boolean {
        return v && typeof v === 'object' && !Array.isArray(v) && Object.keys(v).length === 0;
    }

    private normalizeDiffValue(v: any): any {
        if (v === undefined || v === null) return null;
        if (v instanceof Date) return v.toISOString().slice(0, 10);
        if (this.isEmptyObject(v)) return null;
        return v;
    }

    private computeChangedKeys(
        before: Record<string, any>,
        after: Record<string, any>,
        ignored: Set<string>,
    ): string[] {
        const keys = Object.keys(after || {});
        const out: string[] = [];
        for (const k of keys) {
            if (ignored.has(k)) continue;
            const nb = this.normalizeDiffValue(before?.[k]);
            const na = this.normalizeDiffValue(after?.[k]);
            if (JSON.stringify(nb) !== JSON.stringify(na)) out.push(k);
        }
        return out;
    }

    /**
     * Called before entity removal.
     */
    beforeRemove(event: RemoveEvent<any>) {
        // console.log(`BEFORE ENTITY WITH ID ${event.entityId} REMOVED: `);
    }

    /**
     * Called after entity removal.
     */
    afterRemove(event: RemoveEvent<any>) {
        //  console.log(`AFTER ENTITY WITH ID ${event.entityId} REMOVED: `);
    }

    /**
     * Called before transaction start.
     */
    beforeTransactionStart(event: TransactionStartEvent) {
        // console.log(`BEFORE TRANSACTION STARTED: `);
    }

    /**
     * Called after transaction start.
     */
    afterTransactionStart(event: TransactionStartEvent) {
        // console.log(`AFTER TRANSACTION STARTED: `);
    }

    /**
     * Called before transaction commit.
     */
    beforeTransactionCommit(event: TransactionCommitEvent) {
        // console.log(`BEFORE TRANSACTION COMMITTED: `);
    }

    /**
     * Called after transaction commit.
     */
    afterTransactionCommit(event: TransactionCommitEvent) {
        // console.log(`AFTER TRANSACTION COMMITTED: `);
    }

    /**
     * Called before transaction rollback.
     */
    beforeTransactionRollback(event: TransactionRollbackEvent) {
        // console.log(`BEFORE TRANSACTION ROLLBACK: `);
    }

    /**
     * Called after transaction rollback.
     */
    afterTransactionRollback(event: TransactionRollbackEvent) {
        // console.log(`AFTER TRANSACTION ROLLBACK: `);
    }
}
