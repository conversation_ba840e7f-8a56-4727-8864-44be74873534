import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { EmailRepo } from '~/domains/primary/email/email.repo';
import { clientSessionContext } from '../client-session.context';
import { MailSenderFactory } from './mail-factory';
import { MailConfig, SendMailOptionsDto } from './mail.types';

@Injectable()
export class MailSenderService {
    constructor(
        private readonly factory: MailSenderFactory,
        @InjectRepo(EmailRepo) private readonly emailRepo: EmailRepo,
    ) {}

    async sendMail(options: SendMailOptionsDto): Promise<{ status: number; message?: string }> {
        try {
            const { tenantId } = clientSessionContext;
            const fromEmail = options.from;
            let cfg = null;
            if (fromEmail) {
                cfg = await this.emailRepo.findOne({ where: { tenantId, email: fromEmail } });
            }
            if (!cfg) {
                const list = await this.emailRepo.find({ where: { tenantId } });
                cfg = list[0];
            }
            if (!cfg) {
                throw new Error('<PERSON>hông tìm thấy cấu hình email để gửi');
            }
            const mailCfg: MailConfig = {
                host: cfg.host,
                port: Number(cfg.port) || 587,
                secure: false,
                user: cfg.username || '',
                pass: cfg.password || '',
                from: cfg.email,
            };
            const sender = this.factory.createFromConfig(mailCfg);
            const res = await sender.sendMail(options);
            if (res !== undefined) {
                return { status: 200, message: 'Gửi email thành công' };
            }
        } catch (error) {
            console.error('Lỗi gửi email:', error.message || error);
            throw new Error(`Không thể gửi email: ${error.message || 'Lỗi không xác định'}`);
        }
    }

    // async sendMailWithTemplate(options: SendMailWithTemplateOptionsDto): Promise<void> {
    //     const { templateName, data, ...mailOptions } = options;
    //     const html = this.renderTemplate(templateName, data);
    //     const finalOptions: SendMailOptionsDto = { ...mailOptions, html };
    //     await this.emailSender.sendMail(finalOptions);
    // }

    // private renderTemplate(templateName: string, data: object): string {
    //     const templatePath = join(__dirname, '../../../../template-mail', templateName);
    //     const templateContent = readFileSync(templatePath, 'utf8');
    //     const template = hbs.compile(templateContent);
    //     return template(data);
    // }
}
