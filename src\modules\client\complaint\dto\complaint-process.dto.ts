import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class ListComplaintProcessDto {
    @ApiPropertyOptional({ example: 'uuid complaint' })
    @IsOptional()
    @IsUUID()
    complaintId?: string;

    @ApiPropertyOptional({ example: 'uuid handler' })
    @IsOptional()
    @IsUUID()
    handlerId?: string;
}

export class CreateComplaintProcessDto {
    @ApiPropertyOptional({ example: 'uuid complaint' })
    @IsOptional()
    @IsUUID()
    complaintId?: string;

    @ApiPropertyOptional({ example: 'Mô tả xử lý' })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiPropertyOptional({ description: 'Danh sách media IDs cho ảnh' })
    @IsOptional()
    @IsString({ each: true })
    imageMediaIds?: string[];

    @ApiPropertyOptional({ description: 'Danh sách media IDs cho tài liệu' })
    @IsOptional()
    @IsString({ each: true })
    docMediaIds?: string[];
}
