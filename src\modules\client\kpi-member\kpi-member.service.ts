import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSKPI } from '~/common/enums';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { CustomerRepo } from '~/domains/primary/customer/customer.repo';
import { KpiCategoriesRepo } from '~/domains/primary/kpi-categories/kpi-categories.repo';
import { KpiMemberItemEntity } from '~/domains/primary/kpi-member-item/kpi-member-item.entity';
import { KpiMemberItemRepo } from '~/domains/primary/kpi-member-item/kpi-member-item.repo';
import { KpiMemberEntity } from '~/domains/primary/kpi-member/kpi-member.entity';
import { KpiMemberRepo } from '~/domains/primary/kpi-member/kpi-member.repo';
import { MemberRepo } from '~/domains/primary/member/member.repo';
import { clientSessionContext } from '../client-session.context';
import {
    CreateKpiMemberDto,
    ReportAverageQueryDto,
    UpdateKpiMemberDto,
} from './dto/kpi-member.dto';

type ActualsMap = Record<NSKPI.ECategoryKey, number>;
type ActualsByMemberMap = Record<string, ActualsMap>;

@Injectable()
export class KpiMemberService {
    constructor(
        @InjectRepo(KpiMemberRepo) private readonly kpiMemberRepo: KpiMemberRepo,
        @InjectRepo(KpiMemberItemRepo) private readonly itemRepo: KpiMemberItemRepo,
        @InjectRepo(CustomerRepo) private readonly customerRepo: CustomerRepo,
        @InjectRepo(ContractRepo) private readonly contractRepo: ContractRepo,
        @InjectRepo(MemberRepo) private readonly sysMemberRepo: MemberRepo,
        @InjectRepo(KpiCategoriesRepo) private readonly categoriesRepo: KpiCategoriesRepo,
    ) {}

    @DefTransaction()
    async create(dto: CreateKpiMemberDto) {
        const { tenantId, memberId } = clientSessionContext;
        const start = new Date(dto.periodStart);
        const end = new Date(dto.periodEnd);
        if (isNaN(start as any) || isNaN(end as any)) {
            throw new BusinessException('periodStart/periodEnd không hợp lệ');
        }
        if (start > end) {
            throw new BusinessException('periodStart phải nhỏ hơn hoặc bằng periodEnd');
        }

        const [latest] = await Promise.all([
            this.kpiMemberRepo.find({
                where: { tenantId },
                order: { code: 'DESC' },
                take: 1,
            }),
        ]);

        const lastCode = latest[0]?.code || '';
        const match = /^KPI(\d+)$/.exec(lastCode);
        const nextNum = match ? parseInt(match[1], 10) + 1 : 1;
        const code = `KPI${String(nextNum).padStart(4, '0')}`;

        const targetMemberIds = await this.resolveSubjectMemberIds(
            tenantId,
            dto.subjectType,
            dto.memberIds,
            dto.departmentIds,
            dto.roleIds,
        );

        const header: Partial<KpiMemberEntity> = {
            subjectType: dto.subjectType,
            memberIds: targetMemberIds,
            departmentIds: dto.departmentIds,
            roleIds: dto.roleIds,
            periodType: dto.periodType,
            periodStart: start,
            periodEnd: end,
            title: dto.title,
            note: dto.note,
            code,
            tenantId,
            createdBy: memberId,
        };

        // Validate memberWeights: each value in [0,100], per-member total <= 100
        const perMemberWeightTotalCreate: Record<string, number> = {};
        for (const it of dto.items || []) {
            const mw = (it as any).memberWeights || {};
            for (const [mid, val] of Object.entries(mw)) {
                const v = Number(val) || 0;
                if (v < 0 || v > 100) {
                    throw new BusinessException('Trọng số theo nhân viên phải trong khoảng 0-100%');
                }
                perMemberWeightTotalCreate[mid] = (perMemberWeightTotalCreate[mid] || 0) + v;
                if (perMemberWeightTotalCreate[mid] > 100) {
                    throw new BusinessException(
                        'Tổng trọng số theo nhân viên không được vượt 100%',
                    );
                }
            }
        }

        const saved = await this.kpiMemberRepo.save(header as any);
        const targetId = saved.id as string;

        const existingItems = await this.itemRepo.find({ where: { tenantId, targetId } });
        const byCategory = new Map(existingItems?.map(i => [i.categoryKey, i]));
        const seen = new Set<string>();

        for (const it of dto.items || []) {
            const found = byCategory.get(it.categoryKey);
            const payload: Partial<KpiMemberItemEntity> = {
                targetId,
                categoryKey: it.categoryKey,
                targetValue: it.targetValue,
                weight: it.weight,
                memberWeights: (it as any).memberWeights,
                tenantId,
            };
            if (found?.id) {
                await this.itemRepo.update(found.id, { ...found, ...payload });
            } else {
                await this.itemRepo.save(payload as any);
            }
            seen.add(it.categoryKey);
        }

        // Optionally: remove categories not present in input
        const toRemove = existingItems.filter(i => !seen.has(i.categoryKey));
        if (toRemove.length) await this.itemRepo.remove(toRemove);

        return { id: targetId, memberIds: targetMemberIds };
    }

    @DefTransaction()
    async update(id: string, dto: UpdateKpiMemberDto) {
        const { tenantId, memberId } = clientSessionContext;
        const start = new Date(dto.periodStart);
        const end = new Date(dto.periodEnd);
        if (isNaN(start as any) || isNaN(end as any)) {
            throw new BusinessException('periodStart/periodEnd không hợp lệ');
        }
        if (start > end) {
            throw new BusinessException('periodStart phải nhỏ hơn hoặc bằng periodEnd');
        }

        const header = await this.kpiMemberRepo.findOne({ where: { id, tenantId } });
        if (!header) throw new BusinessException('Không tìm thấy bộ KPI');

        const updMemberIds = await this.resolveSubjectMemberIds(
            tenantId,
            dto.subjectType,
            dto.memberIds,
            dto.departmentIds,
            dto.roleIds,
        );

        const newHeader: Partial<KpiMemberEntity> = {
            subjectType: dto.subjectType,
            memberIds: updMemberIds,
            departmentIds: dto.departmentIds,
            roleIds: dto.roleIds,
            periodType: dto.periodType,
            periodStart: start,
            periodEnd: end,
            title: dto.title,
            note: dto.note,
            updatedBy: memberId,
        };
        await this.kpiMemberRepo.update(id, { ...header, ...newHeader, id });

        // Validate memberWeights: each value in [0,100], per-member total <= 100
        const perMemberWeightTotalUpdate: Record<string, number> = {};
        for (const it of dto.items || []) {
            const mw = (it as any).memberWeights || {};
            for (const [mid, val] of Object.entries(mw)) {
                const v = Number(val) || 0;
                if (v < 0 || v > 100) {
                    throw new BusinessException('Trọng số theo nhân viên phải trong khoảng 0-100%');
                }
                perMemberWeightTotalUpdate[mid] = (perMemberWeightTotalUpdate[mid] || 0) + v;
                if (perMemberWeightTotalUpdate[mid] > 100) {
                    throw new BusinessException(
                        'Tổng trọng số theo nhân viên không được vượt 100%',
                    );
                }
            }
        }

        const existingItems = await this.itemRepo.find({ where: { tenantId, targetId: id } });
        const byCategory = new Map(existingItems?.map(i => [i.categoryKey, i]));
        const seen = new Set<string>();

        for (const it of dto.items || []) {
            const found = byCategory.get(it.categoryKey);
            const payload: Partial<KpiMemberItemEntity> = {
                targetId: id,
                categoryKey: it.categoryKey,
                targetValue: it.targetValue,
                weight: it.weight,
                memberWeights: (it as any).memberWeights,
                tenantId,
            };
            if (found?.id) {
                await this.itemRepo.update(found.id, { ...found, ...payload, id: found.id });
            } else {
                await this.itemRepo.save(payload as any);
            }
            seen.add(it.categoryKey);
        }

        const toRemove = existingItems.filter(i => !seen.has(i.categoryKey));
        if (toRemove.length) await this.itemRepo.remove(toRemove);

        const targetMemberIds = updMemberIds;
        return { id, memberIds: targetMemberIds };
    }

    @DefTransaction()
    async updateStatus(id: string, status: NSKPI.ETargetStatus) {
        const { tenantId } = clientSessionContext;
        const header = await this.kpiMemberRepo.findOne({ where: { id, tenantId } });
        if (!header) throw new BusinessException('Không tìm thấy bộ KPI');
        await this.kpiMemberRepo.update(id, {
            ...header,
            status,
            updatedBy: clientSessionContext.memberId,
            updatedDate: new Date(),
            id: header.id,
        } as any);
        return { id, status };
    }

    private async computeActuals(
        tenantId: string,
        memberIds: string[],
        start: Date,
        end: Date,
        categoryKeys: NSKPI.ECategoryKey[],
    ): Promise<ActualsMap> {
        const out: ActualsMap = {
            [NSKPI.ECategoryKey.CUSTOMERS_COUNT]: 0,
            [NSKPI.ECategoryKey.CONTRACTS_COUNT]: 0,
        };
        const targetMemberIds = memberIds || [];

        const whereCreatedRange = {
            tenantId,
            createdDate: Between(start, end),
        } as any;

        if (categoryKeys.includes(NSKPI.ECategoryKey.CUSTOMERS_COUNT)) {
            const customers = await this.customerRepo.find({ where: whereCreatedRange });
            out[NSKPI.ECategoryKey.CUSTOMERS_COUNT] = customers.filter(c =>
                targetMemberIds.includes(c.createdBy as any),
            ).length;
        }

        if (categoryKeys.includes(NSKPI.ECategoryKey.CONTRACTS_COUNT)) {
            const contracts = await this.contractRepo.find({ where: { tenantId } });
            const inRange = contracts.filter(c => {
                const date = c.signingDate
                    ? new Date(c.signingDate as any)
                    : (c.createdDate as any);
                return date >= start && date <= end;
            });
            out[NSKPI.ECategoryKey.CONTRACTS_COUNT] = inRange.filter(c =>
                targetMemberIds.includes(c.createdBy as any),
            ).length;
        }

        return out;
    }

    private async computeActualsByMember(
        tenantId: string,
        memberIds: string[],
        start: Date,
        end: Date,
        categoryKeys: NSKPI.ECategoryKey[],
    ): Promise<ActualsByMemberMap> {
        const targetMemberIds = memberIds || [];
        const out: ActualsByMemberMap = {};
        for (const mid of targetMemberIds) {
            out[mid] = {
                [NSKPI.ECategoryKey.CUSTOMERS_COUNT]: 0,
                [NSKPI.ECategoryKey.CONTRACTS_COUNT]: 0,
            };
        }

        const whereCreatedRange = {
            tenantId,
            createdDate: Between(start, end),
        } as any;

        if (categoryKeys.includes(NSKPI.ECategoryKey.CUSTOMERS_COUNT)) {
            const customers = await this.customerRepo.find({ where: whereCreatedRange });
            for (const c of customers) {
                const createdBy = c.createdBy as any;
                if (createdBy && targetMemberIds.includes(createdBy)) {
                    out[createdBy][NSKPI.ECategoryKey.CUSTOMERS_COUNT]++;
                }
            }
        }

        if (categoryKeys.includes(NSKPI.ECategoryKey.CONTRACTS_COUNT)) {
            const contracts = await this.contractRepo.find({ where: { tenantId } });
            for (const c of contracts) {
                const date = c.signingDate
                    ? new Date(c.signingDate as any)
                    : (c.createdDate as any);
                if (date >= start && date <= end) {
                    const createdBy = c.createdBy as any;
                    if (createdBy && targetMemberIds.includes(createdBy)) {
                        out[createdBy][NSKPI.ECategoryKey.CONTRACTS_COUNT]++;
                    }
                }
            }
        }

        return out;
    }

    async getDetail(id: string) {
        const { tenantId } = clientSessionContext;
        const header = await this.kpiMemberRepo.findOne({ where: { id, tenantId } });
        if (!header) throw new BusinessException('Không tìm thấy bộ KPI');
        const items = await this.itemRepo.find({ where: { tenantId, targetId: header.id } });

        const actualsByMember = await this.computeActualsByMember(
            tenantId,
            header.memberIds || [],
            header.periodStart,
            header.periodEnd,
            items?.map(i => i.categoryKey),
        );

        const members = await this.sysMemberRepo.find({ where: { tenantId } });
        const perMember = (header.memberIds || [])?.map(memberId => {
            const memberName = members.find(m => m.id === memberId)?.fullName;
            const rows = items?.map(i => {
                const achieved = actualsByMember[memberId]?.[i.categoryKey] ?? 0;
                const targetTotal = i.targetValue;
                const progress = targetTotal > 0 ? achieved / targetTotal : 0;
                return {
                    categoryKey: i.categoryKey,
                    targetValue: i.targetValue,
                    achievedValue: achieved,
                    progress,
                    weight: (i as any).memberWeights?.[memberId] ?? i.weight,
                } as any;
            });
            const count = rows.length || 1;
            const overallMember = rows.reduce((s, r) => s + r.progress, 0) / count;
            const itemsForOutput = rows?.map(r => ({ ...r, memberWeight: r.weight }));
            return { memberId, memberName, items: itemsForOutput, overall: overallMember };
        });

        return { header, perMember };
    }

    async list(query: {
        subjectType?: NSKPI.ESubjectType;
        memberId?: string;
        periodType?: NSKPI.EPeriodType;
        periodKey?: string;
        periodStart?: string;
        periodEnd?: string;
    }) {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };
        if (query.subjectType) where.subjectType = query.subjectType;
        if (query.periodType) where.periodType = query.periodType;
        if (query.periodStart) where.periodStart = new Date(query.periodStart);
        if (query.periodEnd) where.periodEnd = new Date(query.periodEnd);
        const rows = await this.kpiMemberRepo.find({ where });
        const members = await this.sysMemberRepo.find({
            where: {
                tenantId,
            },
        });
        const reMap = rows?.map(it => ({
            ...it,
            memberName: it.memberIds
                ?.map(id => members.find(m => m.id === id)?.fullName)
                .join(', '),
        }));
        return reMap;
    }

    async getActualsForRange(
        subjectType: NSKPI.ESubjectType,
        memberId: string,
        periodStart: Date,
        periodEnd: Date,
        categoryKeys: NSKPI.ECategoryKey[],
    ) {
        const { tenantId } = clientSessionContext;
        const targetMemberIds = await this.resolveSubjectMemberIds(
            tenantId,
            subjectType,
            subjectType === NSKPI.ESubjectType.MEMBER ? [memberId] : undefined,
            subjectType === NSKPI.ESubjectType.DEPARTMENT ? [memberId] : undefined,
            subjectType === NSKPI.ESubjectType.ROLE ? [memberId] : undefined,
        );
        return this.computeActuals(tenantId, targetMemberIds, periodStart, periodEnd, categoryKeys);
    }

    private async resolveSubjectMemberIds(
        tenantId: string,
        subjectType: NSKPI.ESubjectType,
        memberIds?: string[],
        departmentIds?: string[],
        roleIds?: string[],
    ): Promise<string[]> {
        const all = await this.sysMemberRepo.find({ where: { tenantId } });

        if (subjectType === NSKPI.ESubjectType.DEPARTMENT) {
            const set = new Set(departmentIds || []);
            return all.filter(m => m.departmentId && set.has(m.departmentId))?.map(m => m.id);
        }
        if (subjectType === NSKPI.ESubjectType.ROLE) {
            const set = new Set(roleIds || []);
            return all.filter(m => (m.roleIds || []).some(r => set.has(r)))?.map(m => m.id);
        }
        return memberIds;
    }

    async reportAverage(query: ReportAverageQueryDto) {
        const { tenantId } = clientSessionContext;
        const start = new Date(query.periodStart);
        const end = new Date(query.periodEnd);
        if (isNaN(start as any) || isNaN(end as any)) {
            throw new BusinessException('periodStart/periodEnd không hợp lệ');
        }
        if (start > end) {
            throw new BusinessException('periodStart phải nhỏ hơn hoặc bằng periodEnd');
        }
        const subjectType = query.subjectType || null;
        const limit = query.limit || 10;

        // 1. Get ALL members in tenant
        const members = await this.sysMemberRepo.find({ where: { tenantId } });

        // 2. Get APPROVED KPIs within range
        const headerWhere: any = { tenantId, status: NSKPI.ETargetStatus.APPROVED };
        if (subjectType) headerWhere.subjectType = subjectType;
        const headers = await this.kpiMemberRepo.find({ where: headerWhere });

        // Filter by date range ensuring Date objects
        const approved = headers.filter(h => {
            const hStart = new Date(h.periodStart);
            const hEnd = new Date(h.periodEnd);
            // Check valid dates
            if (isNaN(hStart.getTime()) || isNaN(hEnd.getTime())) return false;
            // Check overlap
            return hStart <= end && hEnd >= start;
        });

        const targetIds = approved.map(h => h.id!).filter(Boolean);
        const items = targetIds.length
            ? await this.itemRepo.find({ where: { tenantId, targetId: In(targetIds) } })
            : [];

        const categoryKeysSet = new Set(items.map(i => i.categoryKey));
        const categoryKeys = Array.from(categoryKeysSet) as NSKPI.ECategoryKey[];

        // 3. Initialize aggregation map for ALL members
        const memberAggMap = new Map<
            string,
            {
                totalOverall: number;
                kpiCount: number;
                categories: Map<NSKPI.ECategoryKey, { targetSum: number; achievedSum: number }>;
            }
        >();

        for (const m of members) {
            memberAggMap.set(m.id, {
                totalOverall: 0,
                kpiCount: 0,
                categories: new Map(),
            });
        }

        // 4. Process each KPI
        for (const h of approved) {
            const hId = h.id!;
            const hItems = items.filter(i => i.targetId === hId);
            const hCats = hItems.map(i => i.categoryKey);

            // Use stored memberIds directly (snapshot at creation time)
            // Ensure dates are passed as Date objects
            const hStart = new Date(h.periodStart);
            const hEnd = new Date(h.periodEnd);
            const hMemberIds = h.memberIds || [];

            // Compute actuals for this KPI
            const hActuals = await this.computeActualsByMember(
                tenantId,
                hMemberIds,
                hStart,
                hEnd,
                hCats,
            );

            for (const mId of hMemberIds) {
                const mAgg = memberAggMap.get(mId);
                if (!mAgg) continue; // Should not happen if member list is complete

                // Calculate overall for this KPI (h) for this member (mId)
                // Logic strictly follows getDetail: Unweighted average of item progress
                let sumProgress = 0;
                const itemCount = hItems.length || 1;

                for (const item of hItems) {
                    const achieved = hActuals[mId]?.[item.categoryKey] ?? 0;
                    const target = item.targetValue;
                    const progress = target > 0 ? achieved / target : 0;
                    sumProgress += progress;

                    // Aggregate category details (Summing raw values)
                    if (!mAgg.categories.has(item.categoryKey)) {
                        mAgg.categories.set(item.categoryKey, {
                            targetSum: 0,
                            achievedSum: 0,
                        });
                    }
                    const catAgg = mAgg.categories.get(item.categoryKey)!;
                    catAgg.targetSum += target;
                    catAgg.achievedSum += achieved;
                }

                const kpiOverall = sumProgress / itemCount;
                mAgg.totalOverall += kpiOverall;
                mAgg.kpiCount += 1;
            }
        }

        const activeCategories = await this.categoriesRepo.find({
            where: { tenantId },
        });
        const labelMap = new Map<NSKPI.ECategoryKey, { label: string; unit?: string }>();
        categoryKeys.forEach(k => {
            const found = activeCategories.find(c => c.key === k);
            const def = NSKPI.EDefaultCategories[k];
            labelMap.set(k, {
                label: found?.label || def?.label || String(k),
                unit: found?.unit || def?.unit,
            });
        });

        const perMember: Array<{
            memberId: string;
            memberName: string;
            overall: number;
            categories: Array<{
                key: NSKPI.ECategoryKey;
                label: string;
                unit?: string;
                targetValue: number;
                achievedValue: number;
                progress: number;
            }>;
        }> = [];

        for (const [mId, agg] of memberAggMap.entries()) {
            const memberName = members.find(m => m.id === mId)?.fullName || 'Unknown';
            // Average of overalls across all KPIs the member participated in
            const finalOverall = agg.kpiCount > 0 ? agg.totalOverall / agg.kpiCount : 0;

            const memberCategories = [];
            for (const catKey of categoryKeys) {
                const catAgg = agg.categories.get(catKey) || {
                    targetSum: 0,
                    achievedSum: 0,
                };
                const labelInfo = labelMap.get(catKey);
                // For category breakdown, we use the aggregated sums
                const progress = catAgg.targetSum > 0 ? catAgg.achievedSum / catAgg.targetSum : 0;

                memberCategories.push({
                    key: catKey,
                    label: labelInfo?.label || String(catKey),
                    unit: labelInfo?.unit,
                    targetValue: catAgg.targetSum,
                    achievedValue: catAgg.achievedSum,
                    progress,
                });
            }

            perMember.push({
                memberId: mId,
                memberName,
                overall: finalOverall,
                categories: memberCategories,
            });
        }

        // Sort by overall desc
        perMember.sort((a, b) => b.overall - a.overall);

        const top10 = perMember.slice(0, limit);

        return {
            items: perMember,
            top10,
        };
    }
}
