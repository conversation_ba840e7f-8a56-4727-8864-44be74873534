import { ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('campaign')
export class CampaignEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    code?: string;

    @ApiPropertyOptional({ example: 'Campaign 100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    name?: string;

    @ApiPropertyOptional({ example: 'Mô tả chi tiết chiến dịch' })
    @Column({ type: 'text', nullable: true })
    description?: string;

    @ApiPropertyOptional({ example: 'Template 100000' })
    @Column({ type: 'text', nullable: true })
    content?: string;

    @ApiPropertyOptional({ example: '2023-01-01 00:00:00' })
    @Column({ type: 'timestamptz', nullable: true })
    sendDate?: Date;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', nullable: true })
    mailSend?: string;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', nullable: true })
    status?: NSCampaign.EStatus;

    @ApiPropertyOptional({ example: ['123', '123'] })
    @Column({ type: 'simple-array', nullable: true })
    customerIds?: string[];

    @ApiPropertyOptional({ example: ['group-id-1', 'group-id-2'] })
    @Column({ type: 'simple-array', nullable: true })
    groupIds?: string[];
}
