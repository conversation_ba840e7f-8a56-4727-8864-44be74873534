import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSNotification } from '~/common/enums';
import { NSApproval } from '~/common/enums/approval.enum';
import { NSContractTemplate } from '~/common/enums/contract-template.enum';
import { NSContract } from '~/common/enums/contract.enum';
import { CustomerRepo, MemberRepo, QuoteRepo } from '~/domains/primary';
import { ContractTemplateRepo } from '~/domains/primary/contract-template/contract-template.repo';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { ApprovalRequestService } from '../approval/approval-request.service';
import { clientSessionContext } from '../client-session.context';
import { NotificationService } from '../notification/notification.service';
import {
    ContractTemplateDTO,
    CreateContractTemplateDto,
    PaginationTemplateQueryDto,
} from './dto/contract-template.dto';
import {
    ContractDTO,
    CreateContractDto,
    PaginationQueryDto,
    UpdateStatusDto,
} from './dto/contract.dto';

@Injectable()
export class ContractService {
    constructor(
        @InjectRepo(ContractTemplateRepo) private contractTemplateRepo: ContractTemplateRepo,
        @InjectRepo(ContractRepo) private contractRepo: ContractRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        @InjectRepo(QuoteRepo) private quoteRepo: QuoteRepo,
        private readonly approvalRequestService: ApprovalRequestService,
        private readonly notificationService: NotificationService,
    ) {}

    async createTemplate(body: CreateContractTemplateDto) {
        const { tenantId } = clientSessionContext;
        const { html, ...rest } = body;
        const existingTemplate = await this.contractTemplateRepo.findOne({
            where: { code: rest.code, tenantId },
        });
        if (existingTemplate) {
            throw new BusinessException('Mẫu hợp đồng với code này đã tồn tại');
        }

        await this.contractTemplateRepo.save({
            ...rest,
            createdAt: new Date(),
            html,
        });

        return {
            message: 'Tạo mẫu hợp đồng thành công',
        };
    }
    async pagination(query: PaginationTemplateQueryDto) {
        const { pageIndex, pageSize } = query;
        let where: any = {};
        if (query.code) {
            where.code = ILike(`%${query.code}%`);
        }
        if (query.name) {
            where.name = ILike(`%${query.name}%`);
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.dateFrom && query.dateTo) {
            where.createdDate = Between(query.dateFrom, query.dateTo);
        }
        const { tenantId } = clientSessionContext;
        const { data, total } = await this.contractTemplateRepo.findPagination(
            {
                where: { ...where, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex,
                pageSize,
            },
        );
        return {
            data,
            total,
        };
    }
    async detailTemplate(id: string) {
        const { tenantId } = clientSessionContext;
        return await this.contractTemplateRepo.findOne({
            where: {
                id,
                tenantId,
            },
        });
    }
    async updateTemplate(body: ContractTemplateDTO) {
        const { id, code, ...rest } = body;
        const { tenantId } = clientSessionContext;
        const contractTemplate = await this.contractTemplateRepo.findOne({
            where: { id, tenantId },
        });
        if (!contractTemplate) {
            throw new BusinessException('Mẫu hợp đồng không tồn tại');
        }
        const existingTemplate = await this.contractTemplateRepo.findOne({
            where: { code, tenantId },
        });
        if (existingTemplate && existingTemplate.id !== id) {
            throw new BusinessException('Mẫu hợp đồng với code này đã tồn tại');
        }
        await this.contractTemplateRepo.update(id, {
            ...rest,
            code,
            updatedDate: new Date(),
        });

        return {
            message: 'Cập nhật mẫu hợp đồng thành công',
        };
    }
    async inactiveTemplate(id: string) {
        const contractTemplate = await this.contractTemplateRepo.findOne({
            where: { id },
        });

        if (!contractTemplate) {
            throw new BusinessException('Mẫu hợp đồng không tồn tại');
        }
        await this.contractTemplateRepo.update(id, {
            status: NSContractTemplate.EStatus.INACTIVE,
            updatedDate: new Date(),
        });

        return {
            message: 'Inactivate mẫu hợp đồng thành công',
        };
    }

    async activeTemplate(id: string) {
        const contractTemplate = await this.contractTemplateRepo.findOne({
            where: { id },
        });
        if (!contractTemplate) {
            throw new BusinessException('Mẫu hợp đồng không tồn tại');
        }
        await this.contractTemplateRepo.update(id, {
            status: NSContractTemplate.EStatus.ACTIVE,
            updatedDate: new Date(),
        });

        return {
            message: 'Activate mẫu hợp đồng thành công',
        };
    }

    async paginationContract(query: PaginationQueryDto) {
        const { pageIndex, pageSize } = query;
        let where: any = {};
        if (query.code) {
            where.code = ILike(`%${query.code}%`);
        }
        if (query.name) {
            where.name = ILike(`%${query.name}%`);
        }
        if (query.status) {
            where.status = query.status;
        }

        if (query.createdDateFrom && query.createdDateTo) {
            where.createdDate = Between(query.createdDateFrom, query.createdDateTo);
        }
        if (query.signingDateFrom && query.signingDateTo) {
            where.signingDate = Between(query.signingDateFrom, query.signingDateTo);
        }
        if (query.createdBy) {
            where.createdBy = query.createdBy;
        }

        if (query.type) {
            where.type = query.type;
        }

        const { tenantId } = clientSessionContext;

        const member = await this.memberRepo.find({
            where: {
                tenantId,
            },
        });
        const customer = await this.customerRepo.find({
            where: {
                tenantId,
            },
        });
        const quote = await this.quoteRepo.find({
            where: {
                tenantId,
            },
        });

        const { data, total } = await this.contractRepo.findPagination(
            {
                where: {
                    ...where,
                    tenantId,
                },
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex,
                pageSize,
            },
        );
        const reMapData = data.map(item => ({
            ...item,
            createdByName: member?.find(m => m.id === item.createdBy)?.fullName,
            customerName: customer?.find(c => c.id === item.customerId)?.name,
            quotationNumber: quote?.find(q => q.id === item.quoteId)?.quotationNumber,
        }));

        // Enrich với thông tin approval cho từng Contract
        const dataWithApproval = await Promise.all(
            reMapData.map(async contract => {
                const approvalInfo = await this.approvalRequestService.getApprovalInfoForEntity(
                    contract.id,
                    NSApproval.ApprovalBusinessType.CONTRACT,
                );
                return {
                    ...contract,
                    approval: approvalInfo,
                };
            }),
        );

        return {
            data: dataWithApproval,
            total,
        };
    }
    async createContract(body: CreateContractDto) {
        const { tenantId, memberId } = clientSessionContext;
        const { code, name, type, quoteId, templateId, html, customerId, dueDate } = body;
        const existingContract = await this.contractRepo.findOne({
            where: { code, tenantId },
        });
        if (existingContract) {
            throw new BusinessException('Mã Hợp đồng này đã tồn tại');
        }
        const saved = await this.contractRepo.save({
            code,
            name,
            type,
            html,
            customerId,
            dueDate,
            quoteId,
            templateId,
            createdBy: memberId,
            createdAt: new Date(),
        });
        try {
            await this.notificationService.createNotification({
                source: NSNotification.ESource.CONTRACT,
                type: NSNotification.EType.CONTRACT_NEW,
                severity: NSNotification.ESeverity.LOW,
                title: `Tạo hợp đồng mới ${saved.code}`,
                content: `Hãy kiểm tra và xử lý hợp đồng này. Mô tả: ${saved.name}`,
                recipientType: NSNotification.ERecipientType.MEMBER,
                memberIds: [memberId],
                sendInApp: true,
                sendEmail: false,
                linkId: saved.id,
                linkPath: '/sales-management/contract',
            });
        } catch (error) {
            throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
        }
        try {
            await this.approvalRequestService.createRequestForDocument(
                {
                    businessType: NSApproval.ApprovalBusinessType.CONTRACT,
                    documentId: saved.id,
                    documentCode: code,
                    createdBy: memberId,
                    title: `Duyệt hợp đồng ${code}`,
                    initialStatus: NSApproval.ApprovalRequestStatus.PENDING,
                    finalStatuses: [
                        NSApproval.ApprovalRequestStatus.APPROVED,
                        NSApproval.ApprovalRequestStatus.REJECTED,
                    ],
                    approvedStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                },
                // { skipIfNoConfig: true }, // Skip nếu không có config
            );
        } catch (error) {}

        return {
            message: 'Tạo hợp đồng thành công',
        };
    }

    async updateContract(body: ContractDTO) {
        const { tenantId } = clientSessionContext;
        const { id, code, signingDate, customerId, status, dueDate, ...rest } = body;
        const contract = await this.contractRepo.findOne({
            where: { id, tenantId },
        });
        if (!contract) {
            throw new BusinessException('Hợp đồng không tồn tại');
        }
        const existingContract = await this.contractRepo.findOne({
            where: { code, tenantId },
        });
        if (existingContract && existingContract.id !== id) {
            throw new BusinessException('Hợp đồng với code này đã tồn tại');
        }
        let newSigningDate = null;
        if (status === NSContract.EStatus.SIGNED) {
            newSigningDate = new Date();
        }
        await this.contractRepo.update(id, {
            ...rest,
            signingDate: newSigningDate,
            code,
            customerId,
            status,
            dueDate,
            updatedDate: new Date(),
            id,
        });
        await this.contractRepo.findOne({ where: { id, tenantId } });

        return {
            message: 'Cập nhật hợp đồng thành công',
        };
    }

    async detailContract(id: string) {
        const { tenantId } = clientSessionContext;
        const contract = await this.contractRepo.findOne({
            where: { id, tenantId },
        });
        const customer = await this.customerRepo.findOne({
            where: { id: contract.customerId, tenantId },
        });
        if (!contract) {
            throw new BusinessException('Hợp đồng không tồn tại');
        }
        return {
            ...contract,
            customerName: customer?.name,
        };
    }

    async updateStatusContract(body: UpdateStatusDto) {
        const { tenantId } = clientSessionContext;
        const { id, status } = body;
        const contract = await this.contractRepo.findOne({
            where: { id, tenantId },
        });
        if (!contract) {
            throw new BusinessException('Hợp đồng không tồn tại');
        }
        await this.contractRepo.update(id, {
            status,
            signingDate: status === NSContract.EStatus.SIGNED ? new Date() : null,
            updatedDate: new Date(),
            id,
        });
        return {
            message: 'Cập nhật trạng thái hợp đồng thành công',
        };
    }
}
