import { Column, Entity, Index, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm'
import { PrimaryBaseEntity } from '../primary-base.entity'
import { ApprovalConfigStepEntity } from './approval-config-step.entity'
import { NSApproval } from '~/common/enums/approval.enum'
import { MemberEntity } from '../member/member.entity'
import { PositionEntity } from '../position/position.entity'
import { DepartmentEntity } from '../department/department.entity'

@Entity('approval_config_step_approver')
export class ApprovalConfigStepApproverEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  @Index()
  stepId: string

  @ManyToOne(() => ApprovalConfigStepEntity, (step) => step.approvers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'stepId' })
  step: ApprovalConfigStepEntity

  // <PERSON><PERSON><PERSON> đối tượng duyệt: MEMBER / ROLE / POSITION / DEPARTMENT
  @Column({ type: 'enum', enum: NSApproval.ApproverType })
  approverType: NSApproval.ApproverType

  // Tách biệt RefId cho từng loại (chỉ 1 trong các RefId có giá trị)
  @Column({ type: 'uuid', nullable: true })
  @Index()
  memberRefId?: string

  @Column({ type: 'uuid', nullable: true })
  @Index()
  positionRefId?: string

  @Column({ type: 'uuid', nullable: true })
  @Index()
  roleRefId?: string

  @Column({ type: 'uuid', nullable: true })
  @Index()
  departmentRefId?: string // Cho trường hợp "Position trong Department" hoặc chỉ Department

  // Flag để resolve department từ creator khi tạo request
  @Column({ type: 'boolean', default: false })
  useCreatorDepartment?: boolean // Nếu true, departmentRefId sẽ resolve từ creator's department khi tạo request

  // Foreign keys (optional, để đảm bảo tính toàn vẹn dữ liệu)
  @ManyToOne(() => MemberEntity, { nullable: true })
  @JoinColumn({ name: 'memberRefId' })
  member?: MemberEntity

  @ManyToOne(() => PositionEntity, { nullable: true })
  @JoinColumn({ name: 'positionRefId' })
  position?: PositionEntity

  @ManyToOne(() => DepartmentEntity, { nullable: true })
  @JoinColumn({ name: 'departmentRefId' })
  department?: DepartmentEntity

  // Deprecated: Giữ lại để backward compatibility với dữ liệu cũ
  @Column({ type: 'varchar', length: 50, nullable: true })
  approverRefId?: string
}
