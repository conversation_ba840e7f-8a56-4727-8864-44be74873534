// src/auth/@guards/permission.guard.ts
import { CanActivate, ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { NSMember } from '~/common/enums';
import { In } from 'typeorm';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import {
    ParentRoleCode,
    PERMISSION_CODES,
    PermissionCode,
} from '~/common/enums/permission-config.enum';
import { apeAuthApiConnector } from '~/connectors';
import { MemberRepo, RoleRepo } from '~/domains/primary';
import { clientSessionContext } from '~/modules/client/client-session.context';
import { REQUIRED_PERMISSIONS_KEY } from './require-permissions.decorator';
import { MemoryCacheHelper } from '~/@core/helpers';

type CheckFeatureCacheValue = {
    isActive: boolean;
    checkedAt: number;
};

const checkFeatureCache = new MemoryCacheHelper<CheckFeatureCacheValue>();
const CACHE_TTL_MS = 5 * 60 * 1000; // 5 phút

@Injectable()
export class PermissionGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
        private readonly memberRepo: MemberRepo,
        private readonly roleRepo: RoleRepo,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const { APE_CLIENT_ID } = configEnv();

        const requiredPermissions = this.reflector.get<PermissionCode[]>(
            REQUIRED_PERMISSIONS_KEY,
            context.getHandler(),
        );

        if (!requiredPermissions || requiredPermissions.length === 0) return true;

        const { memberId, tenantId } = clientSessionContext;
        if (!memberId) {
            throw new BusinessException('Không tìm thấy thông tin người dùng trong context.');
        }

        // Map Permission -> Parent Role
        const PERMISSION_VALUE_TO_PARENT = Object.fromEntries(
            Object.entries(PERMISSION_CODES).flatMap(([parent, group]) =>
                Object.values(group).map(v => [v, parent]),
            ),
        ) as Record<PermissionCode, ParentRoleCode>;

        const toParentRoleCodesByMap = (codes: PermissionCode[]): ParentRoleCode[] => {
            const out = new Set<ParentRoleCode>();
            codes.forEach(c => out.add(PERMISSION_VALUE_TO_PARENT[c]));
            return Array.from(out);
        };

        const parentRoleCodes = toParentRoleCodesByMap(requiredPermissions);
        const sortedParents = [...parentRoleCodes].sort();
        const cacheKey = `${tenantId}|${APE_CLIENT_ID}|${sortedParents.join(',')}`;
        const now = Date.now();

        let checkFeatures: any;
        const cached = checkFeatureCache.get(cacheKey);
        if (cached && now - cached.checkedAt < CACHE_TTL_MS) {
            checkFeatures = cached;
        } else {
            const res = await apeAuthApiConnector.post(
                '/api/public/crm/member/check-active-role',
                {
                    roleCodes: parentRoleCodes,
                    tenantId,
                    clientId: APE_CLIENT_ID,
                },
            );
            checkFeatures = res;
            checkFeatureCache.set(cacheKey, {
                isActive: !!res?.isActive,
                checkedAt: now,
            });
        }

        if (!checkFeatures.isActive) {
            throw new BusinessException(
                `Tính năng theo phân quyền ${parentRoleCodes.join(', ')} không khả dụng`,
            );
        }

        const member = await this.memberRepo.findOneByTenant({
            where: { id: memberId },
        });

        // Nếu là root, type TENANT_MASTER thì không check permission
        if (member.type === NSMember.EType.TENANT_MASTER) {
            return true;
        }
        if (!member || !member.roleIds?.length) {
            throw new HttpException('Bạn chưa được gán vai trò nào.', 403);
        }

        const roles = await this.roleRepo.find({
            where: { id: In(member.roleIds || []) },
        });

        // Gộp quyền từ các role
        const uniquePermissions = Array.from(
            new Set(roles.flatMap(r => (r.permissionCodes || []) as unknown as PermissionCode[])),
        );

        // So sánh kiểu string literal (PermissionCode)
        const hasAllRequired = requiredPermissions.every(perm => uniquePermissions.includes(perm));

        if (!hasAllRequired) {
            throw new HttpException('Bạn không có đủ quyền hạn để thực hiện hành động này.', 403);
        }

        return true;
    }
}
