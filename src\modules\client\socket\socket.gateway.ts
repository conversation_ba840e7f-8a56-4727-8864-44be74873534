import { Injectable } from '@nestjs/common';
import {
    ConnectedSocket,
    MessageBody,
    SubscribeMessage,
    WebSocketGateway,
    WebSocketServer,
} from '@nestjs/websockets';
import * as jwt from 'jsonwebtoken';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { Server, Socket } from 'socket.io';
import { configEnv } from '~/@config/env';
import { MemberRepo } from '~/domains/primary/member/member.repo';

type ClientCtx = { tenantId: string; memberId: string };

@WebSocketGateway({ namespace: '/client/socket', cors: { origin: '*' } })
@Injectable()
export class SocketGateway {
    @WebSocketServer()
    server: Server;

    private contexts = new Map<string, ClientCtx>();

    constructor(@InjectRepo(MemberRepo) private readonly memberRepo: MemberRepo) {}

    handleConnection(client: Socket) {}

    handleDisconnect(client: Socket) {
        this.contexts.delete(client.id);
    }

    @SubscribeMessage('auth')
    async onAuth(@ConnectedSocket() client: Socket, @MessageBody() data: { token: string }) {
        try {
            const secret = configEnv().JWT_SECRET;
            const payload: any = jwt.verify(data?.token || '', secret);
            const tenantId = payload?.member?.tenantId || payload?.tenantId;
            const memberId = payload?.member?.id || payload?.memberId || payload?.sub;
            if (!tenantId || !memberId) {
                client.emit('auth.error', { message: 'Invalid token payload' });
                return;
            }
            this.contexts.set(client.id, { tenantId, memberId });
            client.join(`tenant:${tenantId}`);
            client.join(`tenant:${tenantId}:member:${memberId}`);
            try {
                const member = await this.memberRepo.findOne({ where: { id: memberId, tenantId } });
                const roles: string[] = Array.isArray(member?.roleIds) ? member!.roleIds : [];
                for (const rid of roles) client.join(`tenant:${tenantId}:role:${rid}`);
                if (member?.departmentId)
                    client.join(`tenant:${tenantId}:department:${member.departmentId}`);
            } catch {}
            client.emit('auth.ok', { tenantId, memberId });
        } catch (e: any) {
            client.emit('auth.error', { message: e?.message || 'Auth failed' });
        }
    }
}
