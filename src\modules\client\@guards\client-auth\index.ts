// client/@guards/client-jwt-auth.module.ts

import { Module } from '@nestjs/common';
import { ClientAuthGuard } from './client-auth.guard';
import { ClientAuthStrategy } from './client-auth.strategy';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';

@Module({
    imports: [PrimaryRepoModule],
    providers: [ClientAuthGuard, ClientAuthStrategy],
    exports: [ClientAuthGuard],
})
export class ClientAuthModule {}
