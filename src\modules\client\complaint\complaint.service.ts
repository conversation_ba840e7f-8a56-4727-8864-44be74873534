import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSComplaint, NSMember, NSNotification } from '~/common/enums';
import { NSApproval } from '~/common/enums/approval.enum';
import { ComplaintProcessRepo, CustomerRepo, MediaRepo, MemberRepo } from '~/domains/primary';
import { ComplaintHistoryRepo } from '~/domains/primary/complaint-history/complaint-history.repo';
import { ComplaintRepo } from '~/domains/primary/complaint/complaint.repo';
import { CustomerAddressRepo } from '~/domains/primary/customer-address/customer-address.repo';
import { AlertCronService } from '../alert/alert-cron.service';
import { ApprovalRequestService } from '../approval/approval-request.service';
import { clientSessionContext } from '../client-session.context';
import { CommunesService } from '../communes/communes.service';
import { MemberService } from '../member/member.service';
import { NotificationService } from '../notification/notification.service';
import { ProvincesService } from '../provinces/provinces.service';
import { UploadService } from '../upload/upload.service';
import { ListComplaintHistoryDto } from './dto/complaint-history.dto';
import { ListComplaintProcessDto } from './dto/complaint-process.dto';
import {
    CreateCustomerComplaintDto,
    ListCustomerComplaintDto,
    UpdateCustomerComplaintDto,
} from './dto/complaint.dto';

@Injectable()
export class ComplaintService {
    constructor(
        @InjectRepo(ComplaintRepo) private complaintRepo: ComplaintRepo,
        @InjectRepo(ComplaintHistoryRepo) private complaintHistoryRepo: ComplaintHistoryRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        @InjectRepo(ComplaintProcessRepo) private complaintProcessRepo: ComplaintProcessRepo,
        @InjectRepo(MediaRepo) private mediaRepo: MediaRepo,
        private uploadService: UploadService,
        private readonly provincesService: ProvincesService,
        private readonly communesService: CommunesService,
        private readonly memberService: MemberService,
        private readonly customerAddressRepo: CustomerAddressRepo,
        private readonly alertCronService: AlertCronService,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        private readonly notificationService: NotificationService,
        private readonly approvalRequestService: ApprovalRequestService,
    ) {}

    // List
    async listComplaint(params: ListCustomerComplaintDto) {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize } = params;
        let where: any = {};
        if (params.type) {
            where.type = params.type;
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.customerId) {
            where.customerId = params.customerId;
        }
        if (params.provinceCode) {
            where.provinceCode = params.provinceCode;
        }
        if (params.communeCode) {
            where.communeCode = params.communeCode;
        }
        if (params.title) {
            where.title = ILike(`%${params.title}%`);
        }
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }
        if (params.createdDateFrom && params.createdDateTo) {
            where.createdDate = Between(params.createdDateFrom, params.createdDateTo);
        }

        if (params.dueDateFrom && params.dueDateTo) {
            where.dueDate = Between(params.dueDateFrom, params.dueDateTo);
        }
        if (params.handlerEmployeeId) {
            where.handlerEmployeeId = params.handlerEmployeeId;
        }
        if (params.followerEmployeeId) {
            where.followerEmployeeId = params.followerEmployeeId;
        }
        if (params.priority) {
            where.priority = params.priority;
        }
        if (params.createdBy) {
            where.createdBy = params.createdBy;
        }
        if (params.address) {
            where.address = ILike(`%${params.address}%`);
        }
        if (params.parentComplaintId) {
            where.parentComplaintId = params.parentComplaintId;
        }
        if (params.relationshipType) {
            where.relationshipType = params.relationshipType;
        }

        let res: any = [];
        if (pageSize === -1) {
            const [data, total] = await this.complaintRepo.findAndCount({
                where: { ...where, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                total,
                data,
            };
        } else {
            res = await this.complaintRepo.findPaginationByTenant(
                {
                    where: { ...where, tenantId },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                { pageIndex, pageSize },
            );
            const customers = await this.customerRepo.find({
                where: { tenantId },
            });

            const members = await this.memberService.pagination({
                pageSize: -1,
                pageIndex: 1,
            });

            const customerAddresses = await this.customerAddressRepo.find({
                where: { tenantId },
            });
            const reMapRes = res.data.map(item => ({
                ...item,
                parentComplaintCode: item.parentComplaintId
                    ? res.data.find(c => c.id === item.parentComplaintId)?.code
                    : null,
                customerName: customers.find(c => c.id === item.customerId)?.name,
                customerAddress: customerAddresses.filter(c => c.customerId === item.customerId),
                handlerEmployeeName: members.data.find(m => m.id === item.handlerEmployeeId)
                    ?.fullName,
                followerEmployeeName: members.data.find(m => m.id === item.followerEmployeeId)
                    ?.fullName,
            }));
            // Enrich với thông tin approval cho từng Contract
            const dataWithApproval = await Promise.all(
                reMapRes.map(async complaint => {
                    const approvalInfo = await this.approvalRequestService.getApprovalInfoForEntity(
                        complaint.id,
                        NSApproval.ApprovalBusinessType.COMPLAINT,
                    );
                    return {
                        ...complaint,
                        approval: approvalInfo,
                    };
                }),
            );
            return {
                total: res.total,
                data: dataWithApproval,
            };
        }
    }

    // List - History
    async listComplaintHistory(params: ListComplaintHistoryDto) {
        const { pageIndex, pageSize, keyword, ...rest } = params;
        let where: any = { ...rest };

        if (keyword) {
            where.note = ILike(`%${keyword}%`);
        }
        const members = await this.memberRepo.find({
            where: { tenantId: clientSessionContext.tenantId },
        });
        const res = await this.complaintHistoryRepo.findPaginationByTenant(
            {
                where,
                order: {
                    createdDate: 'DESC',
                },
            },
            { pageIndex, pageSize },
        );
        return {
            total: res.total,
            data: res.data.map(item => ({
                ...item,
                createdByName: members.find(m => m.id === item.createdBy)?.fullName,
            })),
        };
    }

    // Create
    @DefTransaction()
    async createComplaint(body: CreateCustomerComplaintDto, file: Express.Multer.File[]) {
        const { tenantId, memberId } = clientSessionContext;
        const data = JSON.parse((body as any).data);

        // Validate mediaIds nếu có
        let mediaIds: string[] = data.mediaIds || [];
        if (mediaIds && mediaIds.length > 0) {
            const mediaItems = await this.mediaRepo.find({
                where: { id: In(mediaIds), tenantId },
            });
            if (mediaItems.length !== mediaIds.length) {
                throw new BusinessException(
                    'Một số media không tồn tại hoặc không thuộc tenant này',
                );
            }
        }

        // Upload file mới và lưu vào media table để lấy mediaId
        let uploadFiles = [];
        if (file && file.length > 0) {
            for (const item of file) {
                try {
                    const itemUpload = await this.uploadService.uploadSingle(item);
                    uploadFiles.push({ file: item, uploaded: itemUpload });
                } catch (error) {
                    throw new BusinessException(error.message);
                }
            }
        }

        const customer = await this.customerRepo.findOneByTenant({
            where: { id: data.customerId, tenantId },
        });
        if (!customer) {
            throw new BusinessException('Không tìm thấy khách hàng');
        }

        // Kiểm tra khiếu nại gốc nếu có
        let parentComplaint = null;
        if (data.parentComplaintId) {
            parentComplaint = await this.complaintRepo.findOneByTenant({
                where: { id: data.parentComplaintId, tenantId },
            });
            if (!parentComplaint) {
                throw new BusinessException('Không tìm thấy khiếu nại gốc');
            }
        }

        const existingComplaint = await this.complaintRepo.find({
            where: { tenantId },
        });
        const code = `KN0000${existingComplaint.length + 1}`;
        const provinces = await this.provincesService.getProvincesByCode(data.provinceCode);
        const communes = await this.communesService.getCommunesByCode(data.communeCode);
        const address = `${data.address}, ${communes.find(c => c.code === data.communeCode)?.name}, ${provinces.find(p => p.code === data.provinceCode)?.name}`;

        let payload: any = {
            ...data,
            code,
            tenantId,
            address,
            dueDate: data.dueDate,
            createdBy: memberId,
            parentComplaintId: data.parentComplaintId || null,
            relationshipType: data.relationshipType || null,
        };

        let historyNote = '';
        let shouldCopyMedia = false;

        if (parentComplaint && data.relationshipType) {
            const parentCode = parentComplaint.code;

            switch (data.relationshipType) {
                case NSComplaint.EComplaintRelationshipType.DUPLICATE:
                    payload.status = NSComplaint.EComplaintStatus.CANCELLED;
                    payload.customerId = parentComplaint.customerId;
                    payload.addressId = parentComplaint.addressId;
                    payload.address = parentComplaint.address;
                    payload.provinceCode = parentComplaint.provinceCode;
                    payload.communeCode = parentComplaint.communeCode;
                    historyNote = `Đã đóng – Trùng lặp với ${parentCode}`;
                    shouldCopyMedia = true;
                    break;

                case NSComplaint.EComplaintRelationshipType.FOLLOW_UP:
                    payload.status = NSComplaint.EComplaintStatus.NEW;
                    if (!payload.customerId) payload.customerId = parentComplaint.customerId;
                    if (!payload.addressId) payload.addressId = parentComplaint.addressId;
                    if (!payload.address) payload.address = parentComplaint.address;
                    if (!payload.provinceCode) payload.provinceCode = parentComplaint.provinceCode;
                    if (!payload.communeCode) payload.communeCode = parentComplaint.communeCode;
                    historyNote = `Tạo case theo dõi tiếp từ ${parentCode}`;
                    break;

                case NSComplaint.EComplaintRelationshipType.RELATED_ISSUE:
                    payload.status = NSComplaint.EComplaintStatus.NEW;
                    if (!payload.customerId) payload.customerId = parentComplaint.customerId;
                    if (!payload.addressId) payload.addressId = parentComplaint.addressId;
                    if (!payload.address) payload.address = parentComplaint.address;
                    if (!payload.provinceCode) payload.provinceCode = parentComplaint.provinceCode;
                    if (!payload.communeCode) payload.communeCode = parentComplaint.communeCode;
                    historyNote = `Tạo case liên quan đến ${parentCode}`;
                    break;

                case NSComplaint.EComplaintRelationshipType.SPLIT_CASE:
                    payload.status = NSComplaint.EComplaintStatus.NEW;
                    historyNote = `Tạo case con tách từ ${parentCode}`;
                    shouldCopyMedia = true;
                    break;
            }
        }

        const res = await this.complaintRepo.save(payload);

        // Copy media từ parent complaint nếu cần
        if (shouldCopyMedia && parentComplaint) {
            const parentMedias = await this.mediaRepo.find({
                where: { entity: 'complaint', entityId: parentComplaint.id, tenantId },
            });
            // Copy media sang complaint mới
            for (const parentMedia of parentMedias) {
                const newMedia = this.mediaRepo.create({
                    tenantId,
                    url: parentMedia.url,
                    fileName: parentMedia.fileName,
                    contentType: parentMedia.contentType,
                    mediaType: parentMedia.mediaType,
                    size: parentMedia.size,
                    entity: 'complaint',
                    entityId: res.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                await this.mediaRepo.save(newMedia);
            }
        }

        // Xử lý side-effect cho Split Case
        if (
            parentComplaint &&
            data.relationshipType === NSComplaint.EComplaintRelationshipType.SPLIT_CASE
        ) {
            const childrenCount = await this.complaintRepo.count({
                where: { parentComplaintId: data.parentComplaintId, tenantId },
            });
            if (childrenCount === 1) {
                const fromStatus = parentComplaint.status;
                await this.complaintRepo.update(parentComplaint.id, {
                    status: NSComplaint.EComplaintStatus.HOLD,
                    updatedBy: memberId,
                });
                await this.complaintHistoryRepo.save({
                    complaintId: parentComplaint.id,
                    actorId: memberId,
                    fromStatus,
                    toStatus: NSComplaint.EComplaintStatus.HOLD,
                    note: 'Case gốc chuyển HOLD để tách case',
                    tenantId,
                    createdBy: memberId,
                });
            }
        }

        // Lưu vào media table cho các file mới upload với entity và entityId
        if (uploadFiles && uploadFiles.length > 0) {
            for (const { file: item, uploaded } of uploadFiles) {
                const mediaEntity = this.mediaRepo.create({
                    tenantId,
                    url: uploaded.fileUrl,
                    fileName: uploaded.fileName || item.originalname,
                    contentType: uploaded.contentType,
                    mediaType: uploaded.mediaType,
                    size: item.size,
                    entity: 'complaint',
                    entityId: res.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                await this.mediaRepo.save(mediaEntity);
            }
        }

        // Cập nhật entityId cho các mediaIds từ frontend (nếu có)
        if (mediaIds && mediaIds.length > 0) {
            await this.mediaRepo.update(
                { id: In(mediaIds), tenantId },
                { entity: 'complaint', entityId: res.id },
            );
        }

        // Ghi lịch sử tạo mối quan hệ
        if (parentComplaint && data.relationshipType) {
            await this.complaintHistoryRepo.save({
                complaintId: res.id,
                actorId: memberId,
                fromParentComplaintId: null,
                toParentComplaintId: data.parentComplaintId,
                fromRelationshipType: null,
                toRelationshipType: data.relationshipType,
                note: historyNote || 'Thiết lập quan hệ khiếu nại',
                tenantId,
                createdBy: memberId,
            });
        }

        try {
            await this.alertCronService.generateAlertForComplaint(res.id);
        } catch (error) {
            throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
        }
        try {
            await this.approvalRequestService.createRequestForDocument(
                {
                    businessType: NSApproval.ApprovalBusinessType.COMPLAINT,
                    documentId: res.id,
                    documentCode: code,
                    createdBy: memberId,
                    title: `Duyệt khiếu nại ${code}`,
                    initialStatus: NSApproval.ApprovalRequestStatus.PENDING,
                    finalStatuses: [
                        NSApproval.ApprovalRequestStatus.APPROVED,
                        NSApproval.ApprovalRequestStatus.REJECTED,
                    ],
                    approvedStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                },
                // { skipIfNoConfig: true }, // Skip nếu không có config
            );
        } catch (error) {}
        try {
            await this.notificationService.createNotification({
                source: NSNotification.ESource.COMPLAINT,
                type: NSNotification.EType.COMPLAINT_NEW,
                severity: NSNotification.ESeverity.LOW,
                title: `Xử lý khiếu nại mới ${res.code}`,
                content: `Hãy kiểm tra và xử lý khiếu nại này. Mô tả: ${data.description}`,
                recipientType: NSNotification.ERecipientType.MEMBER,
                memberIds: [data?.handlerEmployeeId, data?.followerEmployeeId],
                sendInApp: true,
                sendEmail: false,
                linkId: res.id,
                linkPath: '/customer-care/complaint',
            });
        } catch (error) {
            throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
        }

        return {
            message: 'Tạo đơn khiếu nại thành công',
        };
    }

    // Update
    @DefTransaction()
    async updateComplaint(body: UpdateCustomerComplaintDto, file: Express.Multer.File[]) {
        const data = JSON.parse((body as any).data);
        // Check id
        const { tenantId } = clientSessionContext;
        const { memberId } = clientSessionContext;
        const complaint = await this.complaintRepo.findOneByTenant({
            where: { id: data.id, tenantId },
        });

        if (!complaint) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }

        // Kiểm tra khiếu nại gốc nếu có thay đổi
        if (data.parentComplaintId && data.parentComplaintId !== complaint.parentComplaintId) {
            const parentComplaint = await this.complaintRepo.findOneByTenant({
                where: { id: data.parentComplaintId, tenantId },
            });
            if (!parentComplaint) {
                throw new BusinessException('Không tìm thấy khiếu nại gốc');
            }
        }
        // Validate mediaIds nếu có (từ frontend gửi lên)
        let mediaIds: string[] = data.mediaIds || [];
        if (mediaIds && mediaIds.length > 0) {
            const mediaItems = await this.mediaRepo.find({
                where: { id: In(mediaIds), tenantId },
            });
            if (mediaItems.length !== mediaIds.length) {
                throw new BusinessException(
                    'Một số media không tồn tại hoặc không thuộc tenant này',
                );
            }
        }

        // Upload file mới và lưu vào media table để lấy mediaId
        let uploadFiles = [];
        if (file && file.length > 0) {
            for (const item of file) {
                try {
                    const itemUpload = await this.uploadService.uploadSingle(item);
                    uploadFiles.push({ file: item, uploaded: itemUpload });
                } catch (error) {
                    throw new BusinessException(error.message);
                }
            }
        }
        const provinces = await this.provincesService.getProvincesByCode(data.provinceCode);
        const communes = await this.communesService.getCommunesByCode(data.communeCode);
        const address = `${data.address}, ${communes.find(c => c.code === data.communeCode)?.name}, ${provinces.find(p => p.code === data.provinceCode)?.name}`;
        complaint.status = data.status;
        complaint.handlerEmployeeId = data.handlerEmployeeId;
        complaint.followerEmployeeId = data.followerEmployeeId;
        complaint.priority = data.priority;
        complaint.updatedBy = memberId;
        complaint.title = data.title;
        complaint.type = data.type;
        complaint.dueDate = data.dueDate;
        complaint.address = address;
        complaint.addressId = data.addressId;
        complaint.description = data.description;
        complaint.provinceCode = data.provinceCode;
        complaint.communeCode = data.communeCode;
        complaint.updatedDate = new Date();
        complaint.customerId = data.customerId;

        // Lưu vào media table cho các file mới upload với entity và entityId
        if (uploadFiles && uploadFiles.length > 0) {
            for (const { file: item, uploaded } of uploadFiles) {
                const mediaEntity = this.mediaRepo.create({
                    tenantId,
                    url: uploaded.fileUrl,
                    fileName: uploaded.fileName || item.originalname,
                    contentType: uploaded.contentType,
                    mediaType: uploaded.mediaType,
                    size: item.size,
                    entity: 'complaint',
                    entityId: complaint.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                await this.mediaRepo.save(mediaEntity);
            }
        }

        // Cập nhật entityId cho các mediaIds từ frontend (nếu có)
        if (mediaIds && mediaIds.length > 0) {
            await this.mediaRepo.update(
                { id: In(mediaIds), tenantId },
                { entity: 'complaint', entityId: complaint.id },
            );
        }

        // Ghi lại lịch sử thay đổi liên kết nếu có
        if (
            data.parentComplaintId !== complaint.parentComplaintId ||
            data.relationshipType !== complaint.relationshipType
        ) {
            let noteDetail = '';
            let fromComplaintCode = '';
            let toComplaintCode = '';

            // Lấy mã khiếu nại nếu có thay đổi về khiếu nại gốc
            if (data.parentComplaintId !== complaint.parentComplaintId) {
                if (complaint.parentComplaintId) {
                    const fromComplaint = await this.complaintRepo.findOne({
                        where: { id: complaint.parentComplaintId, tenantId },
                        select: ['code'],
                    });
                    fromComplaintCode = fromComplaint?.code || complaint.parentComplaintId;
                }
                if (data.parentComplaintId) {
                    const toComplaint = await this.complaintRepo.findOne({
                        where: { id: data.parentComplaintId, tenantId },
                        select: ['code'],
                    });
                    toComplaintCode = toComplaint?.code || data.parentComplaintId;
                }

                if (!complaint.parentComplaintId && data.parentComplaintId) {
                    noteDetail += `Liên kết với khiếu nại gốc: ${toComplaintCode}`;
                } else if (complaint.parentComplaintId && !data.parentComplaintId) {
                    noteDetail += `Hủy liên kết với khiếu nại gốc: ${fromComplaintCode}`;
                } else if (complaint.parentComplaintId && data.parentComplaintId) {
                    noteDetail += `Thay đổi khiếu nại gốc từ: ${fromComplaintCode} → ${toComplaintCode}`;
                }
            }

            // Xác định thay đổi về loại quan hệ
            if (data.relationshipType !== complaint.relationshipType) {
                if (noteDetail) noteDetail += '. ';
                if (!complaint.relationshipType && data.relationshipType) {
                    noteDetail += `Thiết lập loại quan hệ: ${data.relationshipType}`;
                } else if (complaint.relationshipType && !data.relationshipType) {
                    noteDetail += `Xóa loại quan hệ: ${complaint.relationshipType}`;
                } else if (complaint.relationshipType && data.relationshipType) {
                    noteDetail += `Thay đổi loại quan hệ từ: ${complaint.relationshipType} → ${data.relationshipType}`;
                }
            }

            await this.complaintHistoryRepo.save({
                complaintId: data.id,
                actorId: memberId,
                fromParentComplaintId: complaint.parentComplaintId,
                toParentComplaintId: data.parentComplaintId,
                fromRelationshipType: complaint.relationshipType,
                toRelationshipType: data.relationshipType,
                note: noteDetail || 'Cập nhật mối quan hệ khiếu nại',
                tenantId,
                createdBy: memberId,
            });
        }

        complaint.parentComplaintId = data.parentComplaintId || null;
        complaint.relationshipType = data.relationshipType || null;
        await this.complaintRepo.update(data.id, complaint);
        // Create alert
        await this.alertCronService.generateAlertForComplaint(complaint.id);
        return {
            message: 'Cập nhật đơn khiếu nại thành công',
        };
    }

    async detailComplaint(id: string) {
        const { tenantId } = clientSessionContext;
        const res = await this.complaintRepo.findOne({ where: { id, tenantId } });
        if (!res) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }
        const customer = await this.customerRepo.findOneByTenant({
            where: { id: res.customerId, tenantId },
        });
        const members = await this.memberService.pagination({
            pageSize: -1,
            pageIndex: 1,
        });
        const provinces = await this.provincesService.getProvincesByCode(res.provinceCode);
        const communes = await this.communesService.getCommunesByCode(res.communeCode);

        // Lấy thông tin khiếu nại liên quan
        let parentComplaint = null;
        if (res.parentComplaintId) {
            parentComplaint = await this.complaintRepo.findOne({
                where: { id: res.parentComplaintId, tenantId },
            });
        }

        const childComplaints = await this.complaintRepo.find({
            where: { parentComplaintId: id, tenantId },
            order: { createdDate: 'DESC' },
        });

        // Fetch URLs từ media table dựa trên entity và entityId
        const mediaItems = await this.mediaRepo.find({
            where: { entity: 'complaint', entityId: id, tenantId },
        });
        const imageUrls = mediaItems.map(m => m.url);

        return {
            ...res,
            imageUrls, // Trả về URLs từ relations
            provinceName: provinces.find(p => p.code === res.provinceCode)?.name,
            communeName: communes.find(c => c.code === res.communeCode)?.name,
            customerName: customer?.name,
            handlerEmployeeName: members.data.find(m => m.id === res.handlerEmployeeId)?.fullName,
            followerEmployeeName: members.data.find(m => m.id === res.followerEmployeeId)?.fullName,
            parentComplaint: parentComplaint,
            childComplaints: childComplaints,
        };
    }

    async deleteMediaId(id: string, mediaId: string) {
        const { tenantId } = clientSessionContext;
        const complaint = await this.complaintRepo.findOne({
            where: { id, tenantId },
        });
        if (!complaint) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }
        // Xóa media bằng cách xóa entityId
        await this.mediaRepo.update(
            { id: mediaId, entityId: id, entity: 'complaint', tenantId },
            { entityId: null, entity: null },
        );
        return {
            message: 'Xóa media thành công',
        };
    }

    async updateStatus(id: string, status: NSComplaint.EComplaintStatus) {
        const { tenantId, memberId } = clientSessionContext;
        const complaint = await this.complaintRepo.findOne({
            where: { id, tenantId },
        });
        if (!complaint) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }
        const fromStatus = complaint.status;
        complaint.status = status;
        complaint.updatedBy = memberId;
        await this.complaintRepo.update(id, complaint);

        // Ghi lại lịch sử thay đổi trạng thái
        await this.complaintHistoryRepo.save({
            complaintId: id,
            actorId: memberId,
            fromStatus,
            toStatus: status,
            note: `Cập nhật trạng thái khiếu nại từ ${NSComplaint.EComplaintStatus[fromStatus]} thành ${NSComplaint.EComplaintStatus[status]}`,
            tenantId,
            createdBy: memberId,
        });

        await this.alertCronService.generateAlertForComplaint(complaint.id);
        return {
            message: 'Cập nhật trạng thái thành công',
        };
    }

    async finalizeSplitCase(parentComplaintId: string, finalStatus: NSComplaint.EComplaintStatus) {
        const { tenantId, memberId } = clientSessionContext;
        const parent = await this.complaintRepo.findOne({
            where: { id: parentComplaintId, tenantId },
        });
        if (!parent) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại gốc');
        }
        if (
            finalStatus !== NSComplaint.EComplaintStatus.RESOLVED &&
            finalStatus !== NSComplaint.EComplaintStatus.CANCELLED
        ) {
            throw new BusinessException('Trạng thái kết thúc không hợp lệ');
        }
        const children = await this.complaintRepo.find({
            where: { parentComplaintId, tenantId },
            order: { createdDate: 'ASC' },
        });
        const fromStatus = parent.status;
        await this.complaintRepo.update(parentComplaintId, {
            status: finalStatus,
            updatedBy: memberId,
        });
        const childCodes = children
            .map(c => c.code)
            .filter(Boolean)
            .join(', ');
        const note = childCodes
            ? `Case gốc đã được tách thành: ${childCodes}`
            : 'Case gốc đã được tách và đóng lại';
        await this.complaintHistoryRepo.save({
            complaintId: parentComplaintId,
            actorId: memberId,
            fromStatus,
            toStatus: finalStatus,
            note,
            tenantId,
            createdBy: memberId,
        });
        await this.alertCronService.generateAlertForComplaint(parentComplaintId);
        return { message: 'Đóng case gốc sau khi tách thành công' };
    }

    // Lấy danh sách khiếu nại liên quan
    async getRelatedComplaints(id: string) {
        const { tenantId } = clientSessionContext;

        // Lấy thông tin khiếu nại hiện tại
        const currentComplaint = await this.complaintRepo.findOne({
            where: { id, tenantId },
        });

        if (!currentComplaint) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }

        // Lấy khiếu nại gốc nếu có
        let parentComplaint = null;
        if (currentComplaint.parentComplaintId) {
            parentComplaint = await this.complaintRepo.findOne({
                where: { id: currentComplaint.parentComplaintId, tenantId },
            });
        }

        // Lấy danh sách khiếu nại con
        const childComplaints = await this.complaintRepo.find({
            where: { parentComplaintId: id, tenantId },
            order: { createdDate: 'DESC' },
        });

        return {
            currentComplaint,
            parentComplaint,
            childComplaints,
        };
    }

    // Tạo lần xử lý khiếu nại mới
    @DefTransaction()
    async createComplaintProcess(body: FormData, file: Express.Multer.File[]) {
        const { tenantId, memberId } = clientSessionContext;
        const data = JSON.parse((body as any).data);

        // Kiểm tra khiếu nại tồn tại
        const complaint = await this.complaintRepo.findOneByTenant({
            where: { id: data.complaintId, tenantId },
        });
        if (!complaint) {
            throw new BusinessException('Không tìm thấy đơn khiếu nại');
        }

        const member = await this.memberRepo.findOneByTenant({
            where: { id: memberId, tenantId },
        });

        // Kiểm tra quyền xử lý
        if (member.type !== NSMember.EType.TENANT_MASTER) {
            if (complaint.handlerEmployeeId !== memberId) {
                throw new BusinessException('Bạn không có quyền xử lý khiếu nại này');
            }
        }

        // Validate mediaIds nếu có (từ frontend gửi lên)
        let imageMediaIds: string[] = data.imageMediaIds || [];
        let docMediaIds: string[] = data.docMediaIds || [];

        if (imageMediaIds.length > 0) {
            const imageMediaItems = await this.mediaRepo.find({
                where: { id: In(imageMediaIds), tenantId },
            });
            if (imageMediaItems.length !== imageMediaIds.length) {
                throw new BusinessException(
                    'Một số media ảnh không tồn tại hoặc không thuộc tenant này',
                );
            }
        }

        if (docMediaIds.length > 0) {
            const docMediaItems = await this.mediaRepo.find({
                where: { id: In(docMediaIds), tenantId },
            });
            if (docMediaItems.length !== docMediaIds.length) {
                throw new BusinessException(
                    'Một số media tài liệu không tồn tại hoặc không thuộc tenant này',
                );
            }
        }

        // Upload file mới và lưu vào media table để lấy mediaId
        const uploadedFiles: Array<{ file: Express.Multer.File; uploaded: any }> = [];
        if (file && file.length > 0) {
            for (const item of file) {
                try {
                    const uploaded = await this.uploadService.uploadSingle(item);
                    uploadedFiles.push({ file: item, uploaded });
                } catch (error) {
                    throw new BusinessException(error.message);
                }
            }
        }

        // Lấy số thứ tự xử lý
        const lastProcess = await this.complaintProcessRepo.find({
            where: { complaintId: data.complaintId, tenantId },
        });
        const nextProcessNumber = lastProcess?.length + 1;

        // Tạo bản ghi xử lý
        const process = this.complaintProcessRepo.create({
            complaintId: data.complaintId,
            handlerId: memberId,
            description: data.description,
            processNumber: nextProcessNumber,
            tenantId,
            createdBy: memberId,
        });
        const savedProcess = await this.complaintProcessRepo.save(process);

        // Lưu vào media table cho các file mới upload với entity và entityId
        if (uploadedFiles && uploadedFiles.length > 0) {
            for (const { file: item, uploaded } of uploadedFiles) {
                const mediaEntity = this.mediaRepo.create({
                    tenantId,
                    url: uploaded.fileUrl,
                    fileName: uploaded.fileName || item.originalname,
                    contentType: uploaded.contentType,
                    mediaType: uploaded.mediaType,
                    size: item.size,
                    entity: 'complaint_process',
                    entityId: savedProcess.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                await this.mediaRepo.save(mediaEntity);
            }
        }

        // Cập nhật entityId cho các mediaIds từ frontend (nếu có)
        if (imageMediaIds && imageMediaIds.length > 0) {
            await this.mediaRepo.update(
                { id: In(imageMediaIds), tenantId },
                { entity: 'complaint_process', entityId: savedProcess.id },
            );
        }
        if (docMediaIds && docMediaIds.length > 0) {
            await this.mediaRepo.update(
                { id: In(docMediaIds), tenantId },
                { entity: 'complaint_process', entityId: savedProcess.id },
            );
        }

        // Ghi lại lịch sử
        await this.complaintHistoryRepo.save({
            complaintId: data.complaintId,
            actorId: memberId,
            note: `Đã xử lý khiếu nại lần thứ ${nextProcessNumber}`,
            tenantId,
            createdBy: memberId,
        });

        return {
            message: 'Xử lý khiếu nại thành công',
            data: savedProcess,
        };
    }

    // Lấy danh sách các lần xử lý
    async listComplaintProcess(params: ListComplaintProcessDto) {
        const { tenantId } = clientSessionContext;
        const { complaintId } = params;

        if (!complaintId) {
            throw new BusinessException('complaintId là bắt buộc');
        }

        const processes = await this.complaintProcessRepo.find({
            where: { complaintId, tenantId },
            order: { processNumber: 'DESC' },
        });

        // Lấy thông tin handler
        const handlerIds = processes.map(p => p.handlerId).filter(Boolean);
        const handlers = await this.memberRepo.find({
            where: { id: In(handlerIds) },
            select: ['id', 'fullName'],
        });

        // Fetch URLs từ media table cho tất cả processes dựa trên entity và entityId
        const processIds = processes.map(p => p.id);
        const allMediaItems =
            processIds.length > 0
                ? await this.mediaRepo.find({
                      where: { entity: 'complaint_process', entityId: In(processIds), tenantId },
                  })
                : [];

        // Group media by processId và phân loại image/doc
        const mediaByProcess = new Map<string, { images: any[]; docs: any[] }>();
        allMediaItems.forEach(media => {
            if (!mediaByProcess.has(media.entityId)) {
                mediaByProcess.set(media.entityId, { images: [], docs: [] });
            }
            const processMedia = mediaByProcess.get(media.entityId);
            if (media.contentType && media.contentType.startsWith('image/')) {
                processMedia.images.push(media);
            } else {
                processMedia.docs.push(media);
            }
        });

        const result = processes.map(process => {
            const processMedia = mediaByProcess.get(process.id) || { images: [], docs: [] };
            const imageUrls = processMedia.images.map(m => m.url);
            const docUrls = processMedia.docs.map(m => m.url);
            const docNames = processMedia.docs.map(m => m.fileName || m.url.split('/').pop() || '');

            return {
                ...process,
                imageUrls,
                docUrls,
                docNames,
                handlerName: handlers.find(h => h.id === process.handlerId)?.fullName || 'N/A',
                handlerEmail: 'N/A',
            };
        });

        return {
            total: result.length,
            data: result,
        };
    }
}
