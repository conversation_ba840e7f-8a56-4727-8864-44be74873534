import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  IsNotEmpty,
  IsObject,
  IsDateString,
} from 'class-validator';
import { NSComplaint } from '~/common/enums/complaint.enum';
import { PageRequest } from '~/@systems/utils';

export class CreateComplaintHistoryDto {
  @ApiProperty({ example: 'uuid complaint' })
  @IsUUID()
  @IsNotEmpty()
  complaintId: string;

  @ApiPropertyOptional({ example: 'uuid actor' })
  @IsOptional()
  @IsUUID()
  actorId?: string;

  @ApiPropertyOptional({ enum: NSComplaint.EComplaintStatus })
  @IsOptional()
  @IsEnum(NSComplaint.EComplaintStatus)
  fromStatus?: NSComplaint.EComplaintStatus;

  @ApiPropertyOptional({ enum: NSComplaint.EComplaintStatus })
  @IsOptional()
  @IsEnum(NSComplaint.EComplaintStatus)
  toStatus?: NSComplaint.EComplaintStatus;

  @ApiPropertyOptional({ example: 'Đã gọi khách hàng xác nhận' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({
    example: { patch: { handlerEmployeeId: '...' } },
    description: 'Metadata bổ sung (JSON)',
  })
  @IsOptional()
  @IsObject()
  meta?: Record<string, any>;

  @ApiPropertyOptional({ description: 'ID khiếu nại gốc cũ' })
  @IsOptional()
  @IsString()
  fromParentComplaintId?: string;

  @ApiPropertyOptional({ description: 'ID khiếu nại gốc mới' })
  @IsOptional()
  @IsString()
  toParentComplaintId?: string;

  @ApiPropertyOptional({ description: 'Loại quan hệ cũ' })
  @IsOptional()
  @IsString()
  fromRelationshipType?: string;

  @ApiPropertyOptional({ description: 'Loại quan hệ mới' })
  @IsOptional()
  @IsString()
  toRelationshipType?: string;
}

export class UpdateComplaintHistoryDto extends PartialType(CreateComplaintHistoryDto) {
  @ApiProperty({ example: 'uuid complaint history' })
  @IsUUID()
  @IsNotEmpty()
  id: string;
}

export class ListComplaintHistoryDto extends PageRequest {
  @ApiPropertyOptional({ example: 'uuid complaint' })
  @IsOptional()
  @IsUUID()
  complaintId?: string;

  @ApiPropertyOptional({ example: 'uuid actor' })
  @IsOptional()
  @IsUUID()
  actorId?: string;

  @ApiPropertyOptional({ enum: NSComplaint.EComplaintStatus, isArray: true })
  @IsOptional()
  status?: NSComplaint.EComplaintStatus[];

  @ApiPropertyOptional({ description: 'Từ ngày' })
  @IsOptional()
  @IsDateString()
  createdDateFrom?: string;

  @ApiPropertyOptional({ description: 'Đến ngày' })
  @IsOptional()
  @IsDateString()
  createdDateTo?: string;

  @ApiPropertyOptional({ description: 'Tìm kiếm theo ghi chú' })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: 'Lọc theo khiếu nại gốc cũ' })
  @IsOptional()
  @IsString()
  fromParentComplaintId?: string;

  @ApiPropertyOptional({ description: 'Lọc theo khiếu nại gốc mới' })
  @IsOptional()
  @IsString()
  toParentComplaintId?: string;

  @ApiPropertyOptional({ description: 'Lọc theo loại quan hệ cũ' })
  @IsOptional()
  @IsString()
  fromRelationshipType?: string;

  @ApiPropertyOptional({ description: 'Lọc theo loại quan hệ mới' })
  @IsOptional()
  @IsString()
  toRelationshipType?: string;
}
