import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSKPI } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('kpi_member')
@Index(
    'uniq_kpi_member_subject_period',
    [
        'tenantId',
        'subjectType',
        'memberIds',
        'departmentIds',
        'roleIds',
        'periodType',
        'periodStart',
        'periodEnd',
    ],
    {
        unique: true,
    },
)
@Index('uniq_kpi_member_tenant_code', ['tenantId', 'code'], { unique: true })
export class KpiMemberEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional({ example: 'KPI0001' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    code?: string;

    @ApiProperty({ example: NSKPI.ESubjectType.MEMBER })
    @Column({ type: 'varchar', length: 16 })
    subjectType: NSKPI.ESubjectType;

    @ApiProperty({ example: ['member-id-1', 'member-id-2'] })
    @Column({ type: 'text', array: true, nullable: true, default: '{}' })
    memberIds?: string[];

    @ApiProperty({ example: NSKPI.EPeriodType.MONTH })
    @Column({ type: 'varchar', length: 16 })
    periodType: NSKPI.EPeriodType;

    @ApiProperty()
    @Column({ type: 'timestamptz' })
    periodStart: Date;

    @ApiProperty()
    @Column({ type: 'timestamptz' })
    periodEnd: Date;

    @ApiProperty({ example: NSKPI.ETargetStatus.NEW })
    @Column({
        type: 'varchar',
        length: 16,
        default: NSKPI.ETargetStatus.NEW,
        enum: NSKPI.ETargetStatus,
    })
    status: NSKPI.ETargetStatus;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 255, nullable: true })
    title?: string;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 255, nullable: true })
    note?: string;

    @ApiPropertyOptional({ example: ['department-id-1', 'department-id-2'] })
    @Column({ type: 'text', array: true, nullable: true, default: '{}' })
    departmentIds?: string[];

    @ApiPropertyOptional({ example: ['role-id-1', 'role-id-2'] })
    @Column({ type: 'text', array: true, nullable: true, default: '{}' })
    roleIds?: string[];
}
