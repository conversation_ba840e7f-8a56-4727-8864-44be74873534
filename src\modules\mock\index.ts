import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { REFIX_MODULE } from '../config-module';
import { lazyLoadClasses } from 'nestjs-typeorm3-kit';

const controllers = lazyLoadClasses(__dirname, ['.controller']);
const services = lazyLoadClasses(__dirname, ['.service']);

@ChildModule({
    prefix: REFIX_MODULE.mock,
    providers: [...services],
    controllers: [...controllers],
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
