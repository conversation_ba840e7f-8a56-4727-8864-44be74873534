import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { NSKPI } from '~/common/enums';
import { KpiCategoriesEntity } from '~/domains/primary/kpi-categories/kpi-categories.entity';
import { KpiCategoriesRepo } from '~/domains/primary/kpi-categories/kpi-categories.repo';
import { clientSessionContext } from '../client-session.context';

@Injectable()
export class KpiCategoriesService {
    constructor(@InjectRepo(KpiCategoriesRepo) private readonly repo: KpiCategoriesRepo) {}

    async getDefaultsMerged(): Promise<Partial<KpiCategoriesEntity>[]> {
        const { tenantId } = clientSessionContext;
        const existing = await this.repo.find({ where: { tenantId } });
        const map = new Map(existing.map(e => [e.key, e]));

        return Object.entries(NSKPI.EDefaultCategories).map(([key, def]) => {
            const ov = map.get(key as NSKPI.ECategoryKey);
            return {
                key: key as NSKPI.ECategoryKey,
                label: ov?.label ?? def.label,
                unit: ov?.unit ?? def.unit,
                status: ov?.status ?? NSKPI.ECategoryStatus.ACTIVE,
            };
        });
    }

    async getActiveCategories(): Promise<Partial<KpiCategoriesEntity>[]> {
        const merged = await this.getDefaultsMerged();
        return merged.filter(m => m.status === NSKPI.ECategoryStatus.ACTIVE);
    }

    @DefTransaction()
    async upsert(items: Partial<KpiCategoriesEntity>[]) {
        const { tenantId } = clientSessionContext;
        const exist = await this.repo.find({ where: { tenantId } });
        const byKey = new Map(exist.map(e => [e.key, e]));

        for (const i of items || []) {
            const k = i.key as NSKPI.ECategoryKey;
            const found = byKey.get(k);
            const payload: Partial<KpiCategoriesEntity> = {
                key: k,
                label: i.label,
                unit: i.unit,
                status: i.status as NSKPI.ECategoryStatus,
                tenantId,
            };
            if (found?.id) {
                await this.repo.update(found.id, { ...found, ...payload, id: found.id });
            } else {
                await this.repo.save(payload as any);
            }
        }
        return { success: true };
    }
}
