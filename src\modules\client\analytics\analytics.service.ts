import { Injectable } from '@nestjs/common';
import { Between, Raw } from 'typeorm';
import { NSComplaint } from '~/common/enums';
import { NSContract } from '~/common/enums/contract.enum';
import { ComplaintRepo, CustomerRepo, MemberRepo, QuoteRepo } from '~/domains/primary';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { clientSessionContext } from '../client-session.context';
import { ProvincesService } from '../provinces/provinces.service';
import {
    AnalyticsFilterDto,
    ComplaintByMemberAnalytics,
    CustomerTypeAnalytics,
    EmployeeCustomerAnalytics,
    RevenueAnalytics,
} from './dto/analytics.dto';

@Injectable()
export class AnalyticsService {
    constructor(
        private readonly customerRepo: CustomerRepo,
        private readonly complaintRepo: ComplaintRepo,
        private readonly quoteRepo: QuoteRepo,
        private readonly contractRepo: ContractRepo,
        private readonly memberRepo: MemberRepo,
        private readonly provincesService: ProvincesService,
    ) {}

    async getCustomerTypeAnalytics(filters: AnalyticsFilterDto): Promise<CustomerTypeAnalytics[]> {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            where.createdDate = Between(new Date(filters.dateFrom), new Date(filters.dateTo));
        }

        if (filters.memberId) {
            where.salesRep = Raw(alias => `${alias} @> '["${filters.memberId}"]'::jsonb`);
        }

        if (filters.customerId) {
            where.id = filters.customerId;
        }

        // Filter by customer type (status parameter from frontend)
        if (filters.customerType) {
            where.customerType = filters.customerType;
        }

        const customers = await this.customerRepo.find({ where });
        const total = customers.length;

        // Group by customerType instead of status
        const statusGroups = customers.reduce(
            (acc, customer) => {
                const customerType = customer.customerType || 'UNKNOWN';
                acc[customerType] = (acc[customerType] || 0) + 1;
                return acc;
            },
            {} as Record<string, number>,
        );

        return Object.entries(statusGroups).map(([customerType, count]) => ({
            customerType,
            count,
            percentage: Number(((count / total) * 100).toFixed(1)),
        }));
    }

    async getCustomerByEmployeeAnalytics(
        filters: AnalyticsFilterDto,
    ): Promise<EmployeeCustomerAnalytics[]> {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            where.createdDate = Between(new Date(filters.dateFrom), new Date(filters.dateTo));
        }

        if (filters.customerId) {
            where.id = filters.customerId;
        }

        // Filter by customer type (customerType parameter from frontend)
        if (filters.customerType) {
            where.customerType = filters.customerType;
        }

        const customers = await this.customerRepo.find({ where });
        const members = await this.memberRepo.find({ where: { tenantId } });

        // Fetch signed contracts within filters
        const contractWhere: any = { tenantId, status: NSContract.EStatus.SIGNED };
        if (filters.dateFrom && filters.dateTo) {
            contractWhere.signingDate = Between(
                new Date(filters.dateFrom),
                new Date(filters.dateTo),
            );
        }
        const contracts = await this.contractRepo.find({ where: contractWhere });
        // Đếm số hợp đồng đã ký theo createdBy
        const memberSignedCountMap = new Map<string, number>();
        contracts.forEach(ct => {
            const mid = (ct as any).createdBy;
            memberSignedCountMap.set(mid, (memberSignedCountMap.get(mid) || 0) + 1);
        });

        const memberMap = new Map(members.map(member => [member.id, member.fullName]));
        const employeeStats = new Map<string, EmployeeCustomerAnalytics>();

        // Khởi tạo tất cả nhân viên với số liệu 0 để luôn hiển thị
        members.forEach(m => {
            employeeStats.set(m.id, {
                employeeId: m.id,
                employeeName: m.fullName,
                customerCount: 0,
                signedCustomers: 0,
            });
        });

        customers.forEach(customer => {
            const salesReps = customer.salesRep || [];
            salesReps.forEach(empId => {
                if (!employeeStats.has(empId)) {
                    employeeStats.set(empId, {
                        employeeId: empId,
                        employeeName: memberMap.get(empId) || 'Unknown',
                        customerCount: 0,
                        signedCustomers: 0,
                    });
                }

                const stats = employeeStats.get(empId)!;
                stats.customerCount++;
            });
        });

        // Fill signedCustomers từ số hợp đồng đã ký theo member
        employeeStats.forEach((stats, mid) => {
            stats.signedCustomers = memberSignedCountMap.get(mid) || 0;
        });

        return Array.from(employeeStats.values()).sort((a, b) => b.customerCount - a.customerCount);
    }

    async getCustomerSourcesAnalytics(
        filters: AnalyticsFilterDto,
    ): Promise<Array<{ source: string; count: number; percentage: number }>> {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            where.createdDate = Between(new Date(filters.dateFrom), new Date(filters.dateTo));
        }
        if (filters.memberId) {
            where.salesRep = Raw(alias => `${alias} @> '["${filters.memberId}"]'::jsonb`);
        }
        if (filters.customerId) {
            where.id = filters.customerId;
        }
        if (filters.customerType) {
            where.customerType = filters.customerType;
        }

        const customers = await this.customerRepo.find({ where });
        const total = customers.length || 1;
        const sourceGroups = customers.reduce(
            (acc, c) => {
                const src = c.source || 'UNKNOWN';
                acc[src] = (acc[src] || 0) + 1;
                return acc;
            },
            {} as Record<string, number>,
        );

        return Object.entries(sourceGroups)
            .map(([source, count]) => ({
                source,
                count,
                percentage: Number(((count / total) * 100).toFixed(1)),
            }))
            .sort((a, b) => b.count - a.count);
    }

    async getTopCustomersBySignedContracts(filters: AnalyticsFilterDto): Promise<
        Array<{
            customerId: string;
            customerName: string;
            contractCount: number;
            totalRevenue: number;
        }>
    > {
        const { tenantId } = clientSessionContext;
        const contractWhere: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            contractWhere.signingDate = Between(
                new Date(filters.dateFrom),
                new Date(filters.dateTo),
            );
        }
        if (filters.memberId) {
            contractWhere.createdBy = filters.memberId;
        }

        const [contracts, quotes, customers] = await Promise.all([
            this.contractRepo.find({ where: contractWhere }),
            this.quoteRepo.find({ where: { tenantId } }),
            this.customerRepo.find({ where: { tenantId } }),
        ]);

        const quoteMap = new Map(quotes.map(q => [q.id, q]));
        const customerMap = new Map(customers.map(c => [c.id, c.name]));

        const agg = new Map<
            string,
            {
                customerId: string;
                customerName: string;
                contractCount: number;
                totalRevenue: number;
            }
        >();

        contracts.forEach(ct => {
            const customerId = ct.customerId;
            const customerName = customerMap.get(customerId) || 'Unknown';
            const revenue = Number(quoteMap.get(ct.quoteId)?.totalAmount || 0);
            const cur = agg.get(customerId) || {
                customerId,
                customerName,
                contractCount: 0,
                totalRevenue: 0,
            };
            cur.contractCount += 1;
            cur.totalRevenue += revenue;
            agg.set(customerId, cur);
        });

        return Array.from(agg.values())
            .sort((a, b) => b.totalRevenue - a.totalRevenue || b.contractCount - a.contractCount)
            .slice(0, 10);
    }

    async getRevenueTrendsAnalytics(filters: AnalyticsFilterDto): Promise<RevenueAnalytics[]> {
        const { tenantId } = clientSessionContext;
        const contractWhere: any = {
            tenantId,
        };

        if (filters.dateFrom && filters.dateTo) {
            contractWhere.signingDate = Between(
                new Date(filters.dateFrom),
                new Date(filters.dateTo),
            );
        }

        if (filters.memberId) {
            contractWhere.createdBy = filters.memberId;
        }

        const contracts = await this.contractRepo.find({
            where: contractWhere,
        });

        const quotes = await this.quoteRepo.find({ where: { tenantId } });
        const quoteMap = new Map(quotes.map(q => [q.id, q]));

        const validContracts = contracts.filter(
            c => c.signingDate && !isNaN(new Date(c.signingDate as any).getTime()),
        );
        const earliestKey = (() => {
            let k: string | undefined;
            for (const ct of validContracts) {
                const rev = Number(quoteMap.get(ct.quoteId)?.totalAmount || 0);
                if (rev > 0) {
                    const key = this.getPeriodKey(
                        new Date(ct.signingDate as any),
                        filters.period || 'monthly',
                    );
                    if (!k || key < k) k = key;
                }
            }
            return k;
        })();

        const periodGroups = validContracts.reduce(
            (acc, contract) => {
                const period = this.getPeriodKey(
                    new Date(contract.signingDate as any),
                    filters.period || 'monthly',
                );
                const quote = quoteMap.get(contract.quoteId);
                const revenue = Number(quote?.totalAmount || 0);

                if (!acc[period]) {
                    acc[period] = { revenue: 0, contracts: 0 };
                }

                acc[period].revenue += revenue;
                acc[period].contracts += 1;

                return acc;
            },
            {} as Record<string, { revenue: number; contracts: number }>,
        );

        const periods = Object.entries(periodGroups)
            .filter(([period]) => !earliestKey || period >= earliestKey)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([period, data], index, arr) => {
                const prevPeriod =
                    index > 0
                        ? (arr[index - 1][1] as { revenue: number; contracts: number })
                        : null;
                const growth = prevPeriod
                    ? Number(
                          (
                              ((data.revenue - prevPeriod.revenue) / prevPeriod.revenue) *
                              100
                          ).toFixed(1),
                      )
                    : 0;

                return {
                    period,
                    revenue: data.revenue,
                    contracts: data.contracts,
                    growth,
                };
            });

        return periods;
    }

    async getComplaintAnalytics(filters: AnalyticsFilterDto) {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            where.createdDate = Between(new Date(filters.dateFrom), new Date(filters.dateTo));
        }

        if (filters.memberId) {
            where.handlerEmployeeId = filters.memberId;
        }
        if (filters.customerId) {
            where.customerId = filters.customerId;
        }
        if (filters.customerType) {
            where.customerType = filters.customerType;
        }

        const complaints = await this.complaintRepo.find({ where });

        const totalComplaints = complaints.length;
        const newComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.NEW,
        ).length;
        const sentComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.SENT,
        ).length;
        const approveComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.APPROVED,
        ).length;
        const rejectComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.REJECTED,
        ).length;
        const inProgressComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.IN_PROGRESS,
        ).length;
        const onHoldComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.HOLD,
        ).length;
        const resolvedComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.RESOLVED,
        ).length;

        const cancelledComplaints = complaints.filter(
            c => c.status === NSComplaint.EComplaintStatus.CANCELLED,
        ).length;

        // Group by complaint type (matches Setting Option COMPLAINT_TYPE)
        const typeGroups = complaints.reduce(
            (acc, complaint) => {
                const type = complaint.type || 'UNKNOWN';
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            },
            {} as Record<string, number>,
        );

        const topComplaintTypes = Object.entries(typeGroups)
            .map(([type, count]) => ({
                type,
                count,
                percentage: Number(((count / totalComplaints) * 100).toFixed(1)),
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);

        return {
            totalComplaints,
            newComplaints,
            sentComplaints,
            approveComplaints,
            rejectComplaints,
            inProgressComplaints,
            onHoldComplaints,
            resolvedComplaints,
            cancelledComplaints,
            topComplaintTypes,
        };
    }

    async getComplaintByMemberAnalytics(
        filters: AnalyticsFilterDto,
    ): Promise<ComplaintByMemberAnalytics[]> {
        const { tenantId } = clientSessionContext;
        const where: any = { tenantId };

        if (filters.dateFrom && filters.dateTo) {
            where.createdDate = Between(new Date(filters.dateFrom), new Date(filters.dateTo));
        }
        if (filters.customerId) {
            where.customerId = filters.customerId;
        }
        if (filters.customerType) {
            where.customerType = filters.customerType;
        }
        if (filters.memberId) {
            where.handlerEmployeeId = filters.memberId;
        }

        const complaints = await this.complaintRepo.find({ where });
        const members = await this.memberRepo.find({ where: { tenantId } });
        const memberMap = new Map(members.map(m => [m.id, m.fullName]));

        const agg = new Map<string, ComplaintByMemberAnalytics>();
        complaints.forEach(c => {
            const mid = c.handlerEmployeeId || 'UNKNOWN';
            const cur = agg.get(mid) || {
                memberId: mid,
                memberName: memberMap.get(mid) || 'Unknown',
                totalComplaints: 0,
                resolvedComplaints: 0,
                resolutionRate: 0,
            };
            cur.totalComplaints += 1;
            if (c.status === NSComplaint.EComplaintStatus.RESOLVED) cur.resolvedComplaints += 1;
            agg.set(mid, cur);
        });

        const result = Array.from(agg.values()).map(r => ({
            ...r,
            resolutionRate:
                r.totalComplaints > 0
                    ? Number(((r.resolvedComplaints / r.totalComplaints) * 100).toFixed(1))
                    : 0,
        }));

        return result.sort((a, b) => b.totalComplaints - a.totalComplaints).slice(0, 10);
    }

    private getPeriodKey(date: Date, period: string): string {
        const d = new Date(date);

        switch (period) {
            case 'daily':
                return d.toISOString().split('T')[0]; // YYYY-MM-DD
            case 'weekly':
                const weekStart = new Date(d);
                weekStart.setDate(d.getDate() - d.getDay());
                return `${weekStart.getFullYear()}-W${String(Math.ceil((weekStart.getTime() - new Date(weekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))).padStart(2, '0')}`; // YYYY-W##
            case 'monthly':
                return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`; // YYYY-MM
            case 'quarterly':
                const quarter = Math.floor(d.getMonth() / 3) + 1;
                return `${d.getFullYear()}-Q${quarter}`; // YYYY-QX
            case 'yearly':
                return d.getFullYear().toString(); // YYYY
            default:
                return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
        }
    }
}
