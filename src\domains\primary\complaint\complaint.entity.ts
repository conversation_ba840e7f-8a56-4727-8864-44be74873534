import { ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSComplaint } from '~/common/enums/complaint.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('complaint')
export class ComplaintEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional({ example: 'KN0001' })
    @Column({ type: 'varchar', length: 32, nullable: true })
    @Index()
    code?: string | null;

    @ApiPropertyOptional({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    customerId?: string | null;

    @ApiPropertyOptional({
        enum: NSComplaint.EComplaintPriority,
    })
    @Column({ type: 'varchar', nullable: true })
    @Index()
    priority?: string;

    @ApiPropertyOptional({ description: 'Nhân viên theo dõi/giám sát' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    followerEmployeeId?: string | null;

    @ApiPropertyOptional({ description: 'Nhân viên xử lý chính' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    handlerEmployeeId?: string | null;

    @ApiPropertyOptional({ example: 'Khiếu nại về giao hàng trễ' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    title?: string | null;

    @ApiPropertyOptional({ example: 'Mô tả chi tiết vấn đề…' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @ApiPropertyOptional({})
    @Column({ type: 'varchar', nullable: true })
    @Index()
    type?: string;

    @ApiPropertyOptional({
        enum: NSComplaint.EComplaintStatus,
        example: NSComplaint.EComplaintStatus.IN_PROGRESS,
    })
    @Column({
        type: 'enum',
        enum: NSComplaint.EComplaintStatus,
        default: NSComplaint.EComplaintStatus.NEW,
        nullable: true,
    })
    @Index()
    status?: NSComplaint.EComplaintStatus | null;

    @ApiPropertyOptional({ description: 'SLA deadline dự kiến' })
    @Column({ type: 'timestamptz', nullable: true })
    dueDate?: Date;

    @ApiPropertyOptional({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    addressId?: string | null;

    @ApiPropertyOptional({ example: '123 Đường ABC, Quận 1, TP.HCM' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    address?: string | null;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 6, nullable: true })
    provinceCode?: string | null;

    @ApiPropertyOptional({ example: '020000' })
    @Column({ type: 'varchar', length: 6, nullable: true })
    communeCode?: string | null;

    @ApiPropertyOptional({ description: 'ID khiếu nại gốc (parent complaint)' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    parentComplaintId?: string | null;

    @ApiPropertyOptional({
        description: 'Loại quan hệ với khiếu nại gốc',
        enum: NSComplaint.EComplaintRelationshipType,
    })
    @Column({ type: 'varchar', nullable: true })
    @Index()
    relationshipType?: string;
}
