import { Query, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { MediaService } from './media.service';
import { ListMediaDto } from './dto/media.dto';

@DefController('media')
@UseGuards(ClientAuthGuard)
export class MediaController {
    constructor(private mediaService: MediaService) {}

    @DefGet('list')
    async listMedia(@Query() params: ListMediaDto) {
        return this.mediaService.listMedia(params);
    }

    @DefPost('upload')
    @UseInterceptors(FileInterceptor('file'))
    async uploadMedia(@UploadedFile() file: Express.Multer.File) {
        return this.mediaService.uploadMedia(file);
    }
}

