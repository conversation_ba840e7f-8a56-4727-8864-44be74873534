import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne, OneToMany } from 'typeorm'
import { PrimaryBaseEntity } from '../primary-base.entity'
import { NSApproval } from '~/common/enums/approval.enum'
import { ApprovalConfigStepEntity } from './approval-config-step.entity'
import { ApprovalRequestEntity } from './approval-request.entity'
import { ApprovalRequestStepApproverEntity } from './approval-request-step-approver.entity'

@Entity('approval_request_step')
@Index(['requestId', 'order'], { unique: true })
export class ApprovalRequestStepEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  @Index()
  requestId: string

  // Tham chiếu config step gốc (chỉ để trace, không phụ thuộc để chạy)
  @Column({ type: 'uuid', nullable: true })
  configStepId: string | null

  // Thứ tự cấp duyệt (snapshot từ configStep.order)
  @Column({ type: 'int' })
  order: number

  // Kiểu duyệt: ALL / ANY / MIN_X_USER...
  @Column({
    type: 'enum',
    enum: NSApproval.ApprovalLevelType,
    default: NSApproval.ApprovalLevelType.SINGLE,
  })
  approvalLevelType: NSApproval.ApprovalLevelType

  // Số người cần duyệt tối thiểu (nếu dùng kiểu MIN_X)
  @Column({ type: 'int', default: 1 })
  minApprovers: number

  @Column({
    type: 'enum',
    enum: NSApproval.ApprovalStepStatus,
    default: NSApproval.ApprovalStepStatus.PENDING,
  })
  status: NSApproval.ApprovalStepStatus

  @Column({ type: 'timestamptz', nullable: true })
  startedAt?: Date

  @Column({ type: 'timestamptz', nullable: true })
  finishedAt?: Date

  // Thời điểm dự kiến hoàn thành (tính từ startedAt + leadtime từ config)
  @Column({ type: 'timestamptz', nullable: true })
  expectedFinishedAt?: Date

  @ManyToOne(() => ApprovalConfigStepEntity, {
    onDelete: 'SET NULL', // tránh xóa mất history
  })
  @JoinColumn({ name: 'configStepId' })
  configStep: ApprovalConfigStepEntity

  @ManyToOne(() => ApprovalRequestEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requestId' })
  request: ApprovalRequestEntity

  @OneToMany(() => ApprovalRequestStepApproverEntity, (a) => a.step)
  approvers: ApprovalRequestStepApproverEntity[]
}
