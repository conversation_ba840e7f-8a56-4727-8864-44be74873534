import { PartialType } from '@nestjs/mapped-types';
import { CreateCustomerAddressDto } from './create-customer-address.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsArray } from 'class-validator';

export class UpdateCustomerAddressDto extends PartialType(CreateCustomerAddressDto) {
    @ApiProperty({ description: 'ID của địa chỉ' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class ListUpdateCustomerAddressDto {
    @ApiProperty({ type: [UpdateCustomerAddressDto] })
    @IsNotEmpty()
    @IsArray()
    addresses: UpdateCustomerAddressDto[];
}
