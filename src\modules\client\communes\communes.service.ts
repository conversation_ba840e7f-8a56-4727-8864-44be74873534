import { Injectable } from '@nestjs/common';
import { apePublicApiConnector } from '~/connectors';
import { IPaginationCommunes } from './dto/communes.dto';

const Endpoint = {
    pagination: '/api/public/communes/pagination',
    getCommunesByCode: '/api/public/communes/get-communes-by-code',
};
@Injectable()
export class CommunesService {
    async pagination(query?: IPaginationCommunes) {
        return await apePublicApiConnector.get(Endpoint.pagination, query);
    }

    async getCommunesByCode(codes: string) {
        return await apePublicApiConnector.get(`${Endpoint.getCommunesByCode}?code=${codes}`);
    }
}
