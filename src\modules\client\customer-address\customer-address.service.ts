import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike, In, Not } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { apePublicApiConnector } from '~/connectors';
import { CustomerRepo } from '~/domains/primary';
import { CustomerAddressRepo } from '~/domains/primary/customer-address/customer-address.repo';
import { clientSessionContext } from '../client-session.context';
import { CommunesService } from '../communes/communes.service';
import { ProvincesService } from '../provinces/provinces.service';
import {
    CreateCustomerAddressDto,
    ListCreateCustomerAddressDto,
} from './dto/create-customer-address.dto';
import { DeleteListCustomerAddressDto } from './dto/delete-customer-address.dto';
import { ListCustomerAddressDto } from './dto/list-customer-address.dto';
import {
    ListUpdateCustomerAddressDto,
    UpdateCustomerAddressDto,
} from './dto/update-customer-address.dto';

@Injectable()
export class CustomerAddressService {
    constructor(
        @InjectRepo(CustomerAddressRepo) private customerAddressRepo: CustomerAddressRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        private readonly provincesService: ProvincesService,
        private readonly communesService: CommunesService,
    ) {}

    @DefTransaction()
    async create(createCustomerAddressDto: CreateCustomerAddressDto) {
        const newCustomerAddress = await this.customerAddressRepo.insert({
            ...createCustomerAddressDto,

            createdBy: clientSessionContext.memberId,
        });
        return {
            message: 'Tạo địa chỉ khách hàng thành công',
            data: newCustomerAddress,
        };
    }

    @DefTransaction()
    async createList(createCustomerAddressDto: ListCreateCustomerAddressDto) {
        const newCustomerAddress = await this.customerAddressRepo.insert(
            createCustomerAddressDto.addresses.map(address => ({
                ...address,
                createdBy: clientSessionContext.memberId,
            })),
        );

        //check default
        const defaultAddress = createCustomerAddressDto.addresses.filter(a => a.isDefault);
        if (defaultAddress.length > 1) {
            throw new BusinessException('Không thể có 2 địa chỉ mặc định');
        }

        if (defaultAddress[0]) {
            const provinceCode = defaultAddress[0].provinceCode;
            const communeCode = defaultAddress[0].communeCode;
            const [provinces, communes] = await Promise.all([
                this.provincesService.getProvincesByCode(provinceCode),
                this.communesService.getCommunesByCode(communeCode),
            ]);
            const provinceName = provinces?.find(p => p.code === provinceCode)?.name ?? '';
            const communeName = communes?.find(c => c.code === communeCode)?.name ?? '';
            const base = String(defaultAddress[0].address || '').trim();
            const fullAddress = [base, communeName, provinceName].filter(Boolean).join(', ');
            await this.customerRepo.update(defaultAddress[0].customerId, {
                id: defaultAddress[0].customerId,
                address: fullAddress,
                provinceCode,
                communeCode,
            });
        }

        return {
            message: 'Tạo địa chỉ khách hàng thành công',
            data: newCustomerAddress,
        };
    }

    async list(params: ListCustomerAddressDto) {
        const where: any = {};
        if (params?.customerId) {
            where.customerId = params?.customerId;
        }
        if (params?.addressName) {
            where.addressName = ILike(`%${params?.addressName}%`);
        }
        if (params?.address) {
            where.address = ILike(`%${params?.address}%`);
        }
        if (params?.provinceCode) {
            where.provinceCode = params?.provinceCode;
        }
        if (params?.districtCode) {
            where.districtCode = params?.districtCode;
        }
        if (params?.wardCode) {
            where.wardCode = params?.wardCode;
        }
        if (params?.regionCode) {
            where.regionCode = params?.regionCode;
        }
        if (params?.isActive) {
            where.isActive = params?.isActive;
        }

        const address = await this.customerAddressRepo.findPagination(
            {
                where,
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex: params.pageIndex,
                pageSize: params.pageSize,
            },
        );
        const provinceCodes = address.data.map(item => item.provinceCode).join(', ');
        const provinces = await this.provincesService.getProvincesByCode(provinceCodes);
        const communeCodes = address.data.map(item => item.communeCode).join(', ');
        const communes = await this.communesService.getCommunesByCode(communeCodes);
        return {
            data: address.data.map(item => ({
                ...item,
                provinceName: provinces?.find(p => p.code === item.provinceCode)?.name,
                communeName: communes?.find(c => c.code === item.communeCode)?.name,
            })),
        };
    }

    @DefTransaction()
    async setActive(id: string) {
        const customerAddress = await this.customerAddressRepo.findOneBy({ id });
        if (!customerAddress) {
            throw new Error('Không tìm thấy địa chỉ khách hàng');
        }
        await this.customerAddressRepo.update(id, { isActive: true, id: id });
        return { message: 'Cập nhật trạng thái địa chỉ khách hàng thành công' };
    }

    @DefTransaction()
    async setInActive(id: string) {
        const customerAddress = await this.customerAddressRepo.findOneBy({ id });
        if (!customerAddress) {
            throw new Error('Không tìm thấy địa chỉ khách hàng');
        }
        await this.customerAddressRepo.update(id, { isActive: false, id: id });
        return { message: 'Cập nhật trạng thái địa chỉ khách hàng thành công' };
    }

    @DefTransaction()
    async update(updateCustomerAddressDto: UpdateCustomerAddressDto) {
        //find
        const customerAddress = await this.customerAddressRepo.findOneBy({
            id: updateCustomerAddressDto.id,
        });
        if (!customerAddress) {
            throw new Error('Không tìm thấy địa chỉ khách hàng');
        }

        await this.customerAddressRepo.update(updateCustomerAddressDto.id, {
            ...updateCustomerAddressDto,
            id: updateCustomerAddressDto.id,
        });
        return { message: 'Cập nhật địa chỉ khách hàng thành công' };
    }

    @DefTransaction()
    async updateList(listUpdateCustomerAddressDto: ListUpdateCustomerAddressDto) {
        try {
            const addresses = listUpdateCustomerAddressDto.addresses.map(a =>
                this.customerAddressRepo.create({
                    ...a,
                    updatedBy: clientSessionContext.memberId,
                }),
            );
            await this.customerAddressRepo.saves(addresses);

            //có 2 địa chỉ default thì đá lỗi
            const defaultAddress = addresses.filter(a => a.isDefault);
            if (defaultAddress.length > 1) {
                throw new BusinessException('Không thể có 2 địa chỉ mặc định');
            }

            if (defaultAddress[0]) {
                const provinceCode = defaultAddress[0].provinceCode;
                const communeCode = defaultAddress[0].communeCode;
                const [provinces, communes] = await Promise.all([
                    this.provincesService.getProvincesByCode(provinceCode),
                    this.communesService.getCommunesByCode(communeCode),
                ]);
                const provinceName = provinces?.find(p => p.code === provinceCode)?.name ?? '';
                const communeName = communes?.find(c => c.code === communeCode)?.name ?? '';
                const base = String(defaultAddress[0].address || '').trim();
                const fullAddress = [base, communeName, provinceName].filter(Boolean).join(', ');
                await this.customerRepo.update(defaultAddress[0].customerId, {
                    id: defaultAddress[0].customerId,
                    address: fullAddress,
                    provinceCode,
                    communeCode,
                });
            }

            return { message: 'Cập nhật địa chỉ khách hàng thành công' };
        } catch (error) {
            throw new BusinessException('Cập nhật địa chỉ khách hàng thất bại');
        }
    }

    @DefTransaction()
    async setDefault(id: string) {
        //find
        const customerAddress = await this.customerAddressRepo.findOneBy({ id });
        if (!customerAddress) {
            throw new Error('Không tìm thấy địa chỉ khách hàng');
        }
        await this.customerAddressRepo.update(id, { isDefault: true });
        await this.customerAddressRepo.update(
            { customerId: customerAddress.customerId, id: Not(id) },
            { isDefault: false },
        );

        await this.customerRepo.update(customerAddress.customerId, {
            address: customerAddress.address,
        });
        return { message: 'Cập nhật địa chỉ mặc định thành công' };
    }

    //delete list
    @DefTransaction()
    async deleteList(params: DeleteListCustomerAddressDto) {
        const { ids } = params;
        await this.customerAddressRepo.delete({ id: In(ids) });
        return { message: 'Xóa địa chỉ khách hàng thành công' };
    }

    @DefTransaction()
    async getProvinceByCode(query: string) {
        return apePublicApiConnector.get('/api/public/provinces/get-provinces-by-code', {
            query,
        });
    }

    @DefTransaction()
    async getCommuneByCode(query: string) {
        return apePublicApiConnector.get('/api/public/communes/get-communes-by-code', {
            query,
        });
    }
}
