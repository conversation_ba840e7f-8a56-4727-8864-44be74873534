export namespace NSEventLogs {
    export enum EntityTypes {
        CAMPAIGN = 'CAMPAIGN',
        CAMPAIGN_CUSTOMER = 'CAMPAIGN_CUSTOMER',
        CATALOG = 'CATALOG',
        CATALOG_ITEM = 'CATALOG_ITEM',
        CONTACT_CARE = 'CONTACT_CARE',
        CONTRACT = 'CONTRACT',
        CONTRACT_TEMPLATE = 'CONTRACT_TEMPLATE',
        CUSTOMER = 'CUSTOMER',
        CUSTOMER_ADDRESS = 'CUSTOMER_ADDRESS',
        CUSTOMER_CONTACT = 'CUSTOMER_CONTACT',
        CUSTOMER_DOCUMENT = 'CUSTOMER_DOCUMENT',
        DEPARTMENT = 'DEPARTMENT',
        GROUP_CUSTOMER = 'GROUP_CUSTOMER',
        KPI_CATEGORIES = 'KPI_CATEGORIES',
        KPI_MEMBER = 'KPI_MEMBER',
        KPI_MEMBER_ITEM = 'K<PERSON>_MEMBER_ITEM',
        MEMBER = 'MEMBER',
        POSITION = 'POSITION',
        QUOTE = 'QUOTE',
        QUOTE_ITEM = 'QUOTE_ITEM',
        ROLE = 'ROLE',
        SETTING_STRING = 'SETTING_STRING',
        APPROVAL_CONFIG = 'APPROVAL_CONFIG',
        APPROVAL_CONFIG_MAPPING = 'APPROVAL_CONFIG_MAPPING',
        APPROVAL_CONFIG_STEP = 'APPROVAL_CONFIG_STEP',
        APPROVAL_CONFIG_STEP_APPROVER = 'APPROVAL_CONFIG_STEP_APPROVER',
        APPROVAL_REQUEST = 'APPROVAL_REQUEST',
        APPROVAL_REQUEST_ACTION = 'APPROVAL_REQUEST_ACTION',
        APPROVAL_REQUEST_STEP = 'APPROVAL_REQUEST_STEP',
        APPROVAL_REQUEST_STEP_APPROVER = 'APPROVAL_REQUEST_STEP_APPROVER',
        TASK = 'TASK',
    }

    export enum EventTypes {
        CREATED = 'CREATED',
        UPDATED = 'UPDATED',
        DELETED = 'DELETED',
        STATUS_CHANGE = 'STATUS_CHANGE',
    }
}
