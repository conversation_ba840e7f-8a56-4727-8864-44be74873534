// src/domains/crm/customer.entity.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('customer_address')
export class CustomerAddressEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @Column({ type: 'varchar', length: 64, nullable: false })
    @Index()
    customerId: string;

    @ApiProperty({ example: 'AD-0001', description: 'Tên địa chỉ' })
    @Column({ type: 'text', nullable: false })
    addressName: string;

    @ApiPropertyOptional({ example: '456 Đường XYZ, Quận 2, TP.HCM', description: 'Địa chỉ' })
    @Column({ type: 'text', nullable: true })
    address?: string | null;

    @ApiPropertyOptional({ example: '79', description: 'Mã tỉnh/thành phố' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    provinceCode?: string | null;

    @ApiPropertyOptional({ example: '769', description: 'Mã quận/huyện' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    districtCode?: string | null;

    @ApiPropertyOptional({ example: '27134', description: 'Mã phường/xã' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    communeCode?: string | null;

    @ApiPropertyOptional({ example: 'Miền Bắc', description: 'Mã vùng' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    regionCode?: string | null;

    @ApiPropertyOptional({ example: true, description: 'Địa chỉ mặc định' })
    @Column({ type: 'boolean', default: false })
    isDefault?: boolean;

    @ApiPropertyOptional({ example: 'Mô tả', description: 'Ghi chú' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @ApiProperty({ example: true })
    @Column({ type: 'boolean', default: true, nullable: false })
    isActive: boolean;
}
