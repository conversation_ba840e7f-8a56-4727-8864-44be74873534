import { Body, Param, Query, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { NSComplaint } from '~/common/enums';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { ComplaintService } from './complaint.service';
import { ListComplaintHistoryDto } from './dto/complaint-history.dto';
import { ListComplaintProcessDto } from './dto/complaint-process.dto';
import {
    CreateCustomerComplaintDto,
    ListCustomerComplaintDto,
    UpdateCustomerComplaintDto,
} from './dto/complaint.dto';

@UseGuards(PermissionGuard)
@DefController('complaint')
export class ComplaintController {
    constructor(private complaintService: ComplaintService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.VIEW])
    async listComplaint(@Query() query: ListCustomerComplaintDto) {
        return this.complaintService.listComplaint(query);
    }

    @DefGet('history')
    async listComplaintHistory(@Query() params: ListComplaintHistoryDto) {
        return this.complaintService.listComplaintHistory(params);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.CREATE])
    @UseInterceptors(FilesInterceptor('file', 10))
    async createComplaint(
        @UploadedFiles() files: Express.Multer.File[],
        @Body() body: CreateCustomerComplaintDto,
    ) {
        return this.complaintService.createComplaint(body, files);
    }

    @DefGet('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.VIEW])
    async detailComplaint(@Query('id') id: string) {
        return this.complaintService.detailComplaint(id);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.UPDATE])
    @UseInterceptors(FilesInterceptor('file', 10))
    async updateComplaint(
        @UploadedFiles() files: Express.Multer.File[],
        @Body() body: UpdateCustomerComplaintDto,
    ) {
        return this.complaintService.updateComplaint(body, files);
    }

    @DefPost('update-status')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.UPDATE])
    async updateStatus(@Body() body: { id: string; status: NSComplaint.EComplaintStatus }) {
        return this.complaintService.updateStatus(body.id, body.status);
    }

    @DefPost('delete-media')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.UPDATE])
    async deleteMediaId(@Body() body: { id: string; mediaId: string }) {
        return this.complaintService.deleteMediaId(body.id, body.mediaId);
    }

    @DefGet('related/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.VIEW])
    async getRelatedComplaints(@Param('id') id: string) {
        return this.complaintService.getRelatedComplaints(id);
    }

    @DefPost('process')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.UPDATE])
    @UseInterceptors(FilesInterceptor('file', 10))
    async createComplaintProcess(
        @UploadedFiles() files: Express.Multer.File[],
        @Body() body: FormData,
    ) {
        return this.complaintService.createComplaintProcess(body, files);
    }

    @DefGet('process')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.VIEW])
    async listComplaintProcess(@Query() query: ListComplaintProcessDto) {
        return this.complaintService.listComplaintProcess(query);
    }

    @DefPost('split-finalize')
    @RequirePermissions([PERMISSION_CODES.SETTING_COMPLAINT.UPDATE])
    async finalizeSplitCase(
        @Body()
        body: { parentComplaintId: string; finalStatus: NSComplaint.EComplaintStatus },
    ) {
        return this.complaintService.finalizeSplitCase(body.parentComplaintId, body.finalStatus);
    }
}
