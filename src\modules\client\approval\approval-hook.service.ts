import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'ape-nestjs-typeorm3-kit';
import { NSComplaint } from '~/common/enums';
import { NSApproval } from '~/common/enums/approval.enum';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { NSContactCare } from '~/common/enums/contact-care.enum';
import { NSContract } from '~/common/enums/contract.enum';
import { NSQuote } from '~/common/enums/quote.enum';
import { ComplaintRepo, ContactCareRepo } from '~/domains/primary';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { CampaignRepo } from '~/domains/primary/marketing-campaign/campaign.repo';
import { QuoteRepo } from '~/domains/primary/quote/quote.repo';
import { HookRequestDto } from './dto/approval-request.dto';

@Injectable()
export class ApprovalHookService {
    constructor(
        @InjectRepo(QuoteRepo)
        private readonly quoteRepo: QuoteRepo,
        @InjectRepo(ContractRepo)
        private readonly contractRepo: ContractRepo,
        @InjectRepo(ComplaintRepo)
        private readonly complaintRepo: ComplaintRepo,
        @InjectRepo(ContactCareRepo)
        private readonly contactCareRepo: ContactCareRepo,
        @InjectRepo(CampaignRepo)
        private readonly campaignRepo: CampaignRepo,
    ) {}

    /**
     * HOOK XỬ LÝ NGHIỆP VỤ SAU MỖI LẦN DUYỆT STEP
     *
     * @description
     * - Được gọi sau khi 1 step duyệt hoàn thành (APPROVED hoặc REJECTED).
     * - Nhận tham số orderStep để biết đang duyệt ở cấp nào (step 1, step 2,...).
     * - Nhận tham số numberStep để biết tổng số step trong quy trình.
     * - Dùng để cập nhật trạng thái chứng từ theo từng nghiệp vụ.
     *
     * @param businessType Nghiệp vụ đang xử lý
     * @param documentId   Chứng từ tương ứng
     * @param requestStatus Trạng thái request sau khi duyệt
     * @param comment     Comment người duyệt (nếu có)
     * @param orderStep   Thứ tự step vừa duyệt xong
     * @param numberStep  Tổng số step trong quy trình
     */
    public async updateDocumentAfterApproval(params: HookRequestDto) {
        const { businessType, documentId, requestStatus } = params;

        switch (businessType) {
            case NSApproval.ApprovalBusinessType.QUOTATION: {
                await this.handleQuotationApproval(documentId, requestStatus);
                break;
            }
            case NSApproval.ApprovalBusinessType.CONTRACT: {
                await this.handleContractApproval(documentId, requestStatus);
                break;
            }
            case NSApproval.ApprovalBusinessType.COMPLAINT: {
                await this.handleComplaintApproval(documentId, requestStatus);
                break;
            }
            case NSApproval.ApprovalBusinessType.CONTACT_CARE: {
                await this.handleContactCareApproval(documentId, requestStatus);
                break;
            }
            case NSApproval.ApprovalBusinessType.CAMPAIGN: {
                await this.handleCampaignApproval(documentId, requestStatus);
                break;
            }
            default: {
                break;
            }
        }
    }

    private async handleQuotationApproval(
        documentId: string,
        requestStatus: NSApproval.ApprovalRequestStatus,
    ) {
        if (requestStatus === NSApproval.ApprovalRequestStatus.APPROVED) {
            await this.quoteRepo.update(documentId, {
                status: NSQuote.EStatus.APPROVED,
            });
        }
        if (requestStatus === NSApproval.ApprovalRequestStatus.REJECTED) {
            await this.quoteRepo.update(documentId, {
                status: NSQuote.EStatus.REJECTED,
            });
        }
    }

    private async handleContractApproval(
        documentId: string,
        requestStatus: NSApproval.ApprovalRequestStatus,
    ) {
        if (requestStatus === NSApproval.ApprovalRequestStatus.APPROVED) {
            await this.contractRepo.update(documentId, {
                status: NSContract.EStatus.APPROVED,
            });
        }
        if (requestStatus === NSApproval.ApprovalRequestStatus.REJECTED) {
            await this.contractRepo.update(documentId, {
                status: NSContract.EStatus.REJECTED,
                signingDate: null,
            });
        }
    }

    private async handleComplaintApproval(
        documentId: string,
        requestStatus: NSApproval.ApprovalRequestStatus,
    ) {
        if (requestStatus === NSApproval.ApprovalRequestStatus.APPROVED) {
            await this.complaintRepo.update(documentId, {
                status: NSComplaint.EComplaintStatus.APPROVED,
            });
        }
        if (requestStatus === NSApproval.ApprovalRequestStatus.REJECTED) {
            await this.complaintRepo.update(documentId, {
                status: NSComplaint.EComplaintStatus.REJECTED,
            });
        }
    }

    private async handleContactCareApproval(
        documentId: string,
        requestStatus: NSApproval.ApprovalRequestStatus,
    ) {
        if (requestStatus === NSApproval.ApprovalRequestStatus.APPROVED) {
            await this.contactCareRepo.update(documentId, {
                status: NSContactCare.EStatus.APPROVED,
            });
        }
        if (requestStatus === NSApproval.ApprovalRequestStatus.REJECTED) {
            await this.contactCareRepo.update(documentId, {
                status: NSContactCare.EStatus.REJECTED,
            });
        }
    }

    private async handleCampaignApproval(
        documentId: string,
        requestStatus: NSApproval.ApprovalRequestStatus,
    ) {
        if (requestStatus === NSApproval.ApprovalRequestStatus.APPROVED) {
            await this.campaignRepo.update(documentId, {
                status: NSCampaign.EStatus.APPROVED,
            });
        }
        if (requestStatus === NSApproval.ApprovalRequestStatus.REJECTED) {
            await this.campaignRepo.update(documentId, {
                status: NSCampaign.EStatus.REJECTED,
            });
        }
    }
}
