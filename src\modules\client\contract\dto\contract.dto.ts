import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { NSContract } from '~/common/enums/contract.enum';

export class ContractDTO {
    @ApiProperty({
        example: '12345678-1234-1234-1234-123456789012',
        description: 'ID hợp đồng',
    })
    id: string;

    @ApiProperty({
        example: '12345678-1234-1234-1234-123456789012',
        description: 'ID tenant',
    })
    tenantId: string;
    @ApiProperty({ example: 'HD001', description: 'Mã hợp đồng' })
    code: string;

    @ApiProperty({ example: 'Hợp đồng giữa Công ty A và Công ty B', description: 'Tên hợp đồng' })
    name: string;

    @ApiProperty({ example: '12345678-1234-1234-1234-123456789012', description: 'ID khách hàng' })
    customerId: string;

    @ApiProperty({ example: 'SERVICE', description: 'HTML hợp đồng' })
    type: NSContract.EType;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày ký hợp đồng' })
    signingDate: Date;

    @ApiProperty({ example: '123456', description: 'ID báo giá' })
    quoteId: string;

    @ApiProperty({
        example: '<p>Hợp đồng giữa Công ty A và Công ty B</p>',
        description: 'HTML hợp đồng',
    })
    html: string;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày tạo hợp đồng' })
    createDate: Date;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày hết hạn' })
    dueDate: Date;

    @ApiProperty({ example: '12345678-1234-1234-1234-123456789012', description: 'ID người tạo' })
    createdBy: string;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái mẫu hợp đồng' })
    status: NSContract.EStatus;
}
export class PaginationQueryDto extends ContractDTO {
    @ApiProperty({ example: 1, description: 'Số trang' })
    pageIndex: number;

    @ApiProperty({ example: 10, description: 'Số lượng trên mỗi trang' })
    pageSize: number;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày tạo hợp đồng' })
    createdDateFrom: Date;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày tạo hợp đồng' })
    createdDateTo: Date;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày ký hợp đồng' })
    signingDateFrom: Date;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày ký hợp đồng' })
    signingDateTo: Date;
}
export class CreateContractDto {
    @ApiProperty({ example: 'HD001', description: 'Mã hợp đồng' })
    code: string;

    @ApiProperty({ example: 'Hợp đồng giữa Công ty A và Công ty B', description: 'Tên hợp đồng' })
    name: string;

    @ApiProperty({
        example: '<p>Hợp đồng giữa Công ty A và Công ty B</p>',
        description: 'HTML hợp đồng',
    })
    html: string;

    @ApiProperty({ example: 'SERVICE', description: 'Loại hợp đồng' })
    type: string;

    @ApiProperty({ example: '123456', description: 'ID báo giá' })
    quoteId: string;

    @ApiProperty({
        example: '12345678-1234-1234-1234-123456789012',
        description: 'ID mẫu hợp đồng',
    })
    templateId: string;

    @ApiProperty({ example: '12345678-1234-1234-1234-123456789012', description: 'ID khách hàng' })
    customerId: string;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày hết hạn' })
    dueDate: Date;
}
export class UpdateContractDto {
    @ApiProperty({ example: '12345678-1234-1234-1234-123456789012', description: 'ID hợp đồng' })
    id: string;

    @ApiProperty({ example: 'HD001', description: 'Mã hợp đồng' })
    code: string;

    @ApiProperty({ example: 'HD-2025-123456', description: 'Số hợp đồng' })
    contractNumber: string;

    @ApiProperty({ example: 'Hợp đồng giữa Công ty A và Công ty B', description: 'Tên hợp đồng' })
    name: string;

    @ApiProperty({ example: 'SERVICE', description: 'HTML hợp đồng' })
    type: NSContract.EType;

    @ApiProperty({ example: '123456', description: 'ID báo giá' })
    quoteId: string;

    @ApiProperty({ example: '2025-01-01', description: 'Ngày hết hạn' })
    dueDate: Date;
}
export class UpdateStatusDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ enum: NSContract.EStatus, example: NSContract.EStatus.NEW })
    @IsEnum(NSContract.EStatus)
    status: NSContract.EStatus;
}
