import { Injectable } from '@nestjs/common';
import { MailSenderService } from './mail-sender.service';
import { SendMailOptionsDto } from './mail.types';

@Injectable()
export class MailQueueService {
    private queue: SendMailOptionsDto[] = [];
    private processing = false;

    constructor(private readonly sender: MailSenderService) {}

    enqueue(job: SendMailOptionsDto) {
        this.queue.push(job);
        this.run();
    }

    private async run() {
        if (this.processing) return;
        this.processing = true;
        while (this.queue.length) {
            const job = this.queue.shift()!;
            try {
                await this.sender.sendMail(job);
            } catch (e) {}
        }
        this.processing = false;
    }
}
