import { ApiProperty } from '@nestjs/swagger';

import { PrimaryBaseEntity } from '../primary-base.entity';

import { Column, Entity, Index } from 'typeorm';
import { NSContract } from '~/common/enums/contract.enum';

@Entity('contract')
export class ContractEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'HD001', description: 'Mã hợp đồng' })
    @Column({ type: 'varchar', nullable: true })
    @Index()
    code: string;

    @ApiProperty({ example: 'Hợp đồng giữa Công ty A và Công ty B', description: 'Tên hợp đồng' })
    @Column({ type: 'varchar', nullable: true })
    name: string;

    @ApiProperty({ example: '123456', description: 'ID khách hàng' })
    @Column({ type: 'varchar', nullable: true })
    customerId: string;

    @ApiProperty({ example: 'SERVICE', description: '<PERSON><PERSON><PERSON> hợp đồng' })
    @Column({ type: 'varchar', nullable: true })
    type: string;

    @ApiProperty({ example: '2025-01-01', description: '<PERSON><PERSON><PERSON> ký hợp đồng' })
    @Column({ type: 'timestamptz', nullable: true })
    signingDate?: Date;

    @ApiProperty({ example: 'HTML hợp đồng', description: 'HTML hợp đồng' })
    @Column({ type: 'text', nullable: true })
    html: string;

    @ApiProperty({ example: '123456', description: 'ID báo giá' })
    @Column({ type: 'varchar', nullable: true })
    quoteId: string;

    @ApiProperty({ example: '123456', description: 'ID mẫu hợp đồng' })
    @Column({ type: 'varchar', nullable: true })
    templateId: string;

    @ApiProperty({ description: 'Ngày hết hạn hợp đồng' })
    @Column({ type: 'timestamptz', nullable: true })
    dueDate?: Date;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái mẫu hợp đồng' })
    @Column({
        type: 'enum',
        enum: NSContract.EStatus,
        default: NSContract.EStatus.NEW,
    })
    status: NSContract.EStatus;
}
