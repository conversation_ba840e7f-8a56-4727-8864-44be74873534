import { configEnv } from '~/@config/env';
import { createHttpClient } from '~/@core/network';

const { APE_CRM_API_KEY, APE_SSO_URL, APE_PUBLIC_API_KEY } = configEnv();
export const optionalApiConnector = createHttpClient({
    baseURL: '',
    timeout: 2 * 60 * 1000,
    beforeRequest: config => {
        return config;
    },
    handleError: err => {
        return err;
    },
    handleResponse: async res => {
        return res.data;
    },
});

export const apeAuthApiConnector = createHttpClient({
    baseURL: APE_SSO_URL,
    timeout: 2 * 60 * 1000,
    beforeRequest: config => {
        config.headers['x-api-key'] = `ApiKey ${APE_CRM_API_KEY}`;
        return config;
    },
    handleError: err => {
        return err;
    },
    handleResponse: async res => {
        return res.data;
    },
});

export const apePublicApiConnector = createHttpClient({
    baseURL: APE_SSO_URL,
    timeout: 2 * 60 * 1000,
    beforeRequest: config => {
        config.headers['x-api-key'] = `ApiKey ${APE_PUBLIC_API_KEY}`;
        return config;
    },
    handleError: err => {
        return err;
    },
    handleResponse: async res => {
        return res.data;
    },
});
