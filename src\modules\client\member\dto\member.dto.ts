import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSMember } from '~/common/enums';

export class AccountAuthDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Member name', example: '<PERSON>' })
    name?: string;

    @ApiPropertyOptional({ description: 'Member code', example: 'EMP123' })
    code?: string;

    @ApiPropertyOptional({ description: 'Department name', example: 'Sales' })
    departmentId?: string;

    @ApiPropertyOptional({ description: 'Position title', example: 'Manager' })
    positionId?: string;

    @ApiPropertyOptional({ description: 'Status of the member', example: 'active' })
    status?: string;

    @ApiPropertyOptional({ description: 'Start date filter', example: '2023-01-01' })
    dateFrom?: Date;

    @ApiPropertyOptional({ description: 'End date filter', example: '2023-12-31' })
    dateTo?: Date;
}
export class MemberDto {
    @ApiPropertyOptional({
        description: 'Unique identifier of the member',
        example: '550e8400-e29b-41d4-a716-************',
    })
    id?: string;

    @ApiPropertyOptional({ description: 'Full name of the member', example: 'Nguyễn Văn A' })
    fullName?: string;

    @ApiPropertyOptional({
        description: 'Avatar URL of the member',
        example: 'https://example.com/avatar.jpg',
    })
    avatar?: string;

    @ApiPropertyOptional({ description: 'Status of the member', example: 'active' })
    status?: string;
}

export class MemberPaginationDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Member name', example: 'John Doe' })
    @IsOptional()
    fullName?: string;

    @ApiPropertyOptional({ description: 'Member code', example: 'EMP123' })
    @IsOptional()
    code?: string;

    @ApiPropertyOptional({ description: 'Department name', example: 'Sales' })
    @IsOptional()
    departmentId?: string;

    @ApiPropertyOptional({ description: 'Position title', example: 'Manager' })
    @IsOptional()
    positionId?: string;

    @ApiPropertyOptional({ description: 'Status of the member', example: 'active' })
    @IsOptional()
    status?: string;

    @ApiPropertyOptional({ description: 'Start date filter', example: '2023-01-01' })
    @IsOptional()
    dateFrom?: Date;

    @ApiPropertyOptional({ description: 'End date filter', example: '2023-12-31' })
    @IsOptional()
    dateTo?: Date;
}

export class AssignRoleDto {
    @ApiPropertyOptional({
        description: 'Member ID',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsNotEmpty()
    @IsString()
    memberId: string;

    @ApiPropertyOptional({
        description: 'Role IDs',
    })
    @IsNotEmpty()
    @IsArray()
    roleIds: string[];
}

export class CreateMemberDto {
    @ApiProperty({ description: 'Full name of the member', example: 'Nguyễn Văn A' })
    @IsString()
    @IsNotEmpty()
    fullName: string;

    @ApiProperty({ description: 'Username of the member', example: 'nvA' })
    @IsString()
    @IsNotEmpty()
    username: string;

    // Nếu password là optional (tuỳ use case với Auth):
    @ApiPropertyOptional({ description: 'Password of the member', example: '123456' })
    @IsOptional()
    @IsString()
    password?: string;

    @ApiPropertyOptional({
        description: 'Type of the member',
        example: 'EMPLOYEE',
        enum: NSMember.EType,
    })
    @IsOptional()
    @IsEnum(NSMember.EType)
    type?: NSMember.EType;

    @ApiPropertyOptional({
        description: 'Department ID of the member',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsOptional()
    @IsUUID()
    departmentId?: string;

    @ApiPropertyOptional({
        description: 'Position ID of the member',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsOptional()
    @IsUUID()
    positionId?: string;

    @ApiPropertyOptional({
        description: 'Role IDs of the member',
        example: ['550e8400-e29b-41d4-a716-************'],
        isArray: true,
        type: String,
    })
    @IsOptional()
    @IsArray()
    roleIds?: string[];
}

export class UpdateMemberDto extends OmitType(CreateMemberDto, ['password']) {
    @ApiPropertyOptional({
        description: 'ID of the member to update',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsNotEmpty()
    @IsString()
    id: string;
}

export class UpdateStatusDto {
    @ApiPropertyOptional({
        description: 'Member ID',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsNotEmpty()
    @IsString()
    memberId: string;

    @ApiPropertyOptional({
        description: 'Username of the member',
        example: 'nvA',
    })
    @IsNotEmpty()
    @IsString()
    username: string;

    @ApiPropertyOptional({
        description: 'Status of the member',
        example: 'active',
    })
    @IsNotEmpty()
    @IsEnum(NSMember.EStatus)
    status: NSMember.EStatus;
}
