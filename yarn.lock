# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@angular-devkit/core@19.2.15":
  "integrity" "sha512-pU2RZYX6vhd7uLSdLwPnuBcr0mXJSjp3EgOXKsrlQFQZevc+Qs+2JdXgIElnOT/aDqtRtriDmLlSbtdE8n3ZbA=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/core/-/core-19.2.15.tgz"
  "version" "19.2.15"
  dependencies:
    "ajv" "8.17.1"
    "ajv-formats" "3.0.1"
    "jsonc-parser" "3.3.1"
    "picomatch" "4.0.2"
    "rxjs" "7.8.1"
    "source-map" "0.7.4"

"@angular-devkit/core@19.2.17":
  "integrity" "sha512-Ah008x2RJkd0F+NLKqIpA34/vUGwjlprRCkvddjDopAWRzYn6xCkz1Tqwuhn0nR1Dy47wTLKYD999TYl5ONOAQ=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/core/-/core-19.2.17.tgz"
  "version" "19.2.17"
  dependencies:
    "ajv" "8.17.1"
    "ajv-formats" "3.0.1"
    "jsonc-parser" "3.3.1"
    "picomatch" "4.0.2"
    "rxjs" "7.8.1"
    "source-map" "0.7.4"

"@angular-devkit/schematics-cli@19.2.15":
  "integrity" "sha512-1ESFmFGMpGQmalDB3t2EtmWDGv6gOFYBMxmHO2f1KI/UDl8UmZnCGL4mD3EWo8Hv0YIsZ9wOH9Q7ZHNYjeSpzg=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/schematics-cli/-/schematics-cli-19.2.15.tgz"
  "version" "19.2.15"
  dependencies:
    "@angular-devkit/core" "19.2.15"
    "@angular-devkit/schematics" "19.2.15"
    "@inquirer/prompts" "7.3.2"
    "ansi-colors" "4.1.3"
    "symbol-observable" "4.0.0"
    "yargs-parser" "21.1.1"

"@angular-devkit/schematics@19.2.15":
  "integrity" "sha512-kNOJ+3vekJJCQKWihNmxBkarJzNW09kP5a9E1SRNiQVNOUEeSwcRR0qYotM65nx821gNzjjhJXnAZ8OazWldrg=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/schematics/-/schematics-19.2.15.tgz"
  "version" "19.2.15"
  dependencies:
    "@angular-devkit/core" "19.2.15"
    "jsonc-parser" "3.3.1"
    "magic-string" "0.30.17"
    "ora" "5.4.1"
    "rxjs" "7.8.1"

"@angular-devkit/schematics@19.2.17":
  "integrity" "sha512-ADfbaBsrG8mBF6Mfs+crKA/2ykB8AJI50Cv9tKmZfwcUcyAdmTr+vVvhsBCfvUAEokigSsgqgpYxfkJVxhJYeg=="
  "resolved" "https://registry.npmjs.org/@angular-devkit/schematics/-/schematics-19.2.17.tgz"
  "version" "19.2.17"
  dependencies:
    "@angular-devkit/core" "19.2.17"
    "jsonc-parser" "3.3.1"
    "magic-string" "0.30.17"
    "ora" "5.4.1"
    "rxjs" "7.8.1"

"@aws-crypto/sha256-browser@5.2.0":
  "integrity" "sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw=="
  "resolved" "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@aws-crypto/sha256-js" "^5.2.0"
    "@aws-crypto/supports-web-crypto" "^5.2.0"
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@smithy/util-utf8" "^2.0.0"
    "tslib" "^2.6.2"

"@aws-crypto/sha256-js@^5.2.0", "@aws-crypto/sha256-js@5.2.0":
  "integrity" "sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA=="
  "resolved" "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@aws-crypto/util" "^5.2.0"
    "@aws-sdk/types" "^3.222.0"
    "tslib" "^2.6.2"

"@aws-crypto/supports-web-crypto@^5.2.0":
  "integrity" "sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg=="
  "resolved" "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@aws-crypto/util@^5.2.0":
  "integrity" "sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ=="
  "resolved" "https://registry.npmjs.org/@aws-crypto/util/-/util-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@smithy/util-utf8" "^2.0.0"
    "tslib" "^2.6.2"

"@aws-sdk/client-sesv2@^3.839.0":
  "integrity" "sha512-dg36AsIyBJ6BKL0bFTIAnIk7nBsgaYGUJxdTfT/jNpYv+F239Ih7tvZfJSfjd1jvBw4byzlqjdPuKnl9l5ovDg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/client-sesv2/-/client-sesv2-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/credential-provider-node" "3.914.0"
    "@aws-sdk/middleware-host-header" "3.914.0"
    "@aws-sdk/middleware-logger" "3.914.0"
    "@aws-sdk/middleware-recursion-detection" "3.914.0"
    "@aws-sdk/middleware-user-agent" "3.914.0"
    "@aws-sdk/region-config-resolver" "3.914.0"
    "@aws-sdk/signature-v4-multi-region" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/util-endpoints" "3.914.0"
    "@aws-sdk/util-user-agent-browser" "3.914.0"
    "@aws-sdk/util-user-agent-node" "3.914.0"
    "@smithy/config-resolver" "^4.4.0"
    "@smithy/core" "^3.17.0"
    "@smithy/fetch-http-handler" "^5.3.4"
    "@smithy/hash-node" "^4.2.3"
    "@smithy/invalid-dependency" "^4.2.3"
    "@smithy/middleware-content-length" "^4.2.3"
    "@smithy/middleware-endpoint" "^4.3.4"
    "@smithy/middleware-retry" "^4.4.4"
    "@smithy/middleware-serde" "^4.2.3"
    "@smithy/middleware-stack" "^4.2.3"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/node-http-handler" "^4.4.2"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-body-length-browser" "^4.2.0"
    "@smithy/util-body-length-node" "^4.2.1"
    "@smithy/util-defaults-mode-browser" "^4.3.3"
    "@smithy/util-defaults-mode-node" "^4.2.5"
    "@smithy/util-endpoints" "^3.2.3"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-retry" "^4.2.3"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@aws-sdk/client-sso@3.914.0":
  "integrity" "sha512-83Xp8Wl7RDWg/iIYL8dmrN9DN7qu7fcUzDC9LyMhDN8cAEACykN/i4Fk45UHRCejL9Sjxu4wsQzxRYp1smQ95g=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/middleware-host-header" "3.914.0"
    "@aws-sdk/middleware-logger" "3.914.0"
    "@aws-sdk/middleware-recursion-detection" "3.914.0"
    "@aws-sdk/middleware-user-agent" "3.914.0"
    "@aws-sdk/region-config-resolver" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/util-endpoints" "3.914.0"
    "@aws-sdk/util-user-agent-browser" "3.914.0"
    "@aws-sdk/util-user-agent-node" "3.914.0"
    "@smithy/config-resolver" "^4.4.0"
    "@smithy/core" "^3.17.0"
    "@smithy/fetch-http-handler" "^5.3.4"
    "@smithy/hash-node" "^4.2.3"
    "@smithy/invalid-dependency" "^4.2.3"
    "@smithy/middleware-content-length" "^4.2.3"
    "@smithy/middleware-endpoint" "^4.3.4"
    "@smithy/middleware-retry" "^4.4.4"
    "@smithy/middleware-serde" "^4.2.3"
    "@smithy/middleware-stack" "^4.2.3"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/node-http-handler" "^4.4.2"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-body-length-browser" "^4.2.0"
    "@smithy/util-body-length-node" "^4.2.1"
    "@smithy/util-defaults-mode-browser" "^4.3.3"
    "@smithy/util-defaults-mode-node" "^4.2.5"
    "@smithy/util-endpoints" "^3.2.3"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-retry" "^4.2.3"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@aws-sdk/core@3.914.0":
  "integrity" "sha512-QMnWdW7PwxVfi5WBV2a6apM1fIizgBf1UHYbqd3e1sXk8B0d3tpysmLZdIx30OY066zhEo6FyAKLAeTSsGrALg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/core/-/core-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/xml-builder" "3.914.0"
    "@smithy/core" "^3.17.0"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/signature-v4" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-env@3.914.0":
  "integrity" "sha512-v7zeMsLkTB0/ZK6DGbM6QUNIeeEtNBd+4DHihXjsHKBKxBESKIJlWF5Bcj+pgCSWcFGClxmqL6NfWCFQ0WdtjQ=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-http@3.914.0":
  "integrity" "sha512-NXS5nBD0Tbk5ltjOAucdcx8EQQcFdVpCGrly56AIbznl0yhuG5Sxq4q2tUSJj9006eEXBK5rt52CdDixCcv3xg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/fetch-http-handler" "^5.3.4"
    "@smithy/node-http-handler" "^4.4.2"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/util-stream" "^4.5.3"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-ini@3.914.0":
  "integrity" "sha512-RcL02V3EE8DRuu8qb5zoV+aVWbUIKZRA3NeHsWKWCD25nxQUYF4CrbQizWQ91vda5+e6PysGGLYROOzapX3Xmw=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/credential-provider-env" "3.914.0"
    "@aws-sdk/credential-provider-http" "3.914.0"
    "@aws-sdk/credential-provider-process" "3.914.0"
    "@aws-sdk/credential-provider-sso" "3.914.0"
    "@aws-sdk/credential-provider-web-identity" "3.914.0"
    "@aws-sdk/nested-clients" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/credential-provider-imds" "^4.2.3"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-node@3.914.0":
  "integrity" "sha512-SDUvDKqsJ5UPDkem0rq7/bdZtXKKTnoBeWvRlI20Zuv4CLdYkyIGXU9sSA2mrhsZ/7bt1cduTHpGd1n/UdBQEg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/credential-provider-env" "3.914.0"
    "@aws-sdk/credential-provider-http" "3.914.0"
    "@aws-sdk/credential-provider-ini" "3.914.0"
    "@aws-sdk/credential-provider-process" "3.914.0"
    "@aws-sdk/credential-provider-sso" "3.914.0"
    "@aws-sdk/credential-provider-web-identity" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/credential-provider-imds" "^4.2.3"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-process@3.914.0":
  "integrity" "sha512-34C3CYM3iAVcSg3cX4UfOwabWeTeowjZkqJbWgDZ+I/HNZ8+9YbVuJcOZL5fVhw242UclxlVlddNPNprluZKGg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-sso@3.914.0":
  "integrity" "sha512-LfuSyhwvb1qOWN+oN3zyq5D899RZVA0nUrx6czKpDJYarYG0FCTZPO5aPcyoNGAjUu8l+CYUvXcd9ZdZiwv3/A=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/client-sso" "3.914.0"
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/token-providers" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/credential-provider-web-identity@3.914.0":
  "integrity" "sha512-49zJm5x48eG4kiu7/lUGYicwpOPA3lzkuxZ8tdegKKB9Imya6yxdATx4V5UcapFfX79xgpZr750zYHHqSX53Sw=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/nested-clients" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/middleware-host-header@3.914.0":
  "integrity" "sha512-7r9ToySQ15+iIgXMF/h616PcQStByylVkCshmQqcdeynD/lCn2l667ynckxW4+ql0Q+Bo/URljuhJRxVJzydNA=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/middleware-logger@3.914.0":
  "integrity" "sha512-/gaW2VENS5vKvJbcE1umV4Ag3NuiVzpsANxtrqISxT3ovyro29o1RezW/Avz/6oJqjnmgz8soe9J1t65jJdiNg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/middleware-recursion-detection@3.914.0":
  "integrity" "sha512-yiAjQKs5S2JKYc+GrkvGMwkUvhepXDigEXpSJqUseR/IrqHhvGNuOxDxq+8LbDhM4ajEW81wkiBbU+Jl9G82yQ=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@aws/lambda-invoke-store" "^0.0.1"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/middleware-sdk-s3@3.914.0":
  "integrity" "sha512-jcbOc0FxW6p4KRJE/7LFA05cRAeLK4eZK4k4ZaWZOqbkH48WihKaDlxYsppbWa2WnIvVcjPye4kSTncBEFZrPg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/util-arn-parser" "3.893.0"
    "@smithy/core" "^3.17.0"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/signature-v4" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/util-config-provider" "^4.2.0"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-stream" "^4.5.3"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@aws-sdk/middleware-user-agent@3.914.0":
  "integrity" "sha512-+grKWKg+htCpkileNOqm7LO9OrE9nVPv49CYbF7dXefQIdIhfQ0pvm+hdSUnh8GFLx86FKoJs2DZSBCYqgjQFw=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/util-endpoints" "3.914.0"
    "@smithy/core" "^3.17.0"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/nested-clients@3.914.0":
  "integrity" "sha512-cktvDU5qsvtv9HqJ0uoPgqQ87pttRMZe33fdZ3NQmnkaT6O6AI7x9wQNW5bDH3E6rou/jYle9CBSea1Xum69rQ=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/nested-clients/-/nested-clients-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-crypto/sha256-browser" "5.2.0"
    "@aws-crypto/sha256-js" "5.2.0"
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/middleware-host-header" "3.914.0"
    "@aws-sdk/middleware-logger" "3.914.0"
    "@aws-sdk/middleware-recursion-detection" "3.914.0"
    "@aws-sdk/middleware-user-agent" "3.914.0"
    "@aws-sdk/region-config-resolver" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@aws-sdk/util-endpoints" "3.914.0"
    "@aws-sdk/util-user-agent-browser" "3.914.0"
    "@aws-sdk/util-user-agent-node" "3.914.0"
    "@smithy/config-resolver" "^4.4.0"
    "@smithy/core" "^3.17.0"
    "@smithy/fetch-http-handler" "^5.3.4"
    "@smithy/hash-node" "^4.2.3"
    "@smithy/invalid-dependency" "^4.2.3"
    "@smithy/middleware-content-length" "^4.2.3"
    "@smithy/middleware-endpoint" "^4.3.4"
    "@smithy/middleware-retry" "^4.4.4"
    "@smithy/middleware-serde" "^4.2.3"
    "@smithy/middleware-stack" "^4.2.3"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/node-http-handler" "^4.4.2"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-body-length-browser" "^4.2.0"
    "@smithy/util-body-length-node" "^4.2.1"
    "@smithy/util-defaults-mode-browser" "^4.3.3"
    "@smithy/util-defaults-mode-node" "^4.2.5"
    "@smithy/util-endpoints" "^3.2.3"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-retry" "^4.2.3"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@aws-sdk/region-config-resolver@3.914.0":
  "integrity" "sha512-KlmHhRbn1qdwXUdsdrJ7S/MAkkC1jLpQ11n+XvxUUUCGAJd1gjC7AjxPZUM7ieQ2zcb8bfEzIU7al+Q3ZT0u7Q=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@smithy/config-resolver" "^4.4.0"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/signature-v4-multi-region@3.914.0":
  "integrity" "sha512-EOQ/ObGwaRXfK54GnxVRdHFiUvCZ4IJYAHMoQWfF9ZrV/4g0B4eBgJAal+DRO5g1sye32EtaGwFiQ6sULJb/Pw=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/middleware-sdk-s3" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/signature-v4" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/token-providers@3.914.0":
  "integrity" "sha512-wX8lL5OnCk/54eUPP1L/dCH+Gp/f3MjnHR6rNp+dbGs7+omUAub4dEbM/JMBE4Jsn5coiVgmgqx97Q5cRxh/EA=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/core" "3.914.0"
    "@aws-sdk/nested-clients" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/types@^3.222.0", "@aws-sdk/types@3.914.0":
  "integrity" "sha512-kQWPsRDmom4yvAfyG6L1lMmlwnTzm1XwMHOU+G5IFlsP4YEaMtXidDzW/wiivY0QFrhfCz/4TVmu0a2aPU57ug=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/types/-/types-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/util-arn-parser@3.893.0":
  "integrity" "sha512-u8H4f2Zsi19DGnwj5FSZzDMhytYF/bCh37vAtBsn3cNDL3YG578X5oc+wSX54pM3tOxS+NY7tvOAo52SW7koUA=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/util-arn-parser/-/util-arn-parser-3.893.0.tgz"
  "version" "3.893.0"
  dependencies:
    "tslib" "^2.6.2"

"@aws-sdk/util-endpoints@3.914.0":
  "integrity" "sha512-POUBUTjD7WQ/BVoUGluukCIkIDO12IPdwRAvUgFshfbaUdyXFuBllM/6DmdyeR3rJhXnBqe3Uy5e2eXbz/MBTw=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "@smithy/util-endpoints" "^3.2.3"
    "tslib" "^2.6.2"

"@aws-sdk/util-locate-window@^3.0.0":
  "integrity" "sha512-T89pFfgat6c8nMmpI8eKjBcDcgJq36+m9oiXbcUzeU55MP9ZuGgBomGjGnHaEyF36jenW9gmg3NfZDm0AO2XPg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.893.0.tgz"
  "version" "3.893.0"
  dependencies:
    "tslib" "^2.6.2"

"@aws-sdk/util-user-agent-browser@3.914.0":
  "integrity" "sha512-rMQUrM1ECH4kmIwlGl9UB0BtbHy6ZuKdWFrIknu8yGTRI/saAucqNTh5EI1vWBxZ0ElhK5+g7zOnUuhSmVQYUA=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/types" "3.914.0"
    "@smithy/types" "^4.8.0"
    "bowser" "^2.11.0"
    "tslib" "^2.6.2"

"@aws-sdk/util-user-agent-node@3.914.0":
  "integrity" "sha512-gTkLFUZiNPgJmeFCX8VJRmQWXKfF3Imm5IquFIR5c0sCBfhtMjTXZF0dHDW5BlceZ4tFPwfF9sCqWJ52wbFSBg=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@aws-sdk/middleware-user-agent" "3.914.0"
    "@aws-sdk/types" "3.914.0"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@aws-sdk/xml-builder@3.914.0":
  "integrity" "sha512-k75evsBD5TcIjedycYS7QXQ98AmOtbnxRJOPtCo0IwYRmy7UvqgS/gBL5SmrIqeV6FDSYRQMgdBxSMp6MLmdew=="
  "resolved" "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.914.0.tgz"
  "version" "3.914.0"
  dependencies:
    "@smithy/types" "^4.8.0"
    "fast-xml-parser" "5.2.5"
    "tslib" "^2.6.2"

"@aws/lambda-invoke-store@^0.0.1":
  "integrity" "sha512-ORHRQ2tmvnBXc8t/X9Z8IcSbBA4xTLKuN873FopzklHMeqBst7YG0d+AX97inkvDX+NChYtSr+qGfcqGFaI8Zw=="
  "resolved" "https://registry.npmjs.org/@aws/lambda-invoke-store/-/lambda-invoke-store-0.0.1.tgz"
  "version" "0.0.1"

"@azure-rest/core-client@^2.3.3":
  "integrity" "sha512-EHaOXW0RYDKS5CFffnixdyRPak5ytiCtU7uXDcP/uiY+A6jFRwNGzzJBiznkCzvi5EYpY+YWinieqHb0oY916A=="
  "resolved" "https://registry.npmjs.org/@azure-rest/core-client/-/core-client-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-auth" "^1.10.0"
    "@azure/core-rest-pipeline" "^1.22.0"
    "@azure/core-tracing" "^1.3.0"
    "@typespec/ts-http-runtime" "^0.3.0"
    "tslib" "^2.6.2"

"@azure/abort-controller@^2.0.0", "@azure/abort-controller@^2.1.2":
  "integrity" "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA=="
  "resolved" "https://registry.npmjs.org/@azure/abort-controller/-/abort-controller-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "tslib" "^2.6.2"

"@azure/core-auth@^1.10.0", "@azure/core-auth@^1.3.0", "@azure/core-auth@^1.7.2", "@azure/core-auth@^1.9.0":
  "integrity" "sha512-ykRMW8PjVAn+RS6ww5cmK9U2CyH9p4Q88YJwvUslfuMmN98w/2rdGRLPqJYObapBCdzBVeDgYWdJnFPFb7qzpg=="
  "resolved" "https://registry.npmjs.org/@azure/core-auth/-/core-auth-1.10.1.tgz"
  "version" "1.10.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-util" "^1.13.0"
    "tslib" "^2.6.2"

"@azure/core-client@^1.10.0", "@azure/core-client@^1.5.0", "@azure/core-client@^1.9.2":
  "integrity" "sha512-Nh5PhEOeY6PrnxNPsEHRr9eimxLwgLlpmguQaHKBinFYA/RU9+kOYVOQqOrTsCL+KSxrLLl1gD8Dk5BFW/7l/w=="
  "resolved" "https://registry.npmjs.org/@azure/core-client/-/core-client-1.10.1.tgz"
  "version" "1.10.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-auth" "^1.10.0"
    "@azure/core-rest-pipeline" "^1.22.0"
    "@azure/core-tracing" "^1.3.0"
    "@azure/core-util" "^1.13.0"
    "@azure/logger" "^1.3.0"
    "tslib" "^2.6.2"

"@azure/core-http-compat@^2.2.0":
  "integrity" "sha512-az9BkXND3/d5VgdRRQVkiJb2gOmDU8Qcq4GvjtBmDICNiQ9udFmDk4ZpSB5Qq1OmtDJGlQAfBaS4palFsazQ5g=="
  "resolved" "https://registry.npmjs.org/@azure/core-http-compat/-/core-http-compat-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-client" "^1.10.0"
    "@azure/core-rest-pipeline" "^1.22.0"

"@azure/core-lro@^2.7.2":
  "integrity" "sha512-0YIpccoX8m/k00O7mDDMdJpbr6mf1yWo2dfmxt5A8XVZVVMz2SSKaEbMCeJRvgQ0IaSlqhjT47p4hVIRRy90xw=="
  "resolved" "https://registry.npmjs.org/@azure/core-lro/-/core-lro-2.7.2.tgz"
  "version" "2.7.2"
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-util" "^1.2.0"
    "@azure/logger" "^1.0.0"
    "tslib" "^2.6.2"

"@azure/core-paging@^1.6.2":
  "integrity" "sha512-YKWi9YuCU04B55h25cnOYZHxXYtEvQEbKST5vqRga7hWY9ydd3FZHdeQF8pyh+acWZvppw13M/LMGx0LABUVMA=="
  "resolved" "https://registry.npmjs.org/@azure/core-paging/-/core-paging-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "tslib" "^2.6.2"

"@azure/core-rest-pipeline@^1.17.0", "@azure/core-rest-pipeline@^1.19.0", "@azure/core-rest-pipeline@^1.22.0", "@azure/core-rest-pipeline@^1.8.0":
  "integrity" "sha512-UVZlVLfLyz6g3Hy7GNDpooMQonUygH7ghdiSASOOHy97fKj/mPLqgDX7aidOijn+sCMU+WU8NjlPlNTgnvbcGA=="
  "resolved" "https://registry.npmjs.org/@azure/core-rest-pipeline/-/core-rest-pipeline-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-auth" "^1.10.0"
    "@azure/core-tracing" "^1.3.0"
    "@azure/core-util" "^1.13.0"
    "@azure/logger" "^1.3.0"
    "@typespec/ts-http-runtime" "^0.3.0"
    "tslib" "^2.6.2"

"@azure/core-tracing@^1.0.0", "@azure/core-tracing@^1.2.0", "@azure/core-tracing@^1.3.0":
  "integrity" "sha512-9MWKevR7Hz8kNzzPLfX4EAtGM2b8mr50HPDBvio96bURP/9C+HjdH3sBlLSNNrvRAr5/k/svoH457gB5IKpmwQ=="
  "resolved" "https://registry.npmjs.org/@azure/core-tracing/-/core-tracing-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "tslib" "^2.6.2"

"@azure/core-util@^1.10.0", "@azure/core-util@^1.11.0", "@azure/core-util@^1.13.0", "@azure/core-util@^1.2.0":
  "integrity" "sha512-XPArKLzsvl0Hf0CaGyKHUyVgF7oDnhKoP85Xv6M4StF/1AhfORhZudHtOyf2s+FcbuQ9dPRAjB8J2KvRRMUK2A=="
  "resolved" "https://registry.npmjs.org/@azure/core-util/-/core-util-1.13.1.tgz"
  "version" "1.13.1"
  dependencies:
    "@azure/abort-controller" "^2.1.2"
    "@typespec/ts-http-runtime" "^0.3.0"
    "tslib" "^2.6.2"

"@azure/identity@^4.2.1":
  "integrity" "sha512-uWC0fssc+hs1TGGVkkghiaFkkS7NkTxfnCH+Hdg+yTehTpMcehpok4PgUKKdyCH+9ldu6FhiHRv84Ntqj1vVcw=="
  "resolved" "https://registry.npmjs.org/@azure/identity/-/identity-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.9.0"
    "@azure/core-client" "^1.9.2"
    "@azure/core-rest-pipeline" "^1.17.0"
    "@azure/core-tracing" "^1.0.0"
    "@azure/core-util" "^1.11.0"
    "@azure/logger" "^1.0.0"
    "@azure/msal-browser" "^4.2.0"
    "@azure/msal-node" "^3.5.0"
    "open" "^10.1.0"
    "tslib" "^2.2.0"

"@azure/keyvault-common@^2.0.0":
  "integrity" "sha512-wRLVaroQtOqfg60cxkzUkGKrKMsCP6uYXAOomOIysSMyt1/YM0eUn9LqieAWM8DLcU4+07Fio2YGpPeqUbpP9w=="
  "resolved" "https://registry.npmjs.org/@azure/keyvault-common/-/keyvault-common-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@azure/abort-controller" "^2.0.0"
    "@azure/core-auth" "^1.3.0"
    "@azure/core-client" "^1.5.0"
    "@azure/core-rest-pipeline" "^1.8.0"
    "@azure/core-tracing" "^1.0.0"
    "@azure/core-util" "^1.10.0"
    "@azure/logger" "^1.1.4"
    "tslib" "^2.2.0"

"@azure/keyvault-keys@^4.4.0":
  "integrity" "sha512-eDT7iXoBTRZ2n3fLiftuGJFD+yjkiB1GNqzU2KbY1TLYeXeSPVTVgn2eJ5vmRTZ11978jy2Kg2wI7xa9Tyr8ag=="
  "resolved" "https://registry.npmjs.org/@azure/keyvault-keys/-/keyvault-keys-4.10.0.tgz"
  "version" "4.10.0"
  dependencies:
    "@azure-rest/core-client" "^2.3.3"
    "@azure/abort-controller" "^2.1.2"
    "@azure/core-auth" "^1.9.0"
    "@azure/core-http-compat" "^2.2.0"
    "@azure/core-lro" "^2.7.2"
    "@azure/core-paging" "^1.6.2"
    "@azure/core-rest-pipeline" "^1.19.0"
    "@azure/core-tracing" "^1.2.0"
    "@azure/core-util" "^1.11.0"
    "@azure/keyvault-common" "^2.0.0"
    "@azure/logger" "^1.1.4"
    "tslib" "^2.8.1"

"@azure/logger@^1.0.0", "@azure/logger@^1.1.4", "@azure/logger@^1.3.0":
  "integrity" "sha512-fCqPIfOcLE+CGqGPd66c8bZpwAji98tZ4JI9i/mlTNTlsIWslCfpg48s/ypyLxZTump5sypjrKn2/kY7q8oAbA=="
  "resolved" "https://registry.npmjs.org/@azure/logger/-/logger-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "@typespec/ts-http-runtime" "^0.3.0"
    "tslib" "^2.6.2"

"@azure/msal-browser@^4.2.0":
  "integrity" "sha512-kbL+Ae7/UC62wSzxirZddYeVnHvvkvAnSZkBqL55X+jaSXTAXfngnNsDM5acEWU0Q/SAv3gEQfxO1igWOn87Pg=="
  "resolved" "https://registry.npmjs.org/@azure/msal-browser/-/msal-browser-4.25.0.tgz"
  "version" "4.25.0"
  dependencies:
    "@azure/msal-common" "15.13.0"

"@azure/msal-common@15.13.0":
  "integrity" "sha512-8oF6nj02qX7eE/6+wFT5NluXRHc05AgdCC3fJnkjiJooq8u7BcLmxaYYSwc2AfEkWRMRi6Eyvvbeqk4U4412Ag=="
  "resolved" "https://registry.npmjs.org/@azure/msal-common/-/msal-common-15.13.0.tgz"
  "version" "15.13.0"

"@azure/msal-node@^3.5.0":
  "integrity" "sha512-23BXm82Mp5XnRhrcd4mrHa0xuUNRp96ivu3nRatrfdAqjoeWAGyD0eEAafxAOHAEWWmdlyFK4ELFcdziXyw2sA=="
  "resolved" "https://registry.npmjs.org/@azure/msal-node/-/msal-node-3.8.0.tgz"
  "version" "3.8.0"
  dependencies:
    "@azure/msal-common" "15.13.0"
    "jsonwebtoken" "^9.0.0"
    "uuid" "^8.3.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.27.2":
  "integrity" "sha512-YsmSKC29MJwf0gF8Rjjrg5LQCmyh+j/nD8/eP7f+BeoQTKYqs9RoWbjGOdy0+1Ekr68RJZMUOPVQaQisnIo4Rw=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz"
  "version" "7.28.4"

"@babel/core@^7.0.0", "@babel/core@^7.0.0 || ^8.0.0-0", "@babel/core@^7.0.0-0", "@babel/core@^7.11.0 || ^8.0.0-0", "@babel/core@^7.11.0 || ^8.0.0-beta.1", "@babel/core@^7.23.9", "@babel/core@^7.27.4", "@babel/core@>=7.0.0-beta.0 <8":
  "integrity" "sha512-2BCOP7TN8M+gVDj7/ht3hsaO/B/n5oDbiAyyvnRlNOs+u1o+JWNYTQrmpuNp1/Wq2gcFrI01JAW+paEKDMx/CA=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.28.4.tgz"
  "version" "7.28.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.28.3"
    "@babel/helpers" "^7.28.4"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.4"
    "@babel/types" "^7.28.4"
    "@jridgewell/remapping" "^2.3.5"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.27.5", "@babel/generator@^7.28.3":
  "integrity" "sha512-3lSpxGgvnmZznmBkCRnVREPUFJv2wrv9iAoFDvADJc0ypmdOxdUtcLeBgBJ6zE0PMeTKnxeQzyk0xTBq4Ep7zw=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz"
  "version" "7.28.3"
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    "jsesc" "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  "integrity" "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-globals@^7.28.0":
  "integrity" "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz"
  "version" "7.28.0"

"@babel/helper-module-imports@^7.27.1":
  "integrity" "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.28.3":
  "integrity" "sha512-gytXUbs8k2sXS9PnQptz5o0QnpLL51SwASIORY6XaBKF88nsOT0Zw9szLqlSGQDP/4TljBAD5y98p2U1fqkdsw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz"
  "version" "7.28.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  "integrity" "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helpers@^7.28.4":
  "integrity" "sha512-HFN59MmQXGHVyYadKLVumYsA9dBFun/ldYxipEjzA4196jpLZd8UjEEBLkbEkvfYreDqJhZxYAWFPtrfhNpj4w=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz"
  "version" "7.28.4"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.2", "@babel/parser@^7.28.3", "@babel/parser@^7.28.4":
  "integrity" "sha512-yZbBqeM6TkpP9du/I2pUZnJsRMGGvOuIrhjzC1AwHwW+6he4mni6Bp/m8ijn0iOuZuPI2BfkCoSRunpyjnrQKg=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz"
  "version" "7.28.4"
  dependencies:
    "@babel/types" "^7.28.4"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-attributes@^7.24.7":
  "integrity" "sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  "integrity" "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1":
  "integrity" "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.27.1":
  "integrity" "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/template@^7.27.2":
  "integrity" "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.28.3", "@babel/traverse@^7.28.4":
  "integrity" "sha512-YEzuboP2qvQavAcjgQNVgsvHIDv6ZpwXvcvjmyySP2DIMuByS/6ioU5G9pYrWHM6T2YDfc7xga9iNzYOs12CFQ=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz"
  "version" "7.28.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"
    "debug" "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.28.2", "@babel/types@^7.28.4":
  "integrity" "sha512-bkFqkLhh3pMBUQQkpVgWDWq/lqzc2678eUyDlTBhRqhCHFguYYGM0Efga7tYk4TogG/3x0EEl66/OQ+WGbWB/Q=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz"
  "version" "7.28.4"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw=="
  "resolved" "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@borewit/text-codec@^0.1.0":
  "integrity" "sha512-5L/uBxmjaCIX5h8Z+uu+kA9BQLkc/Wl06UGR5ajNRxu+/XjonB5i8JpgFMrPj3LXTCPA0pv8yxUvbUi+QthGGA=="
  "resolved" "https://registry.npmjs.org/@borewit/text-codec/-/text-codec-0.1.1.tgz"
  "version" "0.1.1"

"@cacheable/utils@^2.1.0":
  "integrity" "sha512-ZdxfOiaarMqMj+H7qwlt5EBKWaeGihSYVHdQv5lUsbn8MJJOTW82OIwirQ39U5tMZkNvy3bQE+ryzC+xTAb9/g=="
  "resolved" "https://registry.npmjs.org/@cacheable/utils/-/utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "keyv" "^5.5.3"

"@colors/colors@1.5.0":
  "integrity" "sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ=="
  "resolved" "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz"
  "version" "1.5.0"

"@cspotcode/source-map-support@^0.8.0":
  "integrity" "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw=="
  "resolved" "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  "version" "0.8.1"
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@emnapi/core@^1.4.3":
  "integrity" "sha512-sbP8GzB1WDzacS8fgNPpHlp6C9VZe+SJP3F90W9rLemaQj2PzIuTEl1qDOYQf58YIpyjViI24y9aPWCjEzY2cg=="
  "resolved" "https://registry.npmjs.org/@emnapi/core/-/core-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "@emnapi/wasi-threads" "1.1.0"
    "tslib" "^2.4.0"

"@emnapi/runtime@^1.4.3":
  "integrity" "sha512-97/BJ3iXHww3djw6hYIfErCZFee7qCtrneuLa20UXFCOTCfBM2cvQHjWJ2EG0s0MtdNwInarqCTz35i4wWXHsQ=="
  "resolved" "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "tslib" "^2.4.0"

"@emnapi/wasi-threads@1.1.0":
  "integrity" "sha512-WI0DdZ8xFSbgMjR1sFsKABJ/C5OnRrjT06JXbZKexJGrDuPTzZdDYfFlsgcCXCyf+suG5QU2e/y1Wo2V/OapLQ=="
  "resolved" "https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "tslib" "^2.4.0"

"@eslint-community/eslint-utils@^4.7.0", "@eslint-community/eslint-utils@^4.8.0":
  "integrity" "sha512-ayVFHdtZ+hsq1t2Dy24wCmGXGe4q9Gu3smhLYALJrr473ZH27MsnSL+LKUlimp4BWJqMDMLmPpx/Q9R3OAlL4g=="
  "resolved" "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.9.0.tgz"
  "version" "4.9.0"
  dependencies:
    "eslint-visitor-keys" "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  "integrity" "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ=="
  "resolved" "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  "version" "4.12.1"

"@eslint/config-array@^0.21.0":
  "integrity" "sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ=="
  "resolved" "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.21.0.tgz"
  "version" "0.21.0"
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    "debug" "^4.3.1"
    "minimatch" "^3.1.2"

"@eslint/config-helpers@^0.4.0":
  "integrity" "sha512-WUFvV4WoIwW8Bv0KeKCIIEgdSiFOsulyN0xrMu+7z43q/hkOLXjvb5u7UC9jDxvRzcrbEmuZBX5yJZz1741jog=="
  "resolved" "https://registry.npmjs.org/@eslint/config-helpers/-/config-helpers-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@eslint/core" "^0.16.0"

"@eslint/core@^0.16.0":
  "integrity" "sha512-nmC8/totwobIiFcGkDza3GIKfAw1+hLiYVrh3I1nIomQ8PEr5cxg34jnkmGawul/ep52wGRAcyeDCNtWKSOj4Q=="
  "resolved" "https://registry.npmjs.org/@eslint/core/-/core-0.16.0.tgz"
  "version" "0.16.0"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  "integrity" "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^10.0.1"
    "globals" "^14.0.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@9.37.0":
  "integrity" "sha512-jaS+NJ+hximswBG6pjNX0uEJZkrT0zwpVi3BA3vX22aFGjJjmgSTSmPpZCRKmoBL5VY/M6p0xsSJx7rk7sy5gg=="
  "resolved" "https://registry.npmjs.org/@eslint/js/-/js-9.37.0.tgz"
  "version" "9.37.0"

"@eslint/object-schema@^2.1.6":
  "integrity" "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA=="
  "resolved" "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  "version" "2.1.6"

"@eslint/plugin-kit@^0.4.0":
  "integrity" "sha512-sB5uyeq+dwCWyPi31B2gQlVlo+j5brPlWx4yZBrEaRo/nhdDE8Xke1gsGgtiBdaBTxuTkceLVuVt/pclrasb0A=="
  "resolved" "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@eslint/core" "^0.16.0"
    "levn" "^0.4.1"

"@humanfs/core@^0.19.1":
  "integrity" "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA=="
  "resolved" "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz"
  "version" "0.19.1"

"@humanfs/node@^0.16.6":
  "integrity" "sha512-/zUx+yOsIrG4Y43Eh2peDeKCxlRt/gET6aHfaKpuq267qXdYDFViVHfMaLyygZOnl0kGWxFIgsBy8QFuTLUXEQ=="
  "resolved" "https://registry.npmjs.org/@humanfs/node/-/node-0.16.7.tgz"
  "version" "0.16.7"
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.4.0"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/retry@^0.4.0", "@humanwhocodes/retry@^0.4.2":
  "integrity" "sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz"
  "version" "0.4.3"

"@inquirer/ansi@^1.0.1":
  "integrity" "sha512-yqq0aJW/5XPhi5xOAL1xRCpe1eh8UFVgYFpFsjEqmIR8rKLyP+HINvFXwUaxYICflJrVlxnp7lLN6As735kVpw=="
  "resolved" "https://registry.npmjs.org/@inquirer/ansi/-/ansi-1.0.1.tgz"
  "version" "1.0.1"

"@inquirer/checkbox@^4.1.2", "@inquirer/checkbox@^4.2.0":
  "integrity" "sha512-5+Q3PKH35YsnoPTh75LucALdAxom6xh5D1oeY561x4cqBuH24ZFVyFREPe14xgnrtmGu3EEt1dIi60wRVSnGCw=="
  "resolved" "https://registry.npmjs.org/@inquirer/checkbox/-/checkbox-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "@inquirer/ansi" "^1.0.1"
    "@inquirer/core" "^10.3.0"
    "@inquirer/figures" "^1.0.14"
    "@inquirer/type" "^3.0.9"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/confirm@^5.1.14", "@inquirer/confirm@^5.1.6":
  "integrity" "sha512-wQNz9cfcxrtEnUyG5PndC8g3gZ7lGDBzmWiXZkX8ot3vfZ+/BLjR8EvyGX4YzQLeVqtAlY/YScZpW7CW8qMoDQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/confirm/-/confirm-5.1.19.tgz"
  "version" "5.1.19"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"

"@inquirer/core@^10.3.0":
  "integrity" "sha512-Uv2aPPPSK5jeCplQmQ9xadnFx2Zhj9b5Dj7bU6ZeCdDNNY11nhYy4btcSdtDguHqCT2h5oNeQTcUNSGGLA7NTA=="
  "resolved" "https://registry.npmjs.org/@inquirer/core/-/core-10.3.0.tgz"
  "version" "10.3.0"
  dependencies:
    "@inquirer/ansi" "^1.0.1"
    "@inquirer/figures" "^1.0.14"
    "@inquirer/type" "^3.0.9"
    "cli-width" "^4.1.0"
    "mute-stream" "^2.0.0"
    "signal-exit" "^4.1.0"
    "wrap-ansi" "^6.2.0"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/editor@^4.2.15", "@inquirer/editor@^4.2.7":
  "integrity" "sha512-MjtjOGjr0Kh4BciaFShYpZ1s9400idOdvQ5D7u7lE6VztPFoyLcVNE5dXBmEEIQq5zi4B9h2kU+q7AVBxJMAkQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.21.tgz"
  "version" "4.2.21"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/external-editor" "^1.0.2"
    "@inquirer/type" "^3.0.9"

"@inquirer/expand@^4.0.17", "@inquirer/expand@^4.0.9":
  "integrity" "sha512-+mScLhIcbPFmuvU3tAGBed78XvYHSvCl6dBiYMlzCLhpr0bzGzd8tfivMMeqND6XZiaZ1tgusbUHJEfc6YzOdA=="
  "resolved" "https://registry.npmjs.org/@inquirer/expand/-/expand-4.0.21.tgz"
  "version" "4.0.21"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/external-editor@^1.0.2":
  "integrity" "sha512-yy9cOoBnx58TlsPrIxauKIFQTiyH+0MK4e97y4sV9ERbI+zDxw7i2hxHLCIEGIE/8PPvDxGhgzIOTSOWcs6/MQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/external-editor/-/external-editor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "chardet" "^2.1.0"
    "iconv-lite" "^0.7.0"

"@inquirer/figures@^1.0.14":
  "integrity" "sha512-DbFgdt+9/OZYFM+19dbpXOSeAstPy884FPy1KjDu4anWwymZeOYhMY1mdFri172htv6mvc/uvIAAi7b7tvjJBQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/figures/-/figures-1.0.14.tgz"
  "version" "1.0.14"

"@inquirer/input@^4.1.6", "@inquirer/input@^4.2.1":
  "integrity" "sha512-7GoWev7P6s7t0oJbenH0eQ0ThNdDJbEAEtVt9vsrYZ9FulIokvd823yLyhQlWHJPGce1wzP53ttfdCZmonMHyA=="
  "resolved" "https://registry.npmjs.org/@inquirer/input/-/input-4.2.5.tgz"
  "version" "4.2.5"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"

"@inquirer/number@^3.0.17", "@inquirer/number@^3.0.9":
  "integrity" "sha512-5QWs0KGaNMlhbdhOSCFfKsW+/dcAVC2g4wT/z2MCiZM47uLgatC5N20kpkDQf7dHx+XFct/MJvvNGy6aYJn4Pw=="
  "resolved" "https://registry.npmjs.org/@inquirer/number/-/number-3.0.21.tgz"
  "version" "3.0.21"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"

"@inquirer/password@^4.0.17", "@inquirer/password@^4.0.9":
  "integrity" "sha512-xxeW1V5SbNFNig2pLfetsDb0svWlKuhmr7MPJZMYuDnCTkpVBI+X/doudg4pznc1/U+yYmWFFOi4hNvGgUo7EA=="
  "resolved" "https://registry.npmjs.org/@inquirer/password/-/password-4.0.21.tgz"
  "version" "4.0.21"
  dependencies:
    "@inquirer/ansi" "^1.0.1"
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"

"@inquirer/prompts@7.3.2":
  "integrity" "sha512-G1ytyOoHh5BphmEBxSwALin3n1KGNYB6yImbICcRQdzXfOGbuJ9Jske/Of5Sebk339NSGGNfUshnzK8YWkTPsQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/prompts/-/prompts-7.3.2.tgz"
  "version" "7.3.2"
  dependencies:
    "@inquirer/checkbox" "^4.1.2"
    "@inquirer/confirm" "^5.1.6"
    "@inquirer/editor" "^4.2.7"
    "@inquirer/expand" "^4.0.9"
    "@inquirer/input" "^4.1.6"
    "@inquirer/number" "^3.0.9"
    "@inquirer/password" "^4.0.9"
    "@inquirer/rawlist" "^4.0.9"
    "@inquirer/search" "^3.0.9"
    "@inquirer/select" "^4.0.9"

"@inquirer/prompts@7.8.0":
  "integrity" "sha512-JHwGbQ6wjf1dxxnalDYpZwZxUEosT+6CPGD9Zh4sm9WXdtUp9XODCQD3NjSTmu+0OAyxWXNOqf0spjIymJa2Tw=="
  "resolved" "https://registry.npmjs.org/@inquirer/prompts/-/prompts-7.8.0.tgz"
  "version" "7.8.0"
  dependencies:
    "@inquirer/checkbox" "^4.2.0"
    "@inquirer/confirm" "^5.1.14"
    "@inquirer/editor" "^4.2.15"
    "@inquirer/expand" "^4.0.17"
    "@inquirer/input" "^4.2.1"
    "@inquirer/number" "^3.0.17"
    "@inquirer/password" "^4.0.17"
    "@inquirer/rawlist" "^4.1.5"
    "@inquirer/search" "^3.1.0"
    "@inquirer/select" "^4.3.1"

"@inquirer/rawlist@^4.0.9", "@inquirer/rawlist@^4.1.5":
  "integrity" "sha512-AWpxB7MuJrRiSfTKGJ7Y68imYt8P9N3Gaa7ySdkFj1iWjr6WfbGAhdZvw/UnhFXTHITJzxGUI9k8IX7akAEBCg=="
  "resolved" "https://registry.npmjs.org/@inquirer/rawlist/-/rawlist-4.1.9.tgz"
  "version" "4.1.9"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/type" "^3.0.9"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/search@^3.0.9", "@inquirer/search@^3.1.0":
  "integrity" "sha512-a5SzB/qrXafDX1Z4AZW3CsVoiNxcIYCzYP7r9RzrfMpaLpB+yWi5U8BWagZyLmwR0pKbbL5umnGRd0RzGVI8bQ=="
  "resolved" "https://registry.npmjs.org/@inquirer/search/-/search-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@inquirer/core" "^10.3.0"
    "@inquirer/figures" "^1.0.14"
    "@inquirer/type" "^3.0.9"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/select@^4.0.9", "@inquirer/select@^4.3.1":
  "integrity" "sha512-kaC3FHsJZvVyIjYBs5Ih8y8Bj4P/QItQWrZW22WJax7zTN+ZPXVGuOM55vzbdCP9zKUiBd9iEJVdesujfF+cAA=="
  "resolved" "https://registry.npmjs.org/@inquirer/select/-/select-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "@inquirer/ansi" "^1.0.1"
    "@inquirer/core" "^10.3.0"
    "@inquirer/figures" "^1.0.14"
    "@inquirer/type" "^3.0.9"
    "yoctocolors-cjs" "^2.1.2"

"@inquirer/type@^3.0.9":
  "integrity" "sha512-QPaNt/nmE2bLGQa9b7wwyRJoLZ7pN6rcyXvzU0YCmivmJyq1BVo94G98tStRWkoD1RgDX5C+dPlhhHzNdu/W/w=="
  "resolved" "https://registry.npmjs.org/@inquirer/type/-/type-3.0.9.tgz"
  "version" "3.0.9"

"@isaacs/balanced-match@^4.0.1":
  "integrity" "sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ=="
  "resolved" "https://registry.npmjs.org/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz"
  "version" "4.0.1"

"@isaacs/brace-expansion@^5.0.0":
  "integrity" "sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA=="
  "resolved" "https://registry.npmjs.org/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@isaacs/balanced-match" "^4.0.1"

"@isaacs/cliui@^8.0.2":
  "integrity" "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA=="
  "resolved" "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "string-width" "^5.1.2"
    "string-width-cjs" "npm:string-width@^4.2.0"
    "strip-ansi" "^7.0.1"
    "strip-ansi-cjs" "npm:strip-ansi@^6.0.1"
    "wrap-ansi" "^8.1.0"
    "wrap-ansi-cjs" "npm:wrap-ansi@^7.0.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  "integrity" "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA=="
  "resolved" "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jest/console@30.2.0":
  "integrity" "sha512-+O1ifRjkvYIkBqASKWgLxrpEhQAAE7hY77ALLUufSk5717KfOShg6IbqLmdsLMPdUiFvA2kTs0R7YZy+l0IzZQ=="
  "resolved" "https://registry.npmjs.org/@jest/console/-/console-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "jest-message-util" "30.2.0"
    "jest-util" "30.2.0"
    "slash" "^3.0.0"

"@jest/core@30.2.0":
  "integrity" "sha512-03W6IhuhjqTlpzh/ojut/pDB2LPRygyWX8ExpgHtQA8H/3K7+1vKmcINx5UzeOX1se6YEsBsOHQ1CRzf3fOwTQ=="
  "resolved" "https://registry.npmjs.org/@jest/core/-/core-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/console" "30.2.0"
    "@jest/pattern" "30.0.1"
    "@jest/reporters" "30.2.0"
    "@jest/test-result" "30.2.0"
    "@jest/transform" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "ansi-escapes" "^4.3.2"
    "chalk" "^4.1.2"
    "ci-info" "^4.2.0"
    "exit-x" "^0.2.2"
    "graceful-fs" "^4.2.11"
    "jest-changed-files" "30.2.0"
    "jest-config" "30.2.0"
    "jest-haste-map" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-regex-util" "30.0.1"
    "jest-resolve" "30.2.0"
    "jest-resolve-dependencies" "30.2.0"
    "jest-runner" "30.2.0"
    "jest-runtime" "30.2.0"
    "jest-snapshot" "30.2.0"
    "jest-util" "30.2.0"
    "jest-validate" "30.2.0"
    "jest-watcher" "30.2.0"
    "micromatch" "^4.0.8"
    "pretty-format" "30.2.0"
    "slash" "^3.0.0"

"@jest/diff-sequences@30.0.1":
  "integrity" "sha512-n5H8QLDJ47QqbCNn5SuFjCRDrOLEZ0h8vAHCK5RL9Ls7Xa8AQLa/YxAc9UjFqoEDM48muwtBGjtMY5cr0PLDCw=="
  "resolved" "https://registry.npmjs.org/@jest/diff-sequences/-/diff-sequences-30.0.1.tgz"
  "version" "30.0.1"

"@jest/environment@30.2.0":
  "integrity" "sha512-/QPTL7OBJQ5ac09UDRa3EQes4gt1FTEG/8jZ/4v5IVzx+Cv7dLxlVIvfvSVRiiX2drWyXeBjkMSR8hvOWSog5g=="
  "resolved" "https://registry.npmjs.org/@jest/environment/-/environment-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/fake-timers" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "jest-mock" "30.2.0"

"@jest/expect-utils@30.2.0":
  "integrity" "sha512-1JnRfhqpD8HGpOmQp180Fo9Zt69zNtC+9lR+kT7NVL05tNXIi+QC8Csz7lfidMoVLPD3FnOtcmp0CEFnxExGEA=="
  "resolved" "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/get-type" "30.1.0"

"@jest/expect@30.2.0":
  "integrity" "sha512-V9yxQK5erfzx99Sf+7LbhBwNWEZ9eZay8qQ9+JSC0TrMR1pMDHLMY+BnVPacWU6Jamrh252/IKo4F1Xn/zfiqA=="
  "resolved" "https://registry.npmjs.org/@jest/expect/-/expect-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "expect" "30.2.0"
    "jest-snapshot" "30.2.0"

"@jest/fake-timers@30.2.0":
  "integrity" "sha512-HI3tRLjRxAbBy0VO8dqqm7Hb2mIa8d5bg/NJkyQcOk7V118ObQML8RC5luTF/Zsg4474a+gDvhce7eTnP4GhYw=="
  "resolved" "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "@sinonjs/fake-timers" "^13.0.0"
    "@types/node" "*"
    "jest-message-util" "30.2.0"
    "jest-mock" "30.2.0"
    "jest-util" "30.2.0"

"@jest/get-type@30.1.0":
  "integrity" "sha512-eMbZE2hUnx1WV0pmURZY9XoXPkUYjpc55mb0CrhtdWLtzMQPFvu/rZkTLZFTsdaVQa+Tr4eWAteqcUzoawq/uA=="
  "resolved" "https://registry.npmjs.org/@jest/get-type/-/get-type-30.1.0.tgz"
  "version" "30.1.0"

"@jest/globals@30.2.0":
  "integrity" "sha512-b63wmnKPaK+6ZZfpYhz9K61oybvbI1aMcIs80++JI1O1rR1vaxHUCNqo3ITu6NU0d4V34yZFoHMn/uoKr/Rwfw=="
  "resolved" "https://registry.npmjs.org/@jest/globals/-/globals-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/environment" "30.2.0"
    "@jest/expect" "30.2.0"
    "@jest/types" "30.2.0"
    "jest-mock" "30.2.0"

"@jest/pattern@30.0.1":
  "integrity" "sha512-gWp7NfQW27LaBQz3TITS8L7ZCQ0TLvtmI//4OwlQRx4rnWxcPNIYjxZpDcN4+UlGxgm3jS5QPz8IPTCkb59wZA=="
  "resolved" "https://registry.npmjs.org/@jest/pattern/-/pattern-30.0.1.tgz"
  "version" "30.0.1"
  dependencies:
    "@types/node" "*"
    "jest-regex-util" "30.0.1"

"@jest/reporters@30.2.0":
  "integrity" "sha512-DRyW6baWPqKMa9CzeiBjHwjd8XeAyco2Vt8XbcLFjiwCOEKOvy82GJ8QQnJE9ofsxCMPjH4MfH8fCWIHHDKpAQ=="
  "resolved" "https://registry.npmjs.org/@jest/reporters/-/reporters-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "30.2.0"
    "@jest/test-result" "30.2.0"
    "@jest/transform" "30.2.0"
    "@jest/types" "30.2.0"
    "@jridgewell/trace-mapping" "^0.3.25"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "collect-v8-coverage" "^1.0.2"
    "exit-x" "^0.2.2"
    "glob" "^10.3.10"
    "graceful-fs" "^4.2.11"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^6.0.0"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^5.0.0"
    "istanbul-reports" "^3.1.3"
    "jest-message-util" "30.2.0"
    "jest-util" "30.2.0"
    "jest-worker" "30.2.0"
    "slash" "^3.0.0"
    "string-length" "^4.0.2"
    "v8-to-istanbul" "^9.0.1"

"@jest/schemas@30.0.5":
  "integrity" "sha512-DmdYgtezMkh3cpU8/1uyXakv3tJRcmcXxBOcO0tbaozPwpmh4YMsnWrQm9ZmZMfa5ocbxzbFk6O4bDPEc/iAnA=="
  "resolved" "https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.5.tgz"
  "version" "30.0.5"
  dependencies:
    "@sinclair/typebox" "^0.34.0"

"@jest/snapshot-utils@30.2.0":
  "integrity" "sha512-0aVxM3RH6DaiLcjj/b0KrIBZhSX1373Xci4l3cW5xiUWPctZ59zQ7jj4rqcJQ/Z8JuN/4wX3FpJSa3RssVvCug=="
  "resolved" "https://registry.npmjs.org/@jest/snapshot-utils/-/snapshot-utils-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "chalk" "^4.1.2"
    "graceful-fs" "^4.2.11"
    "natural-compare" "^1.4.0"

"@jest/source-map@30.0.1":
  "integrity" "sha512-MIRWMUUR3sdbP36oyNyhbThLHyJ2eEDClPCiHVbrYAe5g3CHRArIVpBw7cdSB5fr+ofSfIb2Tnsw8iEHL0PYQg=="
  "resolved" "https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.1.tgz"
  "version" "30.0.1"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    "callsites" "^3.1.0"
    "graceful-fs" "^4.2.11"

"@jest/test-result@30.2.0":
  "integrity" "sha512-RF+Z+0CCHkARz5HT9mcQCBulb1wgCP3FBvl9VFokMX27acKphwyQsNuWH3c+ojd1LeWBLoTYoxF0zm6S/66mjg=="
  "resolved" "https://registry.npmjs.org/@jest/test-result/-/test-result-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/console" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/istanbul-lib-coverage" "^2.0.6"
    "collect-v8-coverage" "^1.0.2"

"@jest/test-sequencer@30.2.0":
  "integrity" "sha512-wXKgU/lk8fKXMu/l5Hog1R61bL4q5GCdT6OJvdAFz1P+QrpoFuLU68eoKuVc4RbrTtNnTL5FByhWdLgOPSph+Q=="
  "resolved" "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/test-result" "30.2.0"
    "graceful-fs" "^4.2.11"
    "jest-haste-map" "30.2.0"
    "slash" "^3.0.0"

"@jest/transform@^29.0.0 || ^30.0.0", "@jest/transform@30.2.0":
  "integrity" "sha512-XsauDV82o5qXbhalKxD7p4TZYYdwcaEXC77PPD2HixEFF+6YGppjrAAQurTl2ECWcEomHBMMNS9AH3kcCFx8jA=="
  "resolved" "https://registry.npmjs.org/@jest/transform/-/transform-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/types" "30.2.0"
    "@jridgewell/trace-mapping" "^0.3.25"
    "babel-plugin-istanbul" "^7.0.1"
    "chalk" "^4.1.2"
    "convert-source-map" "^2.0.0"
    "fast-json-stable-stringify" "^2.1.0"
    "graceful-fs" "^4.2.11"
    "jest-haste-map" "30.2.0"
    "jest-regex-util" "30.0.1"
    "jest-util" "30.2.0"
    "micromatch" "^4.0.8"
    "pirates" "^4.0.7"
    "slash" "^3.0.0"
    "write-file-atomic" "^5.0.1"

"@jest/types@^29.0.0 || ^30.0.0", "@jest/types@30.2.0":
  "integrity" "sha512-H9xg1/sfVvyfU7o3zMfBEjQ1gcsdeTMgqHoYdN79tuLqfTtuu7WckRA1R5whDwOzxaZAeMKTYWqP+WCAi0CHsg=="
  "resolved" "https://registry.npmjs.org/@jest/types/-/types-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/pattern" "30.0.1"
    "@jest/schemas" "30.0.5"
    "@types/istanbul-lib-coverage" "^2.0.6"
    "@types/istanbul-reports" "^3.0.4"
    "@types/node" "*"
    "@types/yargs" "^17.0.33"
    "chalk" "^4.1.2"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-2kkt/7niJ6MgEPxF0bYdQ6etZaA+fQvDcLKckhy1yIQOzaoKjBBjSj63/aLVjYE3qhRt5dvM+uUyfCg6UKCBbA=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz"
  "version" "0.3.13"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/remapping@^2.3.5":
  "integrity" "sha512-LI9u/+laYG4Ds1TDKSJW2YPrIlcVYOwi2fUC6xB43lueCjgxV4lffOCZCtYFiH6TNOX+tQKXx97T4IKHbhyHEQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz"
  "version" "2.3.5"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/source-map@^0.3.3":
  "integrity" "sha512-ZMp1V8ZFcPG5dIWnQLr3NSI1MiCU7UETdS/A0G8V/XWHvJv3ZsFqutJn1Y5RPmAPX6F3BiE397OqveU/9NCuIA=="
  "resolved" "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.11.tgz"
  "version" "0.3.11"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  "integrity" "sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz"
  "version" "1.5.5"

"@jridgewell/trace-mapping@^0.3.12":
  "integrity" "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz"
  "version" "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@^0.3.23":
  "integrity" "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz"
  "version" "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@^0.3.24":
  "integrity" "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz"
  "version" "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@^0.3.25":
  "integrity" "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz"
  "version" "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@^0.3.28":
  "integrity" "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz"
  "version" "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@0.3.9":
  "integrity" "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  "version" "0.3.9"
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@js-joda/core@^5.6.1":
  "integrity" "sha512-3zwefSMwHpu8iVUW8YYz227sIv6UFqO31p1Bf1ZH/Vom7CmNyUsXjDBlnNzcuhmOL1XfxZ3nvND42kR23XlbcQ=="
  "resolved" "https://registry.npmjs.org/@js-joda/core/-/core-5.6.5.tgz"
  "version" "5.6.5"

"@keyv/serialize@^1.1.1":
  "integrity" "sha512-dXn3FZhPv0US+7dtJsIi2R+c7qWYiReoEh5zUntWCf4oSpMNib8FDhSoed6m3QyZdx5hK7iLFkYk3rNxwt8vTA=="
  "resolved" "https://registry.npmjs.org/@keyv/serialize/-/serialize-1.1.1.tgz"
  "version" "1.1.1"

"@lukeed/csprng@^1.0.0":
  "integrity" "sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA=="
  "resolved" "https://registry.npmjs.org/@lukeed/csprng/-/csprng-1.1.0.tgz"
  "version" "1.1.0"

"@microsoft/tsdoc@0.15.1":
  "integrity" "sha512-4aErSrCR/On/e5G2hDP0wjooqDdauzEbIq8hIkIe5pXV0rtWJZvdCEKL0ykZxex+IxIwBp0eGeV48hQN07dXtw=="
  "resolved" "https://registry.npmjs.org/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz"
  "version" "0.15.1"

"@napi-rs/wasm-runtime@^0.2.11":
  "integrity" "sha512-ZVWUcfwY4E/yPitQJl481FjFo3K22D6qF0DuFH6Y/nbnE11GY5uguDxZMGXPQ8WQ0128MXQD7TnfHyK4oWoIJQ=="
  "resolved" "https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz"
  "version" "0.2.12"
  dependencies:
    "@emnapi/core" "^1.4.3"
    "@emnapi/runtime" "^1.4.3"
    "@tybys/wasm-util" "^0.10.0"

"@nestjs/cache-manager@^3.0.1":
  "integrity" "sha512-4UxTnR0fsmKL5YDalU2eLFVnL+OBebWUpX+hEduKGncrVKH4PPNoiRn1kXyOCjmzb0UvWgqubpssNouc8e0MCw=="
  "resolved" "https://registry.npmjs.org/@nestjs/cache-manager/-/cache-manager-3.0.1.tgz"
  "version" "3.0.1"

"@nestjs/cli@^11.0.7":
  "integrity" "sha512-4waDT0yGWANg0pKz4E47+nUrqIJv/UqrZ5wLPkCqc7oMGRMWKAaw1NDZ9rKsaqhqvxb2LfI5+uXOWr4yi94DOQ=="
  "resolved" "https://registry.npmjs.org/@nestjs/cli/-/cli-11.0.10.tgz"
  "version" "11.0.10"
  dependencies:
    "@angular-devkit/core" "19.2.15"
    "@angular-devkit/schematics" "19.2.15"
    "@angular-devkit/schematics-cli" "19.2.15"
    "@inquirer/prompts" "7.8.0"
    "@nestjs/schematics" "^11.0.1"
    "ansis" "4.1.0"
    "chokidar" "4.0.3"
    "cli-table3" "0.6.5"
    "commander" "4.1.1"
    "fork-ts-checker-webpack-plugin" "9.1.0"
    "glob" "11.0.3"
    "node-emoji" "1.11.0"
    "ora" "5.4.1"
    "tree-kill" "1.2.2"
    "tsconfig-paths" "4.2.0"
    "tsconfig-paths-webpack-plugin" "4.2.0"
    "typescript" "5.8.3"
    "webpack" "5.100.2"
    "webpack-node-externals" "3.0.0"

"@nestjs/common@*", "@nestjs/common@^10.0.0 || ^11.0.0", "@nestjs/common@^11.0.0", "@nestjs/common@^11.0.1", "@nestjs/common@^11.1.3", "@nestjs/common@^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/common@^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/common@>=8.0.0":
  "integrity" "sha512-krKwLLcFmeuKDqngG2N/RuZHCs2ycsKcxWIDgcm7i1lf3sQ0iG03ci+DsP/r3FcT/eJDFsIHnKtNta2LIi7PzQ=="
  "resolved" "https://registry.npmjs.org/@nestjs/common/-/common-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "file-type" "21.0.0"
    "iterare" "1.2.1"
    "load-esm" "1.0.2"
    "tslib" "2.8.1"
    "uid" "2.0.2"

"@nestjs/config@^4.0.2":
  "integrity" "sha512-McMW6EXtpc8+CwTUwFdg6h7dYcBUpH5iUILCclAsa+MbCEvC9ZKu4dCHRlJqALuhjLw97pbQu62l4+wRwGeZqA=="
  "resolved" "https://registry.npmjs.org/@nestjs/config/-/config-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "dotenv" "16.4.7"
    "dotenv-expand" "12.0.1"
    "lodash" "4.17.21"

"@nestjs/core@*", "@nestjs/core@^10.0.0 || ^11.0.0", "@nestjs/core@^11.0.0", "@nestjs/core@^11.0.1", "@nestjs/core@^11.1.3", "@nestjs/core@^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/core@>=8.0.0":
  "integrity" "sha512-siWX7UDgErisW18VTeJA+x+/tpNZrJewjTBsRPF3JVxuWRuAB1kRoiJcxHgln8Lb5UY9NdvklITR84DUEXD0Cg=="
  "resolved" "https://registry.npmjs.org/@nestjs/core/-/core-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "@nuxt/opencollective" "0.4.1"
    "fast-safe-stringify" "2.1.1"
    "iterare" "1.2.1"
    "path-to-regexp" "8.2.0"
    "tslib" "2.8.1"
    "uid" "2.0.2"

"@nestjs/jwt@^11.0.0":
  "integrity" "sha512-HXSsc7SAnCnjA98TsZqrE7trGtHDnYXWp4Ffy6LwSmck1QvbGYdMzBquXofX5l6tIRpeY4Qidl2Ti2CVG77Pdw=="
  "resolved" "https://registry.npmjs.org/@nestjs/jwt/-/jwt-11.0.1.tgz"
  "version" "11.0.1"
  dependencies:
    "@types/jsonwebtoken" "9.0.10"
    "jsonwebtoken" "9.0.2"

"@nestjs/mapped-types@2.1.0":
  "integrity" "sha512-W+n+rM69XsFdwORF11UqJahn4J3xi4g/ZEOlJNL6KoW5ygWSmBB2p0S2BZ4FQeS/NDH72e6xIcu35SfJnE8bXw=="
  "resolved" "https://registry.npmjs.org/@nestjs/mapped-types/-/mapped-types-2.1.0.tgz"
  "version" "2.1.0"

"@nestjs/passport@^11.0.5":
  "integrity" "sha512-ulQX6mbjlws92PIM15Naes4F4p2JoxGnIJuUsdXQPT+Oo2sqQmENEZXM7eYuimocfHnKlcfZOuyzbA33LwUlOQ=="
  "resolved" "https://registry.npmjs.org/@nestjs/passport/-/passport-11.0.5.tgz"
  "version" "11.0.5"

"@nestjs/platform-express@^11.0.0", "@nestjs/platform-express@^11.1.3":
  "integrity" "sha512-HErwPmKnk+loTq8qzu1up+k7FC6Kqa8x6lJ4cDw77KnTxLzsCaPt+jBvOq6UfICmfqcqCCf3dKXg+aObQp+kIQ=="
  "resolved" "https://registry.npmjs.org/@nestjs/platform-express/-/platform-express-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "cors" "2.8.5"
    "express" "5.1.0"
    "multer" "2.0.2"
    "path-to-regexp" "8.2.0"
    "tslib" "2.8.1"

"@nestjs/platform-socket.io@^11.0.0", "@nestjs/platform-socket.io@^11.1.3":
  "integrity" "sha512-ozm+OKiRiFLNQdFLA3ULDuazgdVaPrdRdgtG/+404T7tcROXpbUuFL0eEmWJpG64CxMkBNwamclUSH6J0AeU7A=="
  "resolved" "https://registry.npmjs.org/@nestjs/platform-socket.io/-/platform-socket.io-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "socket.io" "4.8.1"
    "tslib" "2.8.1"

"@nestjs/schedule@^6.0.0":
  "integrity" "sha512-v3yO6cSPAoBSSyH67HWnXHzuhPhSNZhRmLY38JvCt2sqY8sPMOODpcU1D79iUMFf7k16DaMEbL4Mgx61ZhiC8Q=="
  "resolved" "https://registry.npmjs.org/@nestjs/schedule/-/schedule-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "cron" "4.3.3"

"@nestjs/schematics@^11.0.1", "@nestjs/schematics@^11.0.5":
  "integrity" "sha512-0NfPbPlEaGwIT8/TCThxLzrlz3yzDNkfRNpbL7FiplKq3w4qXpJg0JYwqgMEJnLQZm3L/L/5XjoyfJHUO3qX9g=="
  "resolved" "https://registry.npmjs.org/@nestjs/schematics/-/schematics-11.0.9.tgz"
  "version" "11.0.9"
  dependencies:
    "@angular-devkit/core" "19.2.17"
    "@angular-devkit/schematics" "19.2.17"
    "comment-json" "4.4.1"
    "jsonc-parser" "3.3.1"
    "pluralize" "8.0.0"

"@nestjs/swagger@^11.2.0":
  "integrity" "sha512-5wolt8GmpNcrQv34tIPUtPoV1EeFbCetm40Ij3+M0FNNnf2RJ3FyWfuQvI8SBlcJyfaounYVTKzKHreFXsUyOg=="
  "resolved" "https://registry.npmjs.org/@nestjs/swagger/-/swagger-11.2.0.tgz"
  "version" "11.2.0"
  dependencies:
    "@microsoft/tsdoc" "0.15.1"
    "@nestjs/mapped-types" "2.1.0"
    "js-yaml" "4.1.0"
    "lodash" "4.17.21"
    "path-to-regexp" "8.2.0"
    "swagger-ui-dist" "5.21.0"

"@nestjs/testing@^11.1.3":
  "integrity" "sha512-srYzzDNxGvVCe1j0SpTS9/ix75PKt6Sn6iMaH1rpJ6nj2g8vwNrhK0CoJJXvpCYgrnI+2WES2pprYnq8rAMYHA=="
  "resolved" "https://registry.npmjs.org/@nestjs/testing/-/testing-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "tslib" "2.8.1"

"@nestjs/typeorm@>=8.1.0", "@nestjs/typeorm@11.0.0":
  "integrity" "sha512-SOeUQl70Lb2OfhGkvnh4KXWlsd+zA08RuuQgT7kKbzivngxzSo1Oc7Usu5VxCxACQC9wc2l9esOHILSJeK7rJA=="
  "resolved" "https://registry.npmjs.org/@nestjs/typeorm/-/typeorm-11.0.0.tgz"
  "version" "11.0.0"

"@nestjs/websockets@^11.0.0", "@nestjs/websockets@^11.1.3":
  "integrity" "sha512-jlBX5QpqhfEVfxkwxTesIjgl0bdhgFMoORQYzjRg1i+Z+Qouf4KmjNPv5DZE3DZRDg91E+3Bpn0VgW0Yfl94ng=="
  "resolved" "https://registry.npmjs.org/@nestjs/websockets/-/websockets-11.1.6.tgz"
  "version" "11.1.6"
  dependencies:
    "iterare" "1.2.1"
    "object-hash" "3.0.0"
    "tslib" "2.8.1"

"@noble/hashes@^1.1.5":
  "integrity" "sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A=="
  "resolved" "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz"
  "version" "1.8.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@nuxt/opencollective@0.4.1":
  "integrity" "sha512-GXD3wy50qYbxCJ652bDrDzgMr3NFEkIS374+IgFQKkCvk9yiYcLvX2XDYr7UyQxf4wK0e+yqDYRubZ0DtOxnmQ=="
  "resolved" "https://registry.npmjs.org/@nuxt/opencollective/-/opencollective-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "consola" "^3.2.3"

"@paralleldrive/cuid2@^2.2.2":
  "integrity" "sha512-ZOBkgDwEdoYVlSeRbYYXs0S9MejQofiVYoTbKzy/6GQa39/q5tQU2IX46+shYnUkpEl3wc+J6wRlar7r2EK2xA=="
  "resolved" "https://registry.npmjs.org/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "@noble/hashes" "^1.1.5"

"@pkgjs/parseargs@^0.11.0":
  "integrity" "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg=="
  "resolved" "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  "version" "0.11.0"

"@pkgr/core@^0.2.9":
  "integrity" "sha512-QNqXyfVS2wm9hweSYD2O7F0G06uurj9kZ96TRQE5Y9hU7+tgdZwIkbAKc5Ocy1HxEY2kuDQa6cQ1WRs/O5LFKA=="
  "resolved" "https://registry.npmjs.org/@pkgr/core/-/core-0.2.9.tgz"
  "version" "0.2.9"

"@scarf/scarf@=1.4.0":
  "integrity" "sha512-xxeapPiUXdZAE3che6f3xogoJPeZgig6omHEy1rIY5WVsB3H2BHNnZH+gHG6x91SCWyQCzWGsuL2Hh3ClO5/qQ=="
  "resolved" "https://registry.npmjs.org/@scarf/scarf/-/scarf-1.4.0.tgz"
  "version" "1.4.0"

"@sinclair/typebox@^0.34.0":
  "integrity" "sha512-********************************/CeDnrzsVz8TTIWUbOBr6gnzOmTYJ3eXQNh4IYHIGi5aIL7sOZ2G/g=="
  "resolved" "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.34.41.tgz"
  "version" "0.34.41"

"@sinonjs/commons@^3.0.1":
  "integrity" "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ=="
  "resolved" "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "type-detect" "4.0.8"

"@sinonjs/fake-timers@^13.0.0":
  "integrity" "sha512-36/hTbH2uaWuGVERyC6da9YwGWnzUZXuPro/F2LfsdOsLnCojz/iSH8MxUt/FD2S5XBSVPhmArFUXcpCQ2Hkiw=="
  "resolved" "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz"
  "version" "13.0.5"
  dependencies:
    "@sinonjs/commons" "^3.0.1"

"@smithy/abort-controller@^4.2.3":
  "integrity" "sha512-xWL9Mf8b7tIFuAlpjKtRPnHrR8XVrwTj5NPYO/QwZPtc0SDLsPxb56V5tzi5yspSMytISHybifez+4jlrx0vkQ=="
  "resolved" "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/config-resolver@^4.4.0":
  "integrity" "sha512-Kkmz3Mup2PGp/HNJxhCWkLNdlajJORLSjwkcfrj0E7nu6STAEdcMR1ir5P9/xOmncx8xXfru0fbUYLlZog/cFg=="
  "resolved" "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "@smithy/util-config-provider" "^4.2.0"
    "@smithy/util-endpoints" "^3.2.3"
    "@smithy/util-middleware" "^4.2.3"
    "tslib" "^2.6.2"

"@smithy/core@^3.17.0":
  "integrity" "sha512-Tir3DbfoTO97fEGUZjzGeoXgcQAUBRDTmuH9A8lxuP8ATrgezrAJ6cLuRvwdKN4ZbYNlHgKlBX69Hyu3THYhtg=="
  "resolved" "https://registry.npmjs.org/@smithy/core/-/core-3.17.0.tgz"
  "version" "3.17.0"
  dependencies:
    "@smithy/middleware-serde" "^4.2.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-body-length-browser" "^4.2.0"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-stream" "^4.5.3"
    "@smithy/util-utf8" "^4.2.0"
    "@smithy/uuid" "^1.1.0"
    "tslib" "^2.6.2"

"@smithy/credential-provider-imds@^4.2.3":
  "integrity" "sha512-hA1MQ/WAHly4SYltJKitEsIDVsNmXcQfYBRv2e+q04fnqtAX5qXaybxy/fhUeAMCnQIdAjaGDb04fMHQefWRhw=="
  "resolved" "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "tslib" "^2.6.2"

"@smithy/fetch-http-handler@^5.3.4":
  "integrity" "sha512-bwigPylvivpRLCm+YK9I5wRIYjFESSVwl8JQ1vVx/XhCw0PtCi558NwTnT2DaVCl5pYlImGuQTSwMsZ+pIavRw=="
  "resolved" "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.3.4.tgz"
  "version" "5.3.4"
  dependencies:
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/querystring-builder" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "@smithy/util-base64" "^4.3.0"
    "tslib" "^2.6.2"

"@smithy/hash-node@^4.2.3":
  "integrity" "sha512-6+NOdZDbfuU6s1ISp3UOk5Rg953RJ2aBLNLLBEcamLjHAg1Po9Ha7QIB5ZWhdRUVuOUrT8BVFR+O2KIPmw027g=="
  "resolved" "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "@smithy/util-buffer-from" "^4.2.0"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/invalid-dependency@^4.2.3":
  "integrity" "sha512-Cc9W5DwDuebXEDMpOpl4iERo8I0KFjTnomK2RMdhhR87GwrSmUmwMxS4P5JdRf+LsjOdIqumcerwRgYMr/tZ9Q=="
  "resolved" "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/is-array-buffer@^2.2.0":
  "integrity" "sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA=="
  "resolved" "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/is-array-buffer@^4.2.0":
  "integrity" "sha512-DZZZBvC7sjcYh4MazJSGiWMI2L7E0oCiRHREDzIxi/M2LY79/21iXt6aPLHge82wi5LsuRF5A06Ds3+0mlh6CQ=="
  "resolved" "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/middleware-content-length@^4.2.3":
  "integrity" "sha512-/atXLsT88GwKtfp5Jr0Ks1CSa4+lB+IgRnkNrrYP0h1wL4swHNb0YONEvTceNKNdZGJsye+W2HH8W7olbcPUeA=="
  "resolved" "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/middleware-endpoint@^4.3.4":
  "integrity" "sha512-/RJhpYkMOaUZoJEkddamGPPIYeKICKXOu/ojhn85dKDM0n5iDIhjvYAQLP3K5FPhgB203O3GpWzoK2OehEoIUw=="
  "resolved" "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "@smithy/core" "^3.17.0"
    "@smithy/middleware-serde" "^4.2.3"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "@smithy/url-parser" "^4.2.3"
    "@smithy/util-middleware" "^4.2.3"
    "tslib" "^2.6.2"

"@smithy/middleware-retry@^4.4.4":
  "integrity" "sha512-vSgABQAkuUHRO03AhR2rWxVQ1un284lkBn+NFawzdahmzksAoOeVMnXXsuPViL4GlhRHXqFaMlc8Mj04OfQk1w=="
  "resolved" "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.4.4.tgz"
  "version" "4.4.4"
  dependencies:
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/service-error-classification" "^4.2.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-retry" "^4.2.3"
    "@smithy/uuid" "^1.1.0"
    "tslib" "^2.6.2"

"@smithy/middleware-serde@^4.2.3":
  "integrity" "sha512-8g4NuUINpYccxiCXM5s1/V+uLtts8NcX4+sPEbvYQDZk4XoJfDpq5y2FQxfmUL89syoldpzNzA0R9nhzdtdKnQ=="
  "resolved" "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/middleware-stack@^4.2.3":
  "integrity" "sha512-iGuOJkH71faPNgOj/gWuEGS6xvQashpLwWB1HjHq1lNNiVfbiJLpZVbhddPuDbx9l4Cgl0vPLq5ltRfSaHfspA=="
  "resolved" "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/node-config-provider@^4.3.3":
  "integrity" "sha512-NzI1eBpBSViOav8NVy1fqOlSfkLgkUjUTlohUSgAEhHaFWA3XJiLditvavIP7OpvTjDp5u2LhtlBhkBlEisMwA=="
  "resolved" "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@smithy/property-provider" "^4.2.3"
    "@smithy/shared-ini-file-loader" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/node-http-handler@^4.4.2":
  "integrity" "sha512-MHFvTjts24cjGo1byXqhXrbqm7uznFD/ESFx8npHMWTFQVdBZjrT1hKottmp69LBTRm/JQzP/sn1vPt0/r6AYQ=="
  "resolved" "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.4.2.tgz"
  "version" "4.4.2"
  dependencies:
    "@smithy/abort-controller" "^4.2.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/querystring-builder" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/property-provider@^4.2.3":
  "integrity" "sha512-+1EZ+Y+njiefCohjlhyOcy1UNYjT+1PwGFHCxA/gYctjg3DQWAU19WigOXAco/Ql8hZokNehpzLd0/+3uCreqQ=="
  "resolved" "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/protocol-http@^5.3.3":
  "integrity" "sha512-Mn7f/1aN2/jecywDcRDvWWWJF4uwg/A0XjFMJtj72DsgHTByfjRltSqcT9NyE9RTdBSN6X1RSXrhn/YWQl8xlw=="
  "resolved" "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.3.3.tgz"
  "version" "5.3.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/querystring-builder@^4.2.3":
  "integrity" "sha512-LOVCGCmwMahYUM/P0YnU/AlDQFjcu+gWbFJooC417QRB/lDJlWSn8qmPSDp+s4YVAHOgtgbNG4sR+SxF/VOcJQ=="
  "resolved" "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "@smithy/util-uri-escape" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/querystring-parser@^4.2.3":
  "integrity" "sha512-cYlSNHcTAX/wc1rpblli3aUlLMGgKZ/Oqn8hhjFASXMCXjIqeuQBei0cnq2JR8t4RtU9FpG6uyl6PxyArTiwKA=="
  "resolved" "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/service-error-classification@^4.2.3":
  "integrity" "sha512-NkxsAxFWwsPsQiwFG2MzJ/T7uIR6AQNh1SzcxSUnmmIqIQMlLRQDKhc17M7IYjiuBXhrQRjQTo3CxX+DobS93g=="
  "resolved" "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"

"@smithy/shared-ini-file-loader@^4.3.3":
  "integrity" "sha512-9f9Ixej0hFhroOK2TxZfUUDR13WVa8tQzhSzPDgXe5jGL3KmaM9s8XN7RQwqtEypI82q9KHnKS71CJ+q/1xLtQ=="
  "resolved" "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/signature-v4@^5.3.3":
  "integrity" "sha512-CmSlUy+eEYbIEYN5N3vvQTRfqt0lJlQkaQUIf+oizu7BbDut0pozfDjBGecfcfWf7c62Yis4JIEgqQ/TCfodaA=="
  "resolved" "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.3.3.tgz"
  "version" "5.3.3"
  dependencies:
    "@smithy/is-array-buffer" "^4.2.0"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "@smithy/util-hex-encoding" "^4.2.0"
    "@smithy/util-middleware" "^4.2.3"
    "@smithy/util-uri-escape" "^4.2.0"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/smithy-client@^4.9.0":
  "integrity" "sha512-qz7RTd15GGdwJ3ZCeBKLDQuUQ88m+skh2hJwcpPm1VqLeKzgZvXf6SrNbxvx7uOqvvkjCMXqx3YB5PDJyk00ww=="
  "resolved" "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.9.0.tgz"
  "version" "4.9.0"
  dependencies:
    "@smithy/core" "^3.17.0"
    "@smithy/middleware-endpoint" "^4.3.4"
    "@smithy/middleware-stack" "^4.2.3"
    "@smithy/protocol-http" "^5.3.3"
    "@smithy/types" "^4.8.0"
    "@smithy/util-stream" "^4.5.3"
    "tslib" "^2.6.2"

"@smithy/types@^4.8.0":
  "integrity" "sha512-QpELEHLO8SsQVtqP+MkEgCYTFW0pleGozfs3cZ183ZBj9z3VC1CX1/wtFMK64p+5bhtZo41SeLK1rBRtd25nHQ=="
  "resolved" "https://registry.npmjs.org/@smithy/types/-/types-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/url-parser@^4.2.3":
  "integrity" "sha512-I066AigYvY3d9VlU3zG9XzZg1yT10aNqvCaBTw9EPgu5GrsEl1aUkcMvhkIXascYH1A8W0LQo3B1Kr1cJNcQEw=="
  "resolved" "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/querystring-parser" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-base64@^4.3.0":
  "integrity" "sha512-GkXZ59JfyxsIwNTWFnjmFEI8kZpRNIBfxKjv09+nkAWPt/4aGaEWMM04m4sxgNVWkbt2MdSvE3KF/PfX4nFedQ=="
  "resolved" "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "@smithy/util-buffer-from" "^4.2.0"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/util-body-length-browser@^4.2.0":
  "integrity" "sha512-Fkoh/I76szMKJnBXWPdFkQJl2r9SjPt3cMzLdOB6eJ4Pnpas8hVoWPYemX/peO0yrrvldgCUVJqOAjUrOLjbxg=="
  "resolved" "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/util-body-length-node@^4.2.1":
  "integrity" "sha512-h53dz/pISVrVrfxV1iqXlx5pRg3V2YWFcSQyPyXZRrZoZj4R4DeWRDo1a7dd3CPTcFi3kE+98tuNyD2axyZReA=="
  "resolved" "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/util-buffer-from@^2.2.0":
  "integrity" "sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA=="
  "resolved" "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "@smithy/is-array-buffer" "^2.2.0"
    "tslib" "^2.6.2"

"@smithy/util-buffer-from@^4.2.0":
  "integrity" "sha512-kAY9hTKulTNevM2nlRtxAG2FQ3B2OR6QIrPY3zE5LqJy1oxzmgBGsHLWTcNhWXKchgA0WHW+mZkQrng/pgcCew=="
  "resolved" "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "@smithy/is-array-buffer" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/util-config-provider@^4.2.0":
  "integrity" "sha512-YEjpl6XJ36FTKmD+kRJJWYvrHeUvm5ykaUS5xK+6oXffQPHeEM4/nXlZPe+Wu0lsgRUcNZiliYNh/y7q9c2y6Q=="
  "resolved" "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/util-defaults-mode-browser@^4.3.3":
  "integrity" "sha512-vqHoybAuZXbFXZqgzquiUXtdY+UT/aU33sxa4GBPkiYklmR20LlCn+d3Wc3yA5ZM13gQ92SZe/D8xh6hkjx+IQ=="
  "resolved" "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@smithy/property-provider" "^4.2.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-defaults-mode-node@^4.2.5":
  "integrity" "sha512-YQ9GQEC3knSa8oGSNdl5U6TlLynoOlLMIszrehgJxNh80v+ZCBnlXLtjyz0ffOxuM7j9cgviJuvuNkAzUseq6w=="
  "resolved" "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.2.5.tgz"
  "version" "4.2.5"
  dependencies:
    "@smithy/config-resolver" "^4.4.0"
    "@smithy/credential-provider-imds" "^4.2.3"
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/property-provider" "^4.2.3"
    "@smithy/smithy-client" "^4.9.0"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-endpoints@^3.2.3":
  "integrity" "sha512-aCfxUOVv0CzBIkU10TubdgKSx5uRvzH064kaiPEWfNIvKOtNpu642P4FP1hgOFkjQIkDObrfIDnKMKkeyrejvQ=="
  "resolved" "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "@smithy/node-config-provider" "^4.3.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-hex-encoding@^4.2.0":
  "integrity" "sha512-CCQBwJIvXMLKxVbO88IukazJD9a4kQ9ZN7/UMGBjBcJYvatpWk+9g870El4cB8/EJxfe+k+y0GmR9CAzkF+Nbw=="
  "resolved" "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/util-middleware@^4.2.3":
  "integrity" "sha512-v5ObKlSe8PWUHCqEiX2fy1gNv6goiw6E5I/PN2aXg3Fb/hse0xeaAnSpXDiWl7x6LamVKq7senB+m5LOYHUAHw=="
  "resolved" "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-retry@^4.2.3":
  "integrity" "sha512-lLPWnakjC0q9z+OtiXk+9RPQiYPNAovt2IXD3CP4LkOnd9NpUsxOjMx1SnoUVB7Orb7fZp67cQMtTBKMFDvOGg=="
  "resolved" "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "@smithy/service-error-classification" "^4.2.3"
    "@smithy/types" "^4.8.0"
    "tslib" "^2.6.2"

"@smithy/util-stream@^4.5.3":
  "integrity" "sha512-oZvn8a5bwwQBNYHT2eNo0EU8Kkby3jeIg1P2Lu9EQtqDxki1LIjGRJM6dJ5CZUig8QmLxWxqOKWvg3mVoOBs5A=="
  "resolved" "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.5.3.tgz"
  "version" "4.5.3"
  dependencies:
    "@smithy/fetch-http-handler" "^5.3.4"
    "@smithy/node-http-handler" "^4.4.2"
    "@smithy/types" "^4.8.0"
    "@smithy/util-base64" "^4.3.0"
    "@smithy/util-buffer-from" "^4.2.0"
    "@smithy/util-hex-encoding" "^4.2.0"
    "@smithy/util-utf8" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/util-uri-escape@^4.2.0":
  "integrity" "sha512-igZpCKV9+E/Mzrpq6YacdTQ0qTiLm85gD6N/IrmyDvQFA4UnU3d5g3m8tMT/6zG/vVkWSU+VxeUyGonL62DuxA=="
  "resolved" "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "tslib" "^2.6.2"

"@smithy/util-utf8@^2.0.0":
  "integrity" "sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A=="
  "resolved" "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@smithy/util-buffer-from" "^2.2.0"
    "tslib" "^2.6.2"

"@smithy/util-utf8@^4.2.0":
  "integrity" "sha512-zBPfuzoI8xyBtR2P6WQj63Rz8i3AmfAaJLuNG8dWsfvPe8lO4aCPYLn879mEgHndZH1zQ2oXmG8O1GGzzaoZiw=="
  "resolved" "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "@smithy/util-buffer-from" "^4.2.0"
    "tslib" "^2.6.2"

"@smithy/uuid@^1.1.0":
  "integrity" "sha512-4aUIteuyxtBUhVdiQqcDhKFitwfd9hqoSDYY2KRXiWtgoWJ9Bmise+KfEPDiVHWeJepvF8xJO9/9+WDIciMFFw=="
  "resolved" "https://registry.npmjs.org/@smithy/uuid/-/uuid-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "tslib" "^2.6.2"

"@socket.io/component-emitter@~3.1.0":
  "integrity" "sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA=="
  "resolved" "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  "version" "3.1.2"

"@sqltools/formatter@^1.2.5":
  "integrity" "sha512-Uy0+khmZqUrUGm5dmMqVlnvufZRSK0FbYzVgp0UMstm+F5+W2/jnEEQyc9vo1ZR/E5ZI/B1WjjoTqBqwJL6Krw=="
  "resolved" "https://registry.npmjs.org/@sqltools/formatter/-/formatter-1.2.5.tgz"
  "version" "1.2.5"

"@tediousjs/connection-string@^0.5.0":
  "integrity" "sha512-7qSgZbincDDDFyRweCIEvZULFAw5iz/DeunhvuxpL31nfntX3P4Yd4HkHBRg9H8CdqY1e5WFN1PZIz/REL9MVQ=="
  "resolved" "https://registry.npmjs.org/@tediousjs/connection-string/-/connection-string-0.5.0.tgz"
  "version" "0.5.0"

"@tokenizer/inflate@^0.2.7":
  "integrity" "sha512-MADQgmZT1eKjp06jpI2yozxaU9uVs4GzzgSL+uEq7bVcJ9V1ZXQkeGNql1fsSI0gMy1vhvNTNbUqrx+pZfJVmg=="
  "resolved" "https://registry.npmjs.org/@tokenizer/inflate/-/inflate-0.2.7.tgz"
  "version" "0.2.7"
  dependencies:
    "debug" "^4.4.0"
    "fflate" "^0.8.2"
    "token-types" "^6.0.0"

"@tokenizer/token@^0.3.0":
  "integrity" "sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A=="
  "resolved" "https://registry.npmjs.org/@tokenizer/token/-/token-0.3.0.tgz"
  "version" "0.3.0"

"@tsconfig/node10@^1.0.7":
  "integrity" "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw=="
  "resolved" "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz"
  "version" "1.0.11"

"@tsconfig/node12@^1.0.7":
  "integrity" "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag=="
  "resolved" "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  "version" "1.0.11"

"@tsconfig/node14@^1.0.0":
  "integrity" "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow=="
  "resolved" "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  "version" "1.0.3"

"@tsconfig/node16@^1.0.2":
  "integrity" "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA=="
  "resolved" "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz"
  "version" "1.0.4"

"@tybys/wasm-util@^0.10.0":
  "integrity" "sha512-9tTaPJLSiejZKx+Bmog4uSubteqTvFrVrURwkmHixBo0G4seD0zUxp98E1DzUBJxLQ3NPwXrGKDiVjwx/DpPsg=="
  "resolved" "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.1.tgz"
  "version" "0.10.1"
  dependencies:
    "tslib" "^2.4.0"

"@types/babel__core@^7.20.5":
  "integrity" "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  "version" "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  "version" "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  "integrity" "sha512-8PvcXf70gTDZBgt9ptxJ8elBeBjcLOAcOtoO/mPJjtji1+CdGbHgm77om1GrsPxsiE+uXIpNSK64UYaIwQXd4Q=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@babel/types" "^7.28.2"

"@types/bcrypt@^5.0.2":
  "integrity" "sha512-6atioO8Y75fNcbmj0G7UjI9lXN2pQ/IGJ2FWT4a/btd0Lk9lQalHLKhkgKVZ3r+spnmWUKfbMi1GEe9wyHQfNQ=="
  "resolved" "https://registry.npmjs.org/@types/bcrypt/-/bcrypt-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  "integrity" "sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g=="
  "resolved" "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz"
  "version" "1.19.6"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cls-hooked@^4.3.3":
  "integrity" "sha512-CMtHMz6Q/dkfcHarq9nioXH8BDPP+v5xvd+N90lBQ2bdmu06UvnLDqxTKoOJzz4SzIwb/x9i4UXGAAcnUDuIvg=="
  "resolved" "https://registry.npmjs.org/@types/cls-hooked/-/cls-hooked-4.3.9.tgz"
  "version" "4.3.9"
  dependencies:
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug=="
  "resolved" "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  "version" "3.4.38"
  dependencies:
    "@types/node" "*"

"@types/cookiejar@^2.1.5":
  "integrity" "sha512-he+DHOWReW0nghN24E1WUqM0efK4kI9oTqDm6XmK8ZPe2djZ90BSNdGnIyCLzCPw7/pogPlGbzI2wHGGmi4O/Q=="
  "resolved" "https://registry.npmjs.org/@types/cookiejar/-/cookiejar-2.1.5.tgz"
  "version" "2.1.5"

"@types/cors@^2.8.12":
  "integrity" "sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg=="
  "resolved" "https://registry.npmjs.org/@types/cors/-/cors-2.8.19.tgz"
  "version" "2.8.19"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.7":
  "integrity" "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg=="
  "resolved" "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  "version" "3.7.7"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@>=8.0.0":
  "integrity" "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag=="
  "resolved" "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6", "@types/estree@^1.0.8":
  "integrity" "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz"
  "version" "1.0.8"

"@types/express-serve-static-core@^5.0.0":
  "integrity" "sha512-jnHMsrd0Mwa9Cf4IdOzbz543y4XJepXrbia2T4b6+spXC2We3t1y6K44D3mR8XMFSXMCf3/l7rCgddfx7UNVBA=="
  "resolved" "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^5.0.3":
  "integrity" "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw=="
  "resolved" "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/serve-static" "*"

"@types/http-errors@*":
  "integrity" "sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg=="
  "resolved" "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz"
  "version" "2.0.5"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.1", "@types/istanbul-lib-coverage@^2.0.6":
  "integrity" "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz"
  "version" "2.0.6"

"@types/istanbul-lib-report@*":
  "integrity" "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.4":
  "integrity" "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ=="
  "resolved" "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^30.0.0":
  "integrity" "sha512-XTYugzhuwqWjws0CVz8QpM36+T+Dz5mTEBKhNs/esGLnCIlGdRy+Dq78NRjd7ls7r8BC8ZRMOrKlkO1hU0JOwA=="
  "resolved" "https://registry.npmjs.org/@types/jest/-/jest-30.0.0.tgz"
  "version" "30.0.0"
  dependencies:
    "expect" "^30.0.0"
    "pretty-format" "^30.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.15", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/jsonwebtoken@*", "@types/jsonwebtoken@9.0.10":
  "integrity" "sha512-asx5hIG9Qmf/1oStypjanR7iKTv0gXQ1Ov/jfrX6kS/EO0OFni8orbmGCn0672NHR3kXHwpAwR+B368ZGN/2rA=="
  "resolved" "https://registry.npmjs.org/@types/jsonwebtoken/-/jsonwebtoken-9.0.10.tgz"
  "version" "9.0.10"
  dependencies:
    "@types/ms" "*"
    "@types/node" "*"

"@types/luxon@~3.7.0":
  "integrity" "sha512-H3iskjFIAn5SlJU7OuxUmTEpebK6TKB8rxZShDslBMZJ5u9S//KM1sbdAisiSrqwLQncVjnpi2OK2J51h+4lsg=="
  "resolved" "https://registry.npmjs.org/@types/luxon/-/luxon-3.7.1.tgz"
  "version" "3.7.1"

"@types/methods@^1.1.4":
  "integrity" "sha512-ymXWVrDiCxTBE3+RIrrP533E70eA+9qu7zdWoHuOmGujkYtzf4HQF96b8nwHLqhuf4ykX61IGRIB38CC6/sImQ=="
  "resolved" "https://registry.npmjs.org/@types/methods/-/methods-1.1.4.tgz"
  "version" "1.1.4"

"@types/mime@^1":
  "integrity" "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w=="
  "resolved" "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz"
  "version" "1.3.5"

"@types/ms@*":
  "integrity" "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA=="
  "resolved" "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz"
  "version" "2.1.0"

"@types/multer@^1.4.13":
  "integrity" "sha512-bhhdtPw7JqCiEfC9Jimx5LqX9BDIPJEh2q/fQ4bqbBPtyEZYr3cvF22NwG0DmPZNYA0CAf2CnqDB4KIGGpJcaw=="
  "resolved" "https://registry.npmjs.org/@types/multer/-/multer-1.4.13.tgz"
  "version" "1.4.13"
  dependencies:
    "@types/express" "*"

"@types/node@*", "@types/node@^24.0.4", "@types/node@>=10.0.0", "@types/node@>=18":
  "integrity" "sha512-/NbVmcGTP+lj5oa4yiYxxeBjRivKQ5Ns1eSZeB99ExsEQ6rX5XYU1Zy/gGxY/ilqtD4Etx9mKyrPxZRetiahhA=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-24.7.2.tgz"
  "version" "24.7.2"
  dependencies:
    "undici-types" "~7.14.0"

"@types/nodemailer@^7.0.2":
  "integrity" "sha512-Zo6uOA9157WRgBk/ZhMpTQ/iCWLMk7OIs/Q9jvHarMvrzUUP/MDdPHL2U1zpf57HrrWGv4nYQn5uIxna0xY3xw=="
  "resolved" "https://registry.npmjs.org/@types/nodemailer/-/nodemailer-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@aws-sdk/client-sesv2" "^3.839.0"
    "@types/node" "*"

"@types/oauth@*":
  "integrity" "sha512-H9TRCVKBNOhZZmyHLqFt9drPM9l+ShWiqqJijU1B8P3DX3ub84NjxDuy+Hjrz+fEca5Kwip3qPMKNyiLgNJtIA=="
  "resolved" "https://registry.npmjs.org/@types/oauth/-/oauth-0.9.6.tgz"
  "version" "0.9.6"
  dependencies:
    "@types/node" "*"

"@types/passport-jwt@^4.0.1":
  "integrity" "sha512-Y0Ykz6nWP4jpxgEUYq8NoVZeCQPo1ZndJLfapI249g1jHChvRfZRO/LS3tqu26YgAS/laI1qx98sYGz0IalRXQ=="
  "resolved" "https://registry.npmjs.org/@types/passport-jwt/-/passport-jwt-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@types/jsonwebtoken" "*"
    "@types/passport-strategy" "*"

"@types/passport-oauth2@^1.8.0":
  "integrity" "sha512-6//z+4orIOy/g3zx17HyQ71GSRK4bs7Sb+zFasRoc2xzlv7ZCJ+vkDBYFci8U6HY+or6Zy7ajf4mz4rK7nsWJQ=="
  "resolved" "https://registry.npmjs.org/@types/passport-oauth2/-/passport-oauth2-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "@types/express" "*"
    "@types/oauth" "*"
    "@types/passport" "*"

"@types/passport-strategy@*":
  "integrity" "sha512-GC6eMqqojOooq993Tmnmp7AUTbbQSgilyvpCYQjT+H6JfG/g6RGc7nXEniZlp0zyKJ0WUdOiZWLBZft9Yug1uA=="
  "resolved" "https://registry.npmjs.org/@types/passport-strategy/-/passport-strategy-0.2.38.tgz"
  "version" "0.2.38"
  dependencies:
    "@types/express" "*"
    "@types/passport" "*"

"@types/passport@*":
  "integrity" "sha512-aciLyx+wDwT2t2/kJGJR2AEeBz0nJU4WuRX04Wu9Dqc5lSUtwu0WERPHYsLhF9PtseiAMPBGNUOtFjxZ56prsg=="
  "resolved" "https://registry.npmjs.org/@types/passport/-/passport-1.0.17.tgz"
  "version" "1.0.17"
  dependencies:
    "@types/express" "*"

"@types/qrcode@^1.5.5":
  "integrity" "sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg=="
  "resolved" "https://registry.npmjs.org/@types/qrcode/-/qrcode-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "@types/node" "*"

"@types/qs@*":
  "integrity" "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ=="
  "resolved" "https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz"
  "version" "6.14.0"

"@types/range-parser@*":
  "integrity" "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ=="
  "resolved" "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz"
  "version" "1.2.7"

"@types/readable-stream@^4.0.0":
  "integrity" "sha512-19eKVv9tugr03IgfXlA9UVUVRbW6IuqRO5B92Dl4a6pT7K8uaGrNS0GkxiZD0BOk6PLuXl5FhWl//eX/pzYdTQ=="
  "resolved" "https://registry.npmjs.org/@types/readable-stream/-/readable-stream-4.0.21.tgz"
  "version" "4.0.21"
  dependencies:
    "@types/node" "*"

"@types/send@*":
  "integrity" "sha512-zBF6vZJn1IaMpg3xUF25VK3gd3l8zwE0ZLRX7dsQyQi+jp4E8mMDJNGDYnYse+bQhYwWERTxVwHpi3dMOq7RKQ=="
  "resolved" "https://registry.npmjs.org/@types/send/-/send-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "@types/node" "*"

"@types/send@<1":
  "integrity" "sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w=="
  "resolved" "https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz"
  "version" "0.17.5"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  "integrity" "sha512-dOTIuqpWLyl3BBXU3maNQsS4A3zuuoYRNIvYSxxhebPfXg2mzWQEPne/nlJ37yOse6uGgR386uTpdsx4D0QZWA=="
  "resolved" "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.9.tgz"
  "version" "1.15.9"
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "<1"

"@types/stack-utils@^2.0.3":
  "integrity" "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw=="
  "resolved" "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz"
  "version" "2.0.3"

"@types/superagent@^8.1.0":
  "integrity" "sha512-pTVjI73witn+9ILmoJdajHGW2jkSaOzhiFYF1Rd3EQ94kymLqB9PjD9ISg7WaALC7+dCHT0FGe9T2LktLq/3GQ=="
  "resolved" "https://registry.npmjs.org/@types/superagent/-/superagent-8.1.9.tgz"
  "version" "8.1.9"
  dependencies:
    "@types/cookiejar" "^2.1.5"
    "@types/methods" "^1.1.4"
    "@types/node" "*"
    "form-data" "^4.0.0"

"@types/supertest@^6.0.3":
  "integrity" "sha512-8WzXq62EXFhJ7QsH3Ocb/iKQ/Ty9ZVWnVzoTKc9tyyFRRF3a74Tk2+TLFgaFFw364Ere+npzHKEJ6ga2LzIL7w=="
  "resolved" "https://registry.npmjs.org/@types/supertest/-/supertest-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "@types/methods" "^1.1.4"
    "@types/superagent" "^8.1.0"

"@types/uuid@^10.0.0":
  "integrity" "sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ=="
  "resolved" "https://registry.npmjs.org/@types/uuid/-/uuid-10.0.0.tgz"
  "version" "10.0.0"

"@types/validator@^13.11.8":
  "integrity" "sha512-7bcUmDyS6PN3EuD9SlGGOxM77F8WLVsrwkxyWxKnxzmXoequ6c7741QBrANq6htVRGOITJ7z72mTP6Z4XyuG+Q=="
  "resolved" "https://registry.npmjs.org/@types/validator/-/validator-13.15.3.tgz"
  "version" "13.15.3"

"@types/yargs-parser@*":
  "integrity" "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ=="
  "resolved" "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz"
  "version" "21.0.3"

"@types/yargs@^17.0.33":
  "integrity" "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA=="
  "resolved" "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz"
  "version" "17.0.33"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^8.35.0":
  "integrity" "sha512-rUsLh8PXmBjdiPY+Emjz9NX2yHvhS11v0SR6xNJkm5GM1MO9ea/1GoDKlHHZGrOJclL/cZ2i/vRUYVtjRhrHVQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.46.1"
    "@typescript-eslint/type-utils" "8.46.1"
    "@typescript-eslint/utils" "8.46.1"
    "@typescript-eslint/visitor-keys" "8.46.1"
    "graphemer" "^1.4.0"
    "ignore" "^7.0.0"
    "natural-compare" "^1.4.0"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/parser@^8.35.0", "@typescript-eslint/parser@^8.46.1":
  "integrity" "sha512-6JSSaBZmsKvEkbRUkf7Zj7dru/8ZCrJxAqArcLaVMee5907JdtEbKGsZ7zNiIm/UAkpGUkaSMZEXShnN2D1HZA=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/scope-manager" "8.46.1"
    "@typescript-eslint/types" "8.46.1"
    "@typescript-eslint/typescript-estree" "8.46.1"
    "@typescript-eslint/visitor-keys" "8.46.1"
    "debug" "^4.3.4"

"@typescript-eslint/project-service@8.46.1":
  "integrity" "sha512-FOIaFVMHzRskXr5J4Jp8lFVV0gz5ngv3RHmn+E4HYxSJ3DgDzU7fVI1/M7Ijh1zf6S7HIoaIOtln1H5y8V+9Zg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.46.1"
    "@typescript-eslint/types" "^8.46.1"
    "debug" "^4.3.4"

"@typescript-eslint/scope-manager@8.46.1":
  "integrity" "sha512-weL9Gg3/5F0pVQKiF8eOXFZp8emqWzZsOJuWRUNtHT+UNV2xSJegmpCNQHy37aEQIbToTq7RHKhWvOsmbM680A=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/types" "8.46.1"
    "@typescript-eslint/visitor-keys" "8.46.1"

"@typescript-eslint/tsconfig-utils@^8.46.1", "@typescript-eslint/tsconfig-utils@8.46.1":
  "integrity" "sha512-X88+J/CwFvlJB+mK09VFqx5FE4H5cXD+H/Bdza2aEWkSb8hnWIQorNcscRl4IEo1Cz9VI/+/r/jnGWkbWPx54g=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.46.1.tgz"
  "version" "8.46.1"

"@typescript-eslint/type-utils@8.46.1":
  "integrity" "sha512-+BlmiHIiqufBxkVnOtFwjah/vrkF4MtKKvpXrKSPLCkCtAp8H01/VV43sfqA98Od7nJpDcFnkwgyfQbOG0AMvw=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/types" "8.46.1"
    "@typescript-eslint/typescript-estree" "8.46.1"
    "@typescript-eslint/utils" "8.46.1"
    "debug" "^4.3.4"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/types@^8.46.1", "@typescript-eslint/types@8.46.1":
  "integrity" "sha512-C+soprGBHwWBdkDpbaRC4paGBrkIXxVlNohadL5o0kfhsXqOC6GYH2S/Obmig+I0HTDl8wMaRySwrfrXVP8/pQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/types/-/types-8.46.1.tgz"
  "version" "8.46.1"

"@typescript-eslint/typescript-estree@8.46.1":
  "integrity" "sha512-uIifjT4s8cQKFQ8ZBXXyoUODtRoAd7F7+G8MKmtzj17+1UbdzFl52AzRyZRyKqPHhgzvXunnSckVu36flGy8cg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/project-service" "8.46.1"
    "@typescript-eslint/tsconfig-utils" "8.46.1"
    "@typescript-eslint/types" "8.46.1"
    "@typescript-eslint/visitor-keys" "8.46.1"
    "debug" "^4.3.4"
    "fast-glob" "^3.3.2"
    "is-glob" "^4.0.3"
    "minimatch" "^9.0.4"
    "semver" "^7.6.0"
    "ts-api-utils" "^2.1.0"

"@typescript-eslint/utils@8.46.1":
  "integrity" "sha512-vkYUy6LdZS7q1v/Gxb2Zs7zziuXN0wxqsetJdeZdRe/f5dwJFglmuvZBfTUivCtjH725C1jWCDfpadadD95EDQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.46.1"
    "@typescript-eslint/types" "8.46.1"
    "@typescript-eslint/typescript-estree" "8.46.1"

"@typescript-eslint/visitor-keys@8.46.1":
  "integrity" "sha512-ptkmIf2iDkNUjdeu2bQqhFPV1m6qTnFFjg7PPDjxKWaMaP0Z6I9l30Jr3g5QqbZGdw8YdYvLp+XnqnWWZOg/NA=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.46.1.tgz"
  "version" "8.46.1"
  dependencies:
    "@typescript-eslint/types" "8.46.1"
    "eslint-visitor-keys" "^4.2.1"

"@typespec/ts-http-runtime@^0.3.0":
  "integrity" "sha512-SnbaqayTVFEA6/tYumdF0UmybY0KHyKwGPBXnyckFlrrKdhWFrL3a2HIPXHjht5ZOElKGcXfD2D63P36btb+ww=="
  "resolved" "https://registry.npmjs.org/@typespec/ts-http-runtime/-/ts-http-runtime-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "http-proxy-agent" "^7.0.0"
    "https-proxy-agent" "^7.0.0"
    "tslib" "^2.6.2"

"@ungap/structured-clone@^1.3.0":
  "integrity" "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g=="
  "resolved" "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  "version" "1.3.0"

"@unrs/resolver-binding-android-arm-eabi@1.11.1":
  "integrity" "sha512-ppLRUgHVaGRWUx0R0Ut06Mjo9gBaBkg3v/8AxusGLhsIotbBLuRk51rAzqLC8gq6NyyAojEXglNjzf6R948DNw=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-android-arm-eabi/-/resolver-binding-android-arm-eabi-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-android-arm64@1.11.1":
  "integrity" "sha512-lCxkVtb4wp1v+EoN+HjIG9cIIzPkX5OtM03pQYkG+U5O/wL53LC4QbIeazgiKqluGeVEeBlZahHalCaBvU1a2g=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-android-arm64/-/resolver-binding-android-arm64-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-darwin-arm64@1.11.1":
  "integrity" "sha512-gPVA1UjRu1Y/IsB/dQEsp2V1pm44Of6+LWvbLc9SDk1c2KhhDRDBUkQCYVWe6f26uJb3fOK8saWMgtX8IrMk3g=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-darwin-x64@1.11.1":
  "integrity" "sha512-cFzP7rWKd3lZaCsDze07QX1SC24lO8mPty9vdP+YVa3MGdVgPmFc59317b2ioXtgCMKGiCLxJ4HQs62oz6GfRQ=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-darwin-x64/-/resolver-binding-darwin-x64-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-freebsd-x64@1.11.1":
  "integrity" "sha512-fqtGgak3zX4DCB6PFpsH5+Kmt/8CIi4Bry4rb1ho6Av2QHTREM+47y282Uqiu3ZRF5IQioJQ5qWRV6jduA+iGw=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-freebsd-x64/-/resolver-binding-freebsd-x64-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1":
  "integrity" "sha512-u92mvlcYtp9MRKmP+ZvMmtPN34+/3lMHlyMj7wXJDeXxuM0Vgzz0+PPJNsro1m3IZPYChIkn944wW8TYgGKFHw=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-arm-gnueabihf/-/resolver-binding-linux-arm-gnueabihf-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-arm-musleabihf@1.11.1":
  "integrity" "sha512-cINaoY2z7LVCrfHkIcmvj7osTOtm6VVT16b5oQdS4beibX2SYBwgYLmqhBjA1t51CarSaBuX5YNsWLjsqfW5Cw=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-arm-musleabihf/-/resolver-binding-linux-arm-musleabihf-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-arm64-gnu@1.11.1":
  "integrity" "sha512-34gw7PjDGB9JgePJEmhEqBhWvCiiWCuXsL9hYphDF7crW7UgI05gyBAi6MF58uGcMOiOqSJ2ybEeCvHcq0BCmQ=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-arm64-gnu/-/resolver-binding-linux-arm64-gnu-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-arm64-musl@1.11.1":
  "integrity" "sha512-RyMIx6Uf53hhOtJDIamSbTskA99sPHS96wxVE/bJtePJJtpdKGXO1wY90oRdXuYOGOTuqjT8ACccMc4K6QmT3w=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-arm64-musl/-/resolver-binding-linux-arm64-musl-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-ppc64-gnu@1.11.1":
  "integrity" "sha512-D8Vae74A4/a+mZH0FbOkFJL9DSK2R6TFPC9M+jCWYia/q2einCubX10pecpDiTmkJVUH+y8K3BZClycD8nCShA=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-ppc64-gnu/-/resolver-binding-linux-ppc64-gnu-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-riscv64-gnu@1.11.1":
  "integrity" "sha512-frxL4OrzOWVVsOc96+V3aqTIQl1O2TjgExV4EKgRY09AJ9leZpEg8Ak9phadbuX0BA4k8U5qtvMSQQGGmaJqcQ=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-riscv64-gnu/-/resolver-binding-linux-riscv64-gnu-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-riscv64-musl@1.11.1":
  "integrity" "sha512-mJ5vuDaIZ+l/acv01sHoXfpnyrNKOk/3aDoEdLO/Xtn9HuZlDD6jKxHlkN8ZhWyLJsRBxfv9GYM2utQ1SChKew=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-riscv64-musl/-/resolver-binding-linux-riscv64-musl-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-s390x-gnu@1.11.1":
  "integrity" "sha512-kELo8ebBVtb9sA7rMe1Cph4QHreByhaZ2QEADd9NzIQsYNQpt9UkM9iqr2lhGr5afh885d/cB5QeTXSbZHTYPg=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-s390x-gnu/-/resolver-binding-linux-s390x-gnu-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-x64-gnu@1.11.1":
  "integrity" "sha512-C3ZAHugKgovV5YvAMsxhq0gtXuwESUKc5MhEtjBpLoHPLYM+iuwSj3lflFwK3DPm68660rZ7G8BMcwSro7hD5w=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-x64-gnu/-/resolver-binding-linux-x64-gnu-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-linux-x64-musl@1.11.1":
  "integrity" "sha512-rV0YSoyhK2nZ4vEswT/QwqzqQXw5I6CjoaYMOX0TqBlWhojUf8P94mvI7nuJTeaCkkds3QE4+zS8Ko+GdXuZtA=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-linux-x64-musl/-/resolver-binding-linux-x64-musl-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-wasm32-wasi@1.11.1":
  "integrity" "sha512-5u4RkfxJm+Ng7IWgkzi3qrFOvLvQYnPBmjmZQ8+szTK/b31fQCnleNl1GgEt7nIsZRIf5PLhPwT0WM+q45x/UQ=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-wasm32-wasi/-/resolver-binding-wasm32-wasi-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@napi-rs/wasm-runtime" "^0.2.11"

"@unrs/resolver-binding-win32-arm64-msvc@1.11.1":
  "integrity" "sha512-nRcz5Il4ln0kMhfL8S3hLkxI85BXs3o8EYoattsJNdsX4YUU89iOkVn7g0VHSRxFuVMdM4Q1jEpIId1Ihim/Uw=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-win32-arm64-msvc/-/resolver-binding-win32-arm64-msvc-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-win32-ia32-msvc@1.11.1":
  "integrity" "sha512-DCEI6t5i1NmAZp6pFonpD5m7i6aFrpofcp4LA2i8IIq60Jyo28hamKBxNrZcyOwVOZkgsRp9O2sXWBWP8MnvIQ=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-win32-ia32-msvc/-/resolver-binding-win32-ia32-msvc-1.11.1.tgz"
  "version" "1.11.1"

"@unrs/resolver-binding-win32-x64-msvc@1.11.1":
  "integrity" "sha512-lrW200hZdbfRtztbygyaq/6jP6AKE8qQN2KvPcJ+x7wiD038YtnYtZ82IMNJ69GJibV7bwL3y9FgK+5w/pYt6g=="
  "resolved" "https://registry.npmjs.org/@unrs/resolver-binding-win32-x64-msvc/-/resolver-binding-win32-x64-msvc-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  "integrity" "sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  "integrity" "sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-api-error@1.13.2":
  "integrity" "sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-buffer@1.14.1":
  "integrity" "sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  "version" "1.14.1"

"@webassemblyjs/helper-numbers@1.13.2":
  "integrity" "sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  "integrity" "sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-wasm-section@1.14.1":
  "integrity" "sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  "integrity" "sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  "integrity" "sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  "integrity" "sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/wasm-edit@^1.14.1":
  "integrity" "sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  "integrity" "sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  "integrity" "sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  "integrity" "sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  "integrity" "sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xmldom/xmldom@^0.8.6":
  "integrity" "sha512-cQzWCtO6C8TQiYl1ruKNn2U6Ao4o4WBBcbL61yJl84x+j5sOWWFU9X7DpND8XZG3daDppSsigMdfAIl2upQBRw=="
  "resolved" "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.11.tgz"
  "version" "0.8.11"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"abort-controller@^3.0.0":
  "integrity" "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg=="
  "resolved" "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "event-target-shim" "^5.0.0"

"accept-language-parser@^1.5.0":
  "integrity" "sha512-QhyTbMLYo0BBGg1aWbeMG4ekWtds/31BrEU+DONOg/7ax23vxpL03Pb7/zBmha2v7vdD3AyzZVWBVGEZxKOXWw=="
  "resolved" "https://registry.npmjs.org/accept-language-parser/-/accept-language-parser-1.5.0.tgz"
  "version" "1.5.0"

"accepts@^2.0.0":
  "integrity" "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "mime-types" "^3.0.0"
    "negotiator" "^1.0.0"

"accepts@~1.3.4":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-import-phases@^1.0.3":
  "integrity" "sha512-wKmbr/DDiIXzEOiWrTTUcDm24kQ2vGfZQvM2fwg2vXqR5uW6aapr7ObPtj1th32b9u90/Pf4AItvdTh42fBmVQ=="
  "resolved" "https://registry.npmjs.org/acorn-import-phases/-/acorn-import-phases-1.0.4.tgz"
  "version" "1.0.4"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^8.1.1":
  "integrity" "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz"
  "version" "8.3.4"
  dependencies:
    "acorn" "^8.11.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.11.0", "acorn@^8.14.0", "acorn@^8.15.0", "acorn@^8.4.1":
  "integrity" "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz"
  "version" "8.15.0"

"adler-32@~1.3.0":
  "integrity" "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="
  "resolved" "https://registry.npmjs.org/adler-32/-/adler-32-1.3.1.tgz"
  "version" "1.3.1"

"agent-base@^7.1.0", "agent-base@^7.1.2":
  "integrity" "sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz"
  "version" "7.1.4"

"ajv-formats@^2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-formats@3.0.1":
  "integrity" "sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.1.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.9.1":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0", "ajv@^8.8.2", "ajv@^8.9.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ajv@8.17.1":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ansi-colors@4.1.3":
  "integrity" "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="
  "resolved" "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-escapes@^4.3.2":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "integrity" "sha512-Bq3SmSpyFHaWjPk8If9yc6svM8c56dB5BAtW4Qbw5jHTwwXXcTLoRMkpDJp6VL0XzlWaCHTXrkFURMYmD0sLqg=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.2.2.tgz"
  "version" "6.2.2"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^5.2.0":
  "integrity" "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  "version" "5.2.0"

"ansi-styles@^6.1.0":
  "integrity" "sha512-4Dj6M28JB+oAH8kFkTLUo+a2jwOFkuqb3yucU0CANcRRUbxS0cP0nZYCGjcc3BNXwRIsUVmDGgzawme7zvJHvg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.3.tgz"
  "version" "6.2.3"

"ansis@^3.17.0":
  "integrity" "sha512-0qWUglt9JEqLFr3w1I1pbrChn1grhaiAR2ocX1PP/flRmxgtwTzPFFFnfIlD6aMOLQZgSuCRlidD70lvx8yhzg=="
  "resolved" "https://registry.npmjs.org/ansis/-/ansis-3.17.0.tgz"
  "version" "3.17.0"

"ansis@4.1.0":
  "integrity" "sha512-BGcItUBWSMRgOCe+SVZJ+S7yTRG0eGt9cXAHev72yuGcY23hnLA7Bky5L/xLyPINoSN95geovfBkqoTlNZYa7w=="
  "resolved" "https://registry.npmjs.org/ansis/-/ansis-4.1.0.tgz"
  "version" "4.1.0"

"anymatch@^3.1.3", "anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"ape-nestjs-typeorm3-kit@^0.1.7":
  "integrity" "sha512-EOMzm0aD5849ugdn3CNKMy+pgZz1HUuLQxIG1i6i4sNy9UYD0V3vzZk3fR4YeCavLn/yv5HvgYyl46+xKFGtVA=="
  "resolved" "https://registry.npmjs.org/ape-nestjs-typeorm3-kit/-/ape-nestjs-typeorm3-kit-0.1.7.tgz"
  "version" "0.1.7"

"app-root-path@^3.1.0":
  "integrity" "sha512-biN3PwB2gUtjaYy/isrU3aNWI5w+fAfvHkSvCKeQGxhmYpwKFUxudR3Yya+KqVRHBmEDYh+/lTozYCFbmzX4nA=="
  "resolved" "https://registry.npmjs.org/app-root-path/-/app-root-path-3.1.0.tgz"
  "version" "3.1.0"

"append-field@^1.0.0":
  "integrity" "sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw=="
  "resolved" "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz"
  "version" "1.0.0"

"arg@^4.1.0":
  "integrity" "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA=="
  "resolved" "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  "version" "4.1.3"

"argparse@^1.0.7", "argparse@~1.0.3":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"array-timsort@^1.0.3":
  "integrity" "sha512-/+3GRL7dDAGEfM6TseQk/U+mi18TU2Ms9I3UlLdUMhz2hbvGNTKdj9xniwXfUqgYhHxRx0+8UnKkvlNwVU+cWQ=="
  "resolved" "https://registry.npmjs.org/array-timsort/-/array-timsort-1.0.3.tgz"
  "version" "1.0.3"

"asap@^2.0.0":
  "integrity" "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA=="
  "resolved" "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  "version" "2.0.6"

"async-hook-jl@^1.7.6":
  "integrity" "sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg=="
  "resolved" "https://registry.npmjs.org/async-hook-jl/-/async-hook-jl-1.7.6.tgz"
  "version" "1.7.6"
  dependencies:
    "stack-chain" "^1.3.7"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"available-typed-arrays@^1.0.7":
  "integrity" "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ=="
  "resolved" "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "possible-typed-array-names" "^1.0.0"

"aws-sdk@^2.1692.0":
  "integrity" "sha512-x511uiJ/57FIsbgUe5csJ13k3uzu25uWQE+XqfBis/sB0SFoiElJWXRkgEAUh0U6n40eT3ay5Ue4oPkRMu1LYw=="
  "resolved" "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.1692.0.tgz"
  "version" "2.1692.0"
  dependencies:
    "buffer" "4.9.2"
    "events" "1.1.1"
    "ieee754" "1.1.13"
    "jmespath" "0.16.0"
    "querystring" "0.2.0"
    "sax" "1.2.1"
    "url" "0.10.3"
    "util" "^0.12.4"
    "uuid" "8.0.0"
    "xml2js" "0.6.2"

"axios@^1.10.0":
  "integrity" "sha512-vMJzPewAlRyOgxV2dU0Cuz2O8zzzx9VYtbJOaBgXFeLc4IV/Eg50n4LowmehOOR61S8ZMpc2K5Sa7g6A4jfkUw=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.12.2.tgz"
  "version" "1.12.2"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.4"
    "proxy-from-env" "^1.1.0"

"babel-jest@^29.0.0 || ^30.0.0", "babel-jest@30.2.0":
  "integrity" "sha512-0YiBEOxWqKkSQWL9nNGGEgndoeL0ZpWrbLMNL5u/Kaxrli3Eaxlt3ZtIDktEvXt4L/R9r3ODr2zKwGM/2BjxVw=="
  "resolved" "https://registry.npmjs.org/babel-jest/-/babel-jest-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/transform" "30.2.0"
    "@types/babel__core" "^7.20.5"
    "babel-plugin-istanbul" "^7.0.1"
    "babel-preset-jest" "30.2.0"
    "chalk" "^4.1.2"
    "graceful-fs" "^4.2.11"
    "slash" "^3.0.0"

"babel-plugin-istanbul@^7.0.1":
  "integrity" "sha512-D8Z6Qm8jCvVXtIRkBnqNHX0zJ37rQcFJ9u8WOS6tkYOsRdHBzypCstaxWiu5ZIlqQtviRYbgnRLSoCEvjqcqbA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.3"
    "istanbul-lib-instrument" "^6.0.2"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@30.2.0":
  "integrity" "sha512-ftzhzSGMUnOzcCXd6WHdBGMyuwy15Wnn0iyyWGKgBDLxf9/s5ABuraCSpBX2uG0jUg4rqJnxsLc5+oYBqoxVaA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@types/babel__core" "^7.20.5"

"babel-preset-current-node-syntax@^1.2.0":
  "integrity" "sha512-E/VlAEzRrsLEb2+dv8yp3bo4scof3l9nR4lrld+Iy5NyVqgVYUJnDAmunkhPMisRI32Qc4iRiz425d8vM++2fg=="
  "resolved" "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

"babel-preset-jest@30.2.0":
  "integrity" "sha512-US4Z3NOieAQumwFnYdUWKvUKh8+YSnS/gB3t6YBiz0bskpu7Pine8pPCheNxlPEW4wnUkma2a94YuW2q3guvCQ=="
  "resolved" "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "babel-plugin-jest-hoist" "30.2.0"
    "babel-preset-current-node-syntax" "^1.2.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.0.2", "base64-js@^1.3.1", "base64-js@^1.5.1":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"base64id@~2.0.0", "base64id@2.0.0":
  "integrity" "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog=="
  "resolved" "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz"
  "version" "2.0.0"

"base64url@3.x.x":
  "integrity" "sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A=="
  "resolved" "https://registry.npmjs.org/base64url/-/base64url-3.0.1.tgz"
  "version" "3.0.1"

"baseline-browser-mapping@^2.8.9":
  "integrity" "sha512-OMu3BGQ4E7P1ErFsIPpbJh0qvDudM/UuJeHgkAvfWe+0HFJCXh+t/l8L6fVLR55RI/UbKrVLnAXZSVwd9ysWYw=="
  "resolved" "https://registry.npmjs.org/baseline-browser-mapping/-/baseline-browser-mapping-2.8.16.tgz"
  "version" "2.8.16"

"bcrypt@^6.0.0":
  "integrity" "sha512-cU8v/EGSrnH+HnxV2z0J7/blxH8gq7Xh2JFT6Aroax7UohdmiJJlxApMxtKfuI7z68NvvVcmR78k2LbT6efhRg=="
  "resolved" "https://registry.npmjs.org/bcrypt/-/bcrypt-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "node-addon-api" "^8.3.0"
    "node-gyp-build" "^4.8.4"

"binary-extensions@^2.0.0":
  "integrity" "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  "version" "2.3.0"

"bl@^4.1.0":
  "integrity" "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  "resolved" "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"bl@^6.0.11":
  "integrity" "sha512-nHB8B5roHlGX5TFsWeiQJijdddZIOHuv1eL2cM2kHnG3qR91CYLsysGe+CvxQfEd23EKD0eJf4lto0frTbddKA=="
  "resolved" "https://registry.npmjs.org/bl/-/bl-6.1.3.tgz"
  "version" "6.1.3"
  dependencies:
    "@types/readable-stream" "^4.0.0"
    "buffer" "^6.0.3"
    "inherits" "^2.0.4"
    "readable-stream" "^4.2.0"

"bluebird@~3.4.0":
  "integrity" "sha512-iD3898SR7sWVRHbiQv+sHUtHnMvC1o3nW5rAcqnq3uOn07DSAppZYUkIGslDz6gXC7HfunPe7YVBgoEJASPcHA=="
  "resolved" "https://registry.npmjs.org/bluebird/-/bluebird-3.4.7.tgz"
  "version" "3.4.7"

"body-parser@^2.2.0":
  "integrity" "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg=="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "bytes" "^3.1.2"
    "content-type" "^1.0.5"
    "debug" "^4.4.0"
    "http-errors" "^2.0.0"
    "iconv-lite" "^0.6.3"
    "on-finished" "^2.4.1"
    "qs" "^6.14.0"
    "raw-body" "^3.0.0"
    "type-is" "^2.0.0"

"bowser@^2.11.0":
  "integrity" "sha512-z4rE2Gxh7tvshQ4hluIT7XcFrgLIQaw9X3A+kTTRdovCz5PMukm/0QC/BKSYPj3omF5Qfypn9O/c5kgpmvYUCw=="
  "resolved" "https://registry.npmjs.org/bowser/-/bowser-2.12.1.tgz"
  "version" "2.12.1"

"brace-expansion@^1.1.7":
  "integrity" "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz"
  "version" "1.1.12"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^3.0.3", "braces@~3.0.2":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"browserslist@^4.24.0", "browserslist@>= 4.21.0":
  "integrity" "sha512-lAUU+02RFBuCKQPj/P6NgjlbCnLBMp4UtgTx7vNHd3XSIJF87s9a5rA3aH2yw3GS9DqZAUbOtZdCCiZeVRqt0w=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.26.3.tgz"
  "version" "4.26.3"
  dependencies:
    "baseline-browser-mapping" "^2.8.9"
    "caniuse-lite" "^1.0.30001746"
    "electron-to-chromium" "^1.5.227"
    "node-releases" "^2.0.21"
    "update-browserslist-db" "^1.1.3"

"bs-logger@^0.2.6":
  "integrity" "sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog=="
  "resolved" "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "fast-json-stable-stringify" "2.x"

"bser@2.1.1":
  "integrity" "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ=="
  "resolved" "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-equal-constant-time@^1.0.1":
  "integrity" "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="
  "resolved" "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  "version" "1.0.1"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer@^5.5.0":
  "integrity" "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffer@^6.0.3":
  "integrity" "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.2.1"

"buffer@4.9.2":
  "integrity" "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg=="
  "resolved" "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"bundle-name@^4.1.0":
  "integrity" "sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q=="
  "resolved" "https://registry.npmjs.org/bundle-name/-/bundle-name-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "run-applescript" "^7.0.0"

"busboy@^1.6.0":
  "integrity" "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA=="
  "resolved" "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "streamsearch" "^1.1.0"

"bytes@^3.1.2", "bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"cache-manager@^7.0.0", "cache-manager@>=6":
  "integrity" "sha512-skmhkqXjPCBmrb70ctEx4zwFk7vb0RdFXlVGYWnFZ8pKvkzdFrFFKSJ1IaKduGfkryHOJvb7q2PkGmonmL+UGw=="
  "resolved" "https://registry.npmjs.org/cache-manager/-/cache-manager-7.2.4.tgz"
  "version" "7.2.4"
  dependencies:
    "@cacheable/utils" "^2.1.0"
    "keyv" "^5.5.3"

"call-bind-apply-helpers@^1.0.0", "call-bind-apply-helpers@^1.0.1", "call-bind-apply-helpers@^1.0.2":
  "integrity" "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ=="
  "resolved" "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"

"call-bind@^1.0.8":
  "integrity" "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind-apply-helpers" "^1.0.0"
    "es-define-property" "^1.0.0"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.2"

"call-bound@^1.0.2", "call-bound@^1.0.3", "call-bound@^1.0.4":
  "integrity" "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg=="
  "resolved" "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "get-intrinsic" "^1.3.0"

"callsites@^3.0.0", "callsites@^3.1.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase@^5.0.0":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.3.0":
  "integrity" "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-lite@^1.0.30001746":
  "integrity" "sha512-cuom0g5sdX6rw00qOoLNSFCJ9/mYIsuSOA+yzpDw8eopiFqcVwQvZHqov0vmEighRxX++cfC0Vg1G+1Iy/mSpQ=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001750.tgz"
  "version" "1.0.30001750"

"cfb@~1.2.1":
  "integrity" "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA=="
  "resolved" "https://registry.npmjs.org/cfb/-/cfb-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "adler-32" "~1.3.0"
    "crc-32" "~1.2.0"

"chalk@^4.0.0", "chalk@^4.1.0", "chalk@^4.1.2":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"char-regex@^1.0.2":
  "integrity" "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw=="
  "resolved" "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  "version" "1.0.2"

"chardet@^2.1.0":
  "integrity" "sha512-bNFETTG/pM5ryzQ9Ad0lJOTa6HWD/YsScAR3EnCPZRPlQh77JocYktSHOUHelyhm8IARL+o4c4F1bP5KVOjiRA=="
  "resolved" "https://registry.npmjs.org/chardet/-/chardet-2.1.0.tgz"
  "version" "2.1.0"

"chokidar@^3.6.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chokidar@^4.0.0", "chokidar@^4.0.1", "chokidar@4.0.3":
  "integrity" "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "readdirp" "^4.0.1"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  "version" "1.0.4"

"ci-info@^4.2.0":
  "integrity" "sha512-Wdy2Igu8OcBpI2pZePZ5oWjPC38tmDVx5WKUXKwlLYkA0ozo85sLsLvkBbBn/sZaSCMFOGZJ14fvW9t5/d7kdA=="
  "resolved" "https://registry.npmjs.org/ci-info/-/ci-info-4.3.1.tgz"
  "version" "4.3.1"

"cjs-module-lexer@^2.1.0":
  "integrity" "sha512-UX0OwmYRYQQetfrLEZeewIFFI+wSTofC+pMBLNuH3RUuu/xzG1oz84UCEDOSoQlN3fZ4+AzmV50ZYvGqkMh9yA=="
  "resolved" "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz"
  "version" "2.1.0"

"class-transformer@*", "class-transformer@^0.4.0 || ^0.5.0", "class-transformer@^0.5.1", "class-transformer@>=0.4.1":
  "integrity" "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw=="
  "resolved" "https://registry.npmjs.org/class-transformer/-/class-transformer-0.5.1.tgz"
  "version" "0.5.1"

"class-validator-jsonschema@^5.0.2":
  "integrity" "sha512-FFOeqLR+Ng+iGoapZksAYwNFMSxTqQaFt32UHFrIDwa8bk72mWMWH5U/LEpvhnQh5ZD1sWZFbh3oTNBcFtt+4A=="
  "resolved" "https://registry.npmjs.org/class-validator-jsonschema/-/class-validator-jsonschema-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lodash.groupby" "^4.6.0"
    "lodash.merge" "^4.6.2"
    "openapi3-ts" "^3.0.0"
    "reflect-metadata" "^0.2.2"
    "tslib" "^2.8.1"

"class-validator@*", "class-validator@^0.13.0 || ^0.14.0", "class-validator@^0.14.0", "class-validator@^0.14.2", "class-validator@>=0.13.2":
  "integrity" "sha512-3kMVRF2io8N8pY1IFIXlho9r8IPUUIfHe2hYVtiebvAzU2XeQFXTv+XI4WX+TnXmtwXMDcjngcpkiPM0O9PvLw=="
  "resolved" "https://registry.npmjs.org/class-validator/-/class-validator-0.14.2.tgz"
  "version" "0.14.2"
  dependencies:
    "@types/validator" "^13.11.8"
    "libphonenumber-js" "^1.11.1"
    "validator" "^13.9.0"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-spinners@^2.5.0":
  "integrity" "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg=="
  "resolved" "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  "version" "2.9.2"

"cli-table3@0.6.5":
  "integrity" "sha512-+W/5efTR7y5HRD7gACw9yQjqMVvEMLBHmboM/kPWam+H+Hmyrgjh6YncVKK122YZkXrLudzTuAukUw9FnMf7IQ=="
  "resolved" "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.5.tgz"
  "version" "0.6.5"
  dependencies:
    "string-width" "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

"cli-width@^4.1.0":
  "integrity" "sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ=="
  "resolved" "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz"
  "version" "4.1.0"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^8.0.1":
  "integrity" "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.1"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.2":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"cls-hooked@^4.2.2":
  "integrity" "sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw=="
  "resolved" "https://registry.npmjs.org/cls-hooked/-/cls-hooked-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "async-hook-jl" "^1.7.6"
    "emitter-listener" "^1.0.1"
    "semver" "^5.4.1"

"co@^4.6.0":
  "integrity" "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ=="
  "resolved" "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  "version" "4.6.0"

"codepage@~1.15.0":
  "integrity" "sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA=="
  "resolved" "https://registry.npmjs.org/codepage/-/codepage-1.15.0.tgz"
  "version" "1.15.0"

"collect-v8-coverage@^1.0.2":
  "integrity" "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q=="
  "resolved" "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  "version" "1.0.2"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^11.0.0":
  "integrity" "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz"
  "version" "11.1.0"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@4.1.1":
  "integrity" "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  "version" "4.1.1"

"comment-json@4.4.1":
  "integrity" "sha512-r1To31BQD5060QdkC+Iheai7gHwoSZobzunqkf2/kQ6xIAfJyrKNAFUwdKvkK7Qgu7pVTKQEa7ok7Ed3ycAJgg=="
  "resolved" "https://registry.npmjs.org/comment-json/-/comment-json-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "array-timsort" "^1.0.3"
    "core-util-is" "^1.0.3"
    "esprima" "^4.0.1"

"component-emitter@^1.3.1":
  "integrity" "sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ=="
  "resolved" "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz"
  "version" "1.3.1"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^2.0.0":
  "integrity" "sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A=="
  "resolved" "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.0.2"
    "typedarray" "^0.0.6"

"consola@^3.2.3":
  "integrity" "sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA=="
  "resolved" "https://registry.npmjs.org/consola/-/consola-3.4.2.tgz"
  "version" "3.4.2"

"content-disposition@^1.0.0":
  "integrity" "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg=="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@^1.0.5":
  "integrity" "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  "version" "1.0.5"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"cookie-signature@^1.2.1":
  "integrity" "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg=="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz"
  "version" "1.2.2"

"cookie@^0.7.0", "cookie@^0.7.1", "cookie@~0.7.2":
  "integrity" "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz"
  "version" "0.7.2"

"cookiejar@^2.1.4":
  "integrity" "sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw=="
  "resolved" "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.4.tgz"
  "version" "2.1.4"

"core-util-is@^1.0.3", "core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cors@~2.8.5", "cors@2.8.5":
  "integrity" "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="
  "resolved" "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  "version" "2.8.5"
  dependencies:
    "object-assign" "^4"
    "vary" "^1"

"cosmiconfig@^8.2.0":
  "integrity" "sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  "version" "8.3.6"
  dependencies:
    "import-fresh" "^3.3.0"
    "js-yaml" "^4.1.0"
    "parse-json" "^5.2.0"
    "path-type" "^4.0.0"

"crc-32@~1.2.0", "crc-32@~1.2.1":
  "integrity" "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="
  "resolved" "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"create-require@^1.1.0":
  "integrity" "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ=="
  "resolved" "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  "version" "1.1.1"

"cron@4.3.3":
  "integrity" "sha512-B/CJj5yL3sjtlun6RtYHvoSB26EmQ2NUmhq9ZiJSyKIM4K/fqfh9aelDFlIayD2YMeFZqWLi9hHV+c+pq2Djkw=="
  "resolved" "https://registry.npmjs.org/cron/-/cron-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@types/luxon" "~3.7.0"
    "luxon" "~3.7.0"

"cross-spawn@^7.0.3", "cross-spawn@^7.0.6":
  "integrity" "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"dayjs@^1.11.13":
  "integrity" "sha512-zFBQ7WFRvVRhKcWoUh+ZA1g2HVgUbsZm9sbddh8EC5iv93sui8DVVz1Npvz+r6meo9VKfa8NyLWBsQK1VvIKPA=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.11.18.tgz"
  "version" "1.11.18"

"debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.1", "debug@^4.3.2", "debug@^4.3.3", "debug@^4.3.4", "debug@^4.3.5", "debug@^4.3.7", "debug@^4.4.0", "debug@4":
  "integrity" "sha512-RGwwWnwQvkVfavKVt22FGLw+xYSdzARwm0ru6DhTVA3umU5hZc28V3kO4stgYryrTlLpuvgI9GiijltAjNbcqA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.4.3.tgz"
  "version" "4.4.3"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.1":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.2":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.4":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"decamelize@^1.2.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"dedent@^1.6.0":
  "integrity" "sha512-HGFtf8yhuhGhqO07SV79tRp+br4MnbdjeVxotpn1QBl30pcLLCQjX5b2295ll0fv8RKDKsmWYrl05usHM9CewQ=="
  "resolved" "https://registry.npmjs.org/dedent/-/dedent-1.7.0.tgz"
  "version" "1.7.0"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^4.2.2", "deepmerge@^4.3.1":
  "integrity" "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"default-browser-id@^5.0.0":
  "integrity" "sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA=="
  "resolved" "https://registry.npmjs.org/default-browser-id/-/default-browser-id-5.0.0.tgz"
  "version" "5.0.0"

"default-browser@^5.2.1":
  "integrity" "sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg=="
  "resolved" "https://registry.npmjs.org/default-browser/-/default-browser-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "bundle-name" "^4.1.0"
    "default-browser-id" "^5.0.0"

"defaults@^1.0.3":
  "integrity" "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="
  "resolved" "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"define-data-property@^1.1.4":
  "integrity" "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A=="
  "resolved" "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-lazy-prop@^3.0.0":
  "integrity" "sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg=="
  "resolved" "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz"
  "version" "3.0.0"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@^2.0.0", "depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"detect-newline@^3.1.0":
  "integrity" "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA=="
  "resolved" "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"dezalgo@^1.0.4":
  "integrity" "sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig=="
  "resolved" "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "asap" "^2.0.0"
    "wrappy" "1"

"diff@^4.0.1":
  "integrity" "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="
  "resolved" "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  "version" "4.0.2"

"dijkstrajs@^1.0.1":
  "integrity" "sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA=="
  "resolved" "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz"
  "version" "1.0.3"

"dingbat-to-unicode@^1.0.1":
  "integrity" "sha512-98l0sW87ZT58pU4i61wa2OHwxbiYSbuxsCBozaVnYX2iCnr3bLM3fIes1/ej7h1YdOKuKt/MLs706TVnALA65w=="
  "resolved" "https://registry.npmjs.org/dingbat-to-unicode/-/dingbat-to-unicode-1.0.1.tgz"
  "version" "1.0.1"

"dotenv-expand@12.0.1":
  "integrity" "sha512-LaKRbou8gt0RNID/9RoI+J2rvXsBRPMV7p+ElHlPhcSARbCPDYcYG2s1TIzAfWv4YSgyY5taidWzzs31lNV3yQ=="
  "resolved" "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-12.0.1.tgz"
  "version" "12.0.1"
  dependencies:
    "dotenv" "^16.4.5"

"dotenv@^16.4.5":
  "integrity" "sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz"
  "version" "16.6.1"

"dotenv@^16.4.7":
  "integrity" "sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz"
  "version" "16.6.1"

"dotenv@16.4.7":
  "integrity" "sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz"
  "version" "16.4.7"

"duck@^0.1.12":
  "integrity" "sha512-wkctla1O6VfP89gQ+J/yDesM0S7B7XLXjKGzXxMDVFg7uEn706niAtyYovKbyq1oT9YwDcly721/iUWoc8MVRg=="
  "resolved" "https://registry.npmjs.org/duck/-/duck-0.1.12.tgz"
  "version" "0.1.12"
  dependencies:
    "underscore" "^1.13.1"

"dunder-proto@^1.0.1":
  "integrity" "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A=="
  "resolved" "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind-apply-helpers" "^1.0.1"
    "es-errors" "^1.3.0"
    "gopd" "^1.2.0"

"eastasianwidth@^0.2.0":
  "integrity" "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA=="
  "resolved" "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  "version" "0.2.0"

"ecdsa-sig-formatter@1.0.11":
  "integrity" "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ=="
  "resolved" "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "safe-buffer" "^5.0.1"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.5.227":
  "integrity" "sha512-i/7ntLFwOdoHY7sgjlTIDo4Sl8EdoTjWIaKinYOVfC6bOp71bmwenyZthWHcasxgHDNWbWxvG9M3Ia116zIaYQ=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.235.tgz"
  "version" "1.5.235"

"emitter-listener@^1.0.1":
  "integrity" "sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ=="
  "resolved" "https://registry.npmjs.org/emitter-listener/-/emitter-listener-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "shimmer" "^1.2.0"

"emittery@^0.13.1":
  "integrity" "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ=="
  "resolved" "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz"
  "version" "0.13.1"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.2.2":
  "integrity" "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  "version" "9.2.2"

"encodeurl@^2.0.0":
  "integrity" "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz"
  "version" "2.0.0"

"engine.io-parser@~5.2.1":
  "integrity" "sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q=="
  "resolved" "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.3.tgz"
  "version" "5.2.3"

"engine.io@~6.6.0":
  "integrity" "sha512-ZCkIjSYNDyGn0R6ewHDtXgns/Zre/NT6Agvq1/WobF7JXgFff4SeDroKiCO3fNJreU9YG429Sc81o4w5ok/W5g=="
  "resolved" "https://registry.npmjs.org/engine.io/-/engine.io-6.6.4.tgz"
  "version" "6.6.4"
  dependencies:
    "@types/cors" "^2.8.12"
    "@types/node" ">=10.0.0"
    "accepts" "~1.3.4"
    "base64id" "2.0.0"
    "cookie" "~0.7.2"
    "cors" "~2.8.5"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.17.1"

"enhanced-resolve@^5.0.0", "enhanced-resolve@^5.17.2", "enhanced-resolve@^5.7.0":
  "integrity" "sha512-d4lC8xfavMeBjzGr2vECC3fsGXziXZQyJxD868h2M/mBI3PwAuODxAkLkq5HYuvrPYcUtiLzsTo8U3PgX3Ocww=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.3.tgz"
  "version" "5.18.3"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"error-ex@^1.3.1":
  "integrity" "sha512-sqQamAnR14VgCr1A618A3sGrygcpK+HEbenA/HiEAkkUwcZIIB/tgWqHFxWgOyDh4nB4JCRimh79dR5Ywc9MDQ=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "is-arrayish" "^0.2.1"

"es-define-property@^1.0.0", "es-define-property@^1.0.1":
  "integrity" "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="
  "resolved" "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  "version" "1.0.1"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-module-lexer@^1.2.1":
  "integrity" "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz"
  "version" "1.7.0"

"es-object-atoms@^1.0.0", "es-object-atoms@^1.1.1":
  "integrity" "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA=="
  "resolved" "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.1.0":
  "integrity" "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA=="
  "resolved" "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"escalade@^3.1.1", "escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-html@^1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^2.0.0":
  "integrity" "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-prettier@^10.1.5", "eslint-config-prettier@>= 7.0.0 <10.0.0 || >=10.1.0":
  "integrity" "sha512-82GZUjRS0p/jganf6q1rEO25VSoHH0hKPCTrgillPjdI/3bgBhAE1QzHrHTizjpRvy6pGAvKjDJtk2pF9NDq8w=="
  "resolved" "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-10.1.8.tgz"
  "version" "10.1.8"

"eslint-plugin-prettier@^5.5.1":
  "integrity" "sha512-swNtI95SToIz05YINMA6Ox5R057IMAmWZ26GqPxusAp1TZzj+IdY9tXNWWD3vkF/wEqydCONcwjTFpxybBqZsg=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.4.tgz"
  "version" "5.5.4"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"
    "synckit" "^0.11.7"

"eslint-scope@^8.4.0":
  "integrity" "sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz"
  "version" "8.4.0"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint-visitor-keys@^4.2.1":
  "integrity" "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz"
  "version" "4.2.1"

"eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^8.57.0 || ^9.0.0", "eslint@^9.29.0", "eslint@>=7.0.0", "eslint@>=8.0.0":
  "integrity" "sha512-XyLmROnACWqSxiGYArdef1fItQd47weqB7iwtfr9JHwRrqIXZdcFMvvEcL9xHCmL0SNsOvF0c42lWyM1U5dgig=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-9.37.0.tgz"
  "version" "9.37.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.8.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.4.0"
    "@eslint/core" "^0.16.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.37.0"
    "@eslint/plugin-kit" "^0.4.0"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.6"
    "debug" "^4.3.2"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^8.4.0"
    "eslint-visitor-keys" "^4.2.1"
    "espree" "^10.4.0"
    "esquery" "^1.5.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^8.0.0"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"

"espree@^10.0.1", "espree@^10.4.0":
  "integrity" "sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-10.4.0.tgz"
  "version" "10.4.0"
  dependencies:
    "acorn" "^8.15.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^4.2.1"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.5.0":
  "integrity" "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@^1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-target-shim@^5.0.0":
  "integrity" "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ=="
  "resolved" "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  "version" "5.0.1"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"events@^3.3.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"events@1.1.1":
  "integrity" "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw=="
  "resolved" "https://registry.npmjs.org/events/-/events-1.1.1.tgz"
  "version" "1.1.1"

"execa@^5.1.1":
  "integrity" "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg=="
  "resolved" "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit-x@^0.2.2":
  "integrity" "sha512-+I6B/IkJc1o/2tiURyz/ivu/O0nKNEArIUB5O7zBrlDVJr22SCLH3xTeEry428LvFhRzIA1g8izguxJ/gbNcVQ=="
  "resolved" "https://registry.npmjs.org/exit-x/-/exit-x-0.2.2.tgz"
  "version" "0.2.2"

"expect@^30.0.0", "expect@30.2.0":
  "integrity" "sha512-u/feCi0GPsI+988gU2FLcsHyAHTU0MX1Wg68NhAnN7z/+C5wqG+CY8J53N9ioe8RXgaoz0nBR/TYMf3AycUuPw=="
  "resolved" "https://registry.npmjs.org/expect/-/expect-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/expect-utils" "30.2.0"
    "@jest/get-type" "30.1.0"
    "jest-matcher-utils" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-mock" "30.2.0"
    "jest-util" "30.2.0"

"express@5.1.0":
  "integrity" "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA=="
  "resolved" "https://registry.npmjs.org/express/-/express-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "accepts" "^2.0.0"
    "body-parser" "^2.2.0"
    "content-disposition" "^1.0.0"
    "content-type" "^1.0.5"
    "cookie" "^0.7.1"
    "cookie-signature" "^1.2.1"
    "debug" "^4.4.0"
    "encodeurl" "^2.0.0"
    "escape-html" "^1.0.3"
    "etag" "^1.8.1"
    "finalhandler" "^2.1.0"
    "fresh" "^2.0.0"
    "http-errors" "^2.0.0"
    "merge-descriptors" "^2.0.0"
    "mime-types" "^3.0.0"
    "on-finished" "^2.4.1"
    "once" "^1.4.0"
    "parseurl" "^1.3.3"
    "proxy-addr" "^2.0.7"
    "qs" "^6.14.0"
    "range-parser" "^1.2.1"
    "router" "^2.2.0"
    "send" "^1.1.0"
    "serve-static" "^2.2.0"
    "statuses" "^2.0.1"
    "type-is" "^2.0.1"
    "vary" "^1.1.2"

"extensionsjs@^1.2.0":
  "integrity" "sha512-qOvRQA+wQ3ykStj5tbEfiDTsM+zISWsnStL9MqsxVMWjBIA3Z8W/kWWkiDJyBnF6JkW1rT4hlpFwMaK1B8MtIw=="
  "resolved" "https://registry.npmjs.org/extensionsjs/-/extensionsjs-1.2.0.tgz"
  "version" "1.2.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz"
  "version" "1.3.0"

"fast-glob@^3.3.2", "fast-glob@^3.3.3":
  "integrity" "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.8"

"fast-json-stable-stringify@^2.0.0", "fast-json-stable-stringify@^2.1.0", "fast-json-stable-stringify@2.x":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fast-safe-stringify@^2.1.1", "fast-safe-stringify@2.1.1":
  "integrity" "sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA=="
  "resolved" "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  "version" "2.1.1"

"fast-uri@^3.0.1":
  "integrity" "sha512-iPeeDKJSWf4IEOasVVrknXpaBV0IApz/gp7S2bb7Z4Lljbl2MGJRqInZiUrQwV16cpzw/D3S5j5Julj/gT52AA=="
  "resolved" "https://registry.npmjs.org/fast-uri/-/fast-uri-3.1.0.tgz"
  "version" "3.1.0"

"fast-xml-parser@5.2.5":
  "integrity" "sha512-pfX9uG9Ki0yekDHx2SiuRIyFdyAr1kMIMitPvb0YBo8SUfKvia7w7FIyd/l6av85pFYRhZscS75MwMnbvY+hcQ=="
  "resolved" "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-5.2.5.tgz"
  "version" "5.2.5"
  dependencies:
    "strnum" "^2.1.0"

"fastq@^1.6.0":
  "integrity" "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz"
  "version" "1.19.1"
  dependencies:
    "reusify" "^1.0.4"

"fb-watchman@^2.0.2":
  "integrity" "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA=="
  "resolved" "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "bser" "2.1.1"

"fflate@^0.8.2":
  "integrity" "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="
  "resolved" "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  "version" "0.8.2"

"file-entry-cache@^8.0.0":
  "integrity" "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "flat-cache" "^4.0.0"

"file-type@21.0.0":
  "integrity" "sha512-ek5xNX2YBYlXhiUXui3D/BXa3LdqPmoLJ7rqEx2bKJ7EAUEfmXgW0Das7Dc6Nr9MvqaOnIqiPV0mZk/r/UpNAg=="
  "resolved" "https://registry.npmjs.org/file-type/-/file-type-21.0.0.tgz"
  "version" "21.0.0"
  dependencies:
    "@tokenizer/inflate" "^0.2.7"
    "strtok3" "^10.2.2"
    "token-types" "^6.0.0"
    "uint8array-extras" "^1.4.0"

"fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@^2.1.0":
  "integrity" "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "debug" "^4.4.0"
    "encodeurl" "^2.0.0"
    "escape-html" "^1.0.3"
    "on-finished" "^2.4.1"
    "parseurl" "^1.3.3"
    "statuses" "^2.0.1"

"find-up@^4.0.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^4.0.0":
  "integrity" "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.4"

"flatted@^3.2.9":
  "integrity" "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz"
  "version" "3.3.3"

"follow-redirects@^1.15.6":
  "integrity" "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.11.tgz"
  "version" "1.15.11"

"for-each@^0.3.5":
  "integrity" "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg=="
  "resolved" "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "is-callable" "^1.2.7"

"foreground-child@^3.1.0", "foreground-child@^3.3.1":
  "integrity" "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw=="
  "resolved" "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "cross-spawn" "^7.0.6"
    "signal-exit" "^4.0.1"

"fork-ts-checker-webpack-plugin@9.1.0":
  "integrity" "sha512-mpafl89VFPJmhnJ1ssH+8wmM2b50n+Rew5x42NeI2U78aRWgtkEtGmctp7iT16UjquJTjorEmIfESj3DxdW84Q=="
  "resolved" "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "chalk" "^4.1.2"
    "chokidar" "^4.0.1"
    "cosmiconfig" "^8.2.0"
    "deepmerge" "^4.2.2"
    "fs-extra" "^10.0.0"
    "memfs" "^3.4.1"
    "minimatch" "^3.0.4"
    "node-abort-controller" "^3.0.1"
    "schema-utils" "^3.1.1"
    "semver" "^7.3.5"
    "tapable" "^2.2.1"

"form-data@^4.0.0", "form-data@^4.0.4":
  "integrity" "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "es-set-tostringtag" "^2.1.0"
    "hasown" "^2.0.2"
    "mime-types" "^2.1.12"

"formidable@^3.5.4":
  "integrity" "sha512-YikH+7CUTOtP44ZTnUhR7Ic2UASBPOqmaRkRKxRbywPTe5VxF7RRCck4af9wutiZ/QKM5nME9Bie2fFaPz5Gug=="
  "resolved" "https://registry.npmjs.org/formidable/-/formidable-3.5.4.tgz"
  "version" "3.5.4"
  dependencies:
    "@paralleldrive/cuid2" "^2.2.2"
    "dezalgo" "^1.0.4"
    "once" "^1.4.0"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"frac@~1.1.2":
  "integrity" "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="
  "resolved" "https://registry.npmjs.org/frac/-/frac-1.1.2.tgz"
  "version" "1.1.2"

"fresh@^2.0.0":
  "integrity" "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A=="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz"
  "version" "2.0.0"

"fs-extra@^10.0.0":
  "integrity" "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ=="
  "resolved" "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-monkey@^1.0.4":
  "integrity" "sha512-QMUezzXWII9EV5aTFXW1UBVUO77wYPpjqIF8/AviUCThNeSYZykpoTixUeaNNBwmCev0AMDWMAni+f8Hxb1IFw=="
  "resolved" "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.1.0.tgz"
  "version" "1.1.0"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^2.3.3", "fsevents@~2.3.2":
  "integrity" "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw=="
  "resolved" "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz"
  "version" "2.3.3"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"generator-function@^2.0.0":
  "integrity" "sha512-SFdFmIJi+ybC0vjlHN0ZGVGHc3lgE0DxPAT0djjVg+kjOnSqclqmj0KQ7ykTOLP6YxoqOvuAODGdcHJn+43q3g=="
  "resolved" "https://registry.npmjs.org/generator-function/-/generator-function-2.0.1.tgz"
  "version" "2.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.2.4", "get-intrinsic@^1.2.5", "get-intrinsic@^1.2.6", "get-intrinsic@^1.3.0":
  "integrity" "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "math-intrinsics" "^1.1.0"

"get-package-type@^0.1.0":
  "integrity" "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q=="
  "resolved" "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-proto@^1.0.1":
  "integrity" "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g=="
  "resolved" "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-object-atoms" "^1.0.0"

"get-stream@^6.0.0":
  "integrity" "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg=="
  "resolved" "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^10.3.10":
  "integrity" "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  "version" "10.4.5"
  dependencies:
    "foreground-child" "^3.1.0"
    "jackspeak" "^3.1.2"
    "minimatch" "^9.0.4"
    "minipass" "^7.1.2"
    "package-json-from-dist" "^1.0.0"
    "path-scurry" "^1.11.1"

"glob@^10.4.5":
  "integrity" "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  "version" "10.4.5"
  dependencies:
    "foreground-child" "^3.1.0"
    "jackspeak" "^3.1.2"
    "minimatch" "^9.0.4"
    "minipass" "^7.1.2"
    "package-json-from-dist" "^1.0.0"
    "path-scurry" "^1.11.1"

"glob@^7.1.4":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@11.0.3":
  "integrity" "sha512-2Nim7dha1KVkaiF4q6Dj+ngPPMdfvLJEOpZk/jKiUAkqKebpGAWQXAq9z1xu9HKu5lWfqw/FASuccEjyznjPaA=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-11.0.3.tgz"
  "version" "11.0.3"
  dependencies:
    "foreground-child" "^3.3.1"
    "jackspeak" "^4.1.1"
    "minimatch" "^10.0.3"
    "minipass" "^7.1.2"
    "package-json-from-dist" "^1.0.0"
    "path-scurry" "^2.0.0"

"globals@^14.0.0":
  "integrity" "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz"
  "version" "14.0.0"

"gopd@^1.0.1", "gopd@^1.2.0":
  "integrity" "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  "version" "1.2.0"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.11", "graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"graphemer@^1.4.0":
  "integrity" "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="
  "resolved" "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  "version" "1.4.0"

"handlebars@^4.7.8":
  "integrity" "sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ=="
  "resolved" "https://registry.npmjs.org/handlebars/-/handlebars-4.7.8.tgz"
  "version" "4.7.8"
  dependencies:
    "minimist" "^1.2.5"
    "neo-async" "^2.6.2"
    "source-map" "^0.6.1"
    "wordwrap" "^1.0.0"
  optionalDependencies:
    "uglify-js" "^3.1.4"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.2":
  "integrity" "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg=="
  "resolved" "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-symbols@^1.0.3", "has-symbols@^1.1.0":
  "integrity" "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  "version" "1.1.0"

"has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"html-escaper@^2.0.0":
  "integrity" "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="
  "resolved" "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"http-errors@^2.0.0", "http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-proxy-agent@^7.0.0":
  "integrity" "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "agent-base" "^7.1.0"
    "debug" "^4.3.4"

"https-proxy-agent@^7.0.0":
  "integrity" "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "agent-base" "^7.1.2"
    "debug" "4"

"human-signals@^2.1.0":
  "integrity" "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw=="
  "resolved" "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"iconv-lite@^0.6.3":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@^0.7.0":
  "integrity" "sha512-cf6L2Ds3h57VVmkZe+Pn+5APsT7FpqJtEhhieDCvrE2MK5Qk9MyffgQyuxQTm6BChfeZNtcOLHp9IcWRVcIcBQ=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"iconv-lite@0.7.0":
  "integrity" "sha512-cf6L2Ds3h57VVmkZe+Pn+5APsT7FpqJtEhhieDCvrE2MK5Qk9MyffgQyuxQTm6BChfeZNtcOLHp9IcWRVcIcBQ=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"ieee754@^1.1.13":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ieee754@^1.1.4":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ieee754@^1.2.1":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ieee754@1.1.13":
  "integrity" "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg=="
  "resolved" "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  "version" "1.1.13"

"ignore@^5.2.0":
  "integrity" "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  "version" "5.3.2"

"ignore@^7.0.0":
  "integrity" "sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-7.0.5.tgz"
  "version" "7.0.5"

"immediate@~3.0.5":
  "integrity" "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="
  "resolved" "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"import-fresh@^3.2.1", "import-fresh@^3.3.0":
  "integrity" "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-local@^3.2.0":
  "integrity" "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA=="
  "resolved" "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-arguments@^1.0.4":
  "integrity" "sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "call-bound" "^1.0.2"
    "has-tostringtag" "^1.0.2"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-callable@^1.2.7":
  "integrity" "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  "version" "1.2.7"

"is-docker@^3.0.0":
  "integrity" "sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ=="
  "resolved" "https://registry.npmjs.org/is-docker/-/is-docker-3.0.0.tgz"
  "version" "3.0.0"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-generator-fn@^2.1.0":
  "integrity" "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ=="
  "resolved" "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-generator-function@^1.0.7":
  "integrity" "sha512-upqt1SkGkODW9tsGNG5mtXTXtECizwtS2kA161M+gJPc1xdb/Ax629af6YrTwcOeQHbewrPNlE5Dx7kzvXTizA=="
  "resolved" "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bound" "^1.0.4"
    "generator-function" "^2.0.0"
    "get-proto" "^1.0.1"
    "has-tostringtag" "^1.0.2"
    "safe-regex-test" "^1.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-inside-container@^1.0.0":
  "integrity" "sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA=="
  "resolved" "https://registry.npmjs.org/is-inside-container/-/is-inside-container-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-docker" "^3.0.0"

"is-interactive@^1.0.0":
  "integrity" "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w=="
  "resolved" "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-promise@^4.0.0":
  "integrity" "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ=="
  "resolved" "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz"
  "version" "4.0.0"

"is-regex@^1.2.1":
  "integrity" "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "call-bound" "^1.0.2"
    "gopd" "^1.2.0"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-typed-array@^1.1.14", "is-typed-array@^1.1.3":
  "integrity" "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ=="
  "resolved" "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz"
  "version" "1.1.15"
  dependencies:
    "which-typed-array" "^1.1.16"

"is-unicode-supported@^0.1.0":
  "integrity" "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="
  "resolved" "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-wsl@^3.1.0":
  "integrity" "sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw=="
  "resolved" "https://registry.npmjs.org/is-wsl/-/is-wsl-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-inside-container" "^1.0.0"

"isarray@^1.0.0", "isarray@~1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@^2.0.5":
  "integrity" "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  "version" "2.0.5"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"istanbul-lib-coverage@^3.0.0", "istanbul-lib-coverage@^3.2.0":
  "integrity" "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  "version" "3.2.2"

"istanbul-lib-instrument@^6.0.0", "istanbul-lib-instrument@^6.0.2":
  "integrity" "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    "istanbul-lib-coverage" "^3.2.0"
    "semver" "^7.5.4"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^4.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^5.0.0":
  "integrity" "sha512-yg2d+Em4KizZC5niWhQaIomgf5WlL4vOOjZ5xGCmF8SnPE/mDWWXgvRExdcpCgh9lLRRa1/fSYp2ymmbJ1pI+A=="
  "resolved" "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz"
  "version" "5.0.6"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"

"istanbul-reports@^3.1.3":
  "integrity" "sha512-HGYWWS/ehqTV3xN10i23tkPkpH46MLCIMFNCaaKNavAXTF1RkqxawEPtnjnGZ6XKSInBKkiOA5BKS+aZiY3AvA=="
  "resolved" "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"iterare@^1.2.1", "iterare@1.2.1":
  "integrity" "sha512-RKYVTCjAnRthyJes037NX/IiqeidgN1xc3j1RjFfECFp28A1GVwK9nA+i0rJPaHqSZwygLzRnFlzUuHFoWWy+Q=="
  "resolved" "https://registry.npmjs.org/iterare/-/iterare-1.2.1.tgz"
  "version" "1.2.1"

"jackspeak@^3.1.2":
  "integrity" "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw=="
  "resolved" "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  "version" "3.4.3"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

"jackspeak@^4.1.1":
  "integrity" "sha512-zptv57P3GpL+O0I7VdMJNBZCu+BPHVQUk55Ft8/QCJjTVxrnJHuVuX/0Bl2A6/+2oyR/ZMEuFKwmzqqZ/U5nPQ=="
  "resolved" "https://registry.npmjs.org/jackspeak/-/jackspeak-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@isaacs/cliui" "^8.0.2"

"jest-changed-files@30.2.0":
  "integrity" "sha512-L8lR1ChrRnSdfeOvTrwZMlnWV8G/LLjQ0nG9MBclwWZidA2N5FviRki0Bvh20WRMOX31/JYvzdqTJrk5oBdydQ=="
  "resolved" "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "execa" "^5.1.1"
    "jest-util" "30.2.0"
    "p-limit" "^3.1.0"

"jest-circus@30.2.0":
  "integrity" "sha512-Fh0096NC3ZkFx05EP2OXCxJAREVxj1BcW/i6EWqqymcgYKWjyyDpral3fMxVcHXg6oZM7iULer9wGRFvfpl+Tg=="
  "resolved" "https://registry.npmjs.org/jest-circus/-/jest-circus-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/environment" "30.2.0"
    "@jest/expect" "30.2.0"
    "@jest/test-result" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "co" "^4.6.0"
    "dedent" "^1.6.0"
    "is-generator-fn" "^2.1.0"
    "jest-each" "30.2.0"
    "jest-matcher-utils" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-runtime" "30.2.0"
    "jest-snapshot" "30.2.0"
    "jest-util" "30.2.0"
    "p-limit" "^3.1.0"
    "pretty-format" "30.2.0"
    "pure-rand" "^7.0.0"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.6"

"jest-cli@30.2.0":
  "integrity" "sha512-Os9ukIvADX/A9sLt6Zse3+nmHtHaE6hqOsjQtNiugFTbKRHYIYtZXNGNK9NChseXy7djFPjndX1tL0sCTlfpAA=="
  "resolved" "https://registry.npmjs.org/jest-cli/-/jest-cli-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/core" "30.2.0"
    "@jest/test-result" "30.2.0"
    "@jest/types" "30.2.0"
    "chalk" "^4.1.2"
    "exit-x" "^0.2.2"
    "import-local" "^3.2.0"
    "jest-config" "30.2.0"
    "jest-util" "30.2.0"
    "jest-validate" "30.2.0"
    "yargs" "^17.7.2"

"jest-config@30.2.0":
  "integrity" "sha512-g4WkyzFQVWHtu6uqGmQR4CQxz/CH3yDSlhzXMWzNjDx843gYjReZnMRanjRCq5XZFuQrGDxgUaiYWE8BRfVckA=="
  "resolved" "https://registry.npmjs.org/jest-config/-/jest-config-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/get-type" "30.1.0"
    "@jest/pattern" "30.0.1"
    "@jest/test-sequencer" "30.2.0"
    "@jest/types" "30.2.0"
    "babel-jest" "30.2.0"
    "chalk" "^4.1.2"
    "ci-info" "^4.2.0"
    "deepmerge" "^4.3.1"
    "glob" "^10.3.10"
    "graceful-fs" "^4.2.11"
    "jest-circus" "30.2.0"
    "jest-docblock" "30.2.0"
    "jest-environment-node" "30.2.0"
    "jest-regex-util" "30.0.1"
    "jest-resolve" "30.2.0"
    "jest-runner" "30.2.0"
    "jest-util" "30.2.0"
    "jest-validate" "30.2.0"
    "micromatch" "^4.0.8"
    "parse-json" "^5.2.0"
    "pretty-format" "30.2.0"
    "slash" "^3.0.0"
    "strip-json-comments" "^3.1.1"

"jest-diff@30.2.0":
  "integrity" "sha512-dQHFo3Pt4/NLlG5z4PxZ/3yZTZ1C7s9hveiOj+GCN+uT109NC2QgsoVZsVOAvbJ3RgKkvyLGXZV9+piDpWbm6A=="
  "resolved" "https://registry.npmjs.org/jest-diff/-/jest-diff-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/diff-sequences" "30.0.1"
    "@jest/get-type" "30.1.0"
    "chalk" "^4.1.2"
    "pretty-format" "30.2.0"

"jest-docblock@30.2.0":
  "integrity" "sha512-tR/FFgZKS1CXluOQzZvNH3+0z9jXr3ldGSD8bhyuxvlVUwbeLOGynkunvlTMxchC5urrKndYiwCFC0DLVjpOCA=="
  "resolved" "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "detect-newline" "^3.1.0"

"jest-each@30.2.0":
  "integrity" "sha512-lpWlJlM7bCUf1mfmuqTA8+j2lNURW9eNafOy99knBM01i5CQeY5UH1vZjgT9071nDJac1M4XsbyI44oNOdhlDQ=="
  "resolved" "https://registry.npmjs.org/jest-each/-/jest-each-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/get-type" "30.1.0"
    "@jest/types" "30.2.0"
    "chalk" "^4.1.2"
    "jest-util" "30.2.0"
    "pretty-format" "30.2.0"

"jest-environment-node@30.2.0":
  "integrity" "sha512-ElU8v92QJ9UrYsKrxDIKCxu6PfNj4Hdcktcn0JX12zqNdqWHB0N+hwOnnBBXvjLd2vApZtuLUGs1QSY+MsXoNA=="
  "resolved" "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/environment" "30.2.0"
    "@jest/fake-timers" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "jest-mock" "30.2.0"
    "jest-util" "30.2.0"
    "jest-validate" "30.2.0"

"jest-haste-map@30.2.0":
  "integrity" "sha512-sQA/jCb9kNt+neM0anSj6eZhLZUIhQgwDt7cPGjumgLM4rXsfb9kpnlacmvZz3Q5tb80nS+oG/if+NBKrHC+Xw=="
  "resolved" "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "anymatch" "^3.1.3"
    "fb-watchman" "^2.0.2"
    "graceful-fs" "^4.2.11"
    "jest-regex-util" "30.0.1"
    "jest-util" "30.2.0"
    "jest-worker" "30.2.0"
    "micromatch" "^4.0.8"
    "walker" "^1.0.8"
  optionalDependencies:
    "fsevents" "^2.3.3"

"jest-leak-detector@30.2.0":
  "integrity" "sha512-M6jKAjyzjHG0SrQgwhgZGy9hFazcudwCNovY/9HPIicmNSBuockPSedAP9vlPK6ONFJ1zfyH/M2/YYJxOz5cdQ=="
  "resolved" "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/get-type" "30.1.0"
    "pretty-format" "30.2.0"

"jest-matcher-utils@30.2.0":
  "integrity" "sha512-dQ94Nq4dbzmUWkQ0ANAWS9tBRfqCrn0bV9AMYdOi/MHW726xn7eQmMeRTpX2ViC00bpNaWXq+7o4lIQ3AX13Hg=="
  "resolved" "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/get-type" "30.1.0"
    "chalk" "^4.1.2"
    "jest-diff" "30.2.0"
    "pretty-format" "30.2.0"

"jest-message-util@30.2.0":
  "integrity" "sha512-y4DKFLZ2y6DxTWD4cDe07RglV88ZiNEdlRfGtqahfbIjfsw1nMCPx49Uev4IA/hWn3sDKyAnSPwoYSsAEdcimw=="
  "resolved" "https://registry.npmjs.org/jest-message-util/-/jest-message-util-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@jest/types" "30.2.0"
    "@types/stack-utils" "^2.0.3"
    "chalk" "^4.1.2"
    "graceful-fs" "^4.2.11"
    "micromatch" "^4.0.8"
    "pretty-format" "30.2.0"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.6"

"jest-mock@30.2.0":
  "integrity" "sha512-JNNNl2rj4b5ICpmAcq+WbLH83XswjPbjH4T7yvGzfAGCPh1rw+xVNbtk+FnRslvt9lkCcdn9i1oAoKUuFsOxRw=="
  "resolved" "https://registry.npmjs.org/jest-mock/-/jest-mock-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "jest-util" "30.2.0"

"jest-pnp-resolver@^1.2.3":
  "integrity" "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w=="
  "resolved" "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  "version" "1.2.3"

"jest-regex-util@30.0.1":
  "integrity" "sha512-jHEQgBXAgc+Gh4g0p3bCevgRCVRkB4VB70zhoAE48gxeSr1hfUOsM/C2WoJgVL7Eyg//hudYENbm3Ne+/dRVVA=="
  "resolved" "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.1.tgz"
  "version" "30.0.1"

"jest-resolve-dependencies@30.2.0":
  "integrity" "sha512-xTOIGug/0RmIe3mmCqCT95yO0vj6JURrn1TKWlNbhiAefJRWINNPgwVkrVgt/YaerPzY3iItufd80v3lOrFJ2w=="
  "resolved" "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "jest-regex-util" "30.0.1"
    "jest-snapshot" "30.2.0"

"jest-resolve@*", "jest-resolve@30.2.0":
  "integrity" "sha512-TCrHSxPlx3tBY3hWNtRQKbtgLhsXa1WmbJEqBlTBrGafd5fiQFByy2GNCEoGR+Tns8d15GaL9cxEzKOO3GEb2A=="
  "resolved" "https://registry.npmjs.org/jest-resolve/-/jest-resolve-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "chalk" "^4.1.2"
    "graceful-fs" "^4.2.11"
    "jest-haste-map" "30.2.0"
    "jest-pnp-resolver" "^1.2.3"
    "jest-util" "30.2.0"
    "jest-validate" "30.2.0"
    "slash" "^3.0.0"
    "unrs-resolver" "^1.7.11"

"jest-runner@30.2.0":
  "integrity" "sha512-PqvZ2B2XEyPEbclp+gV6KO/F1FIFSbIwewRgmROCMBo/aZ6J1w8Qypoj2pEOcg3G2HzLlaP6VUtvwCI8dM3oqQ=="
  "resolved" "https://registry.npmjs.org/jest-runner/-/jest-runner-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/console" "30.2.0"
    "@jest/environment" "30.2.0"
    "@jest/test-result" "30.2.0"
    "@jest/transform" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "emittery" "^0.13.1"
    "exit-x" "^0.2.2"
    "graceful-fs" "^4.2.11"
    "jest-docblock" "30.2.0"
    "jest-environment-node" "30.2.0"
    "jest-haste-map" "30.2.0"
    "jest-leak-detector" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-resolve" "30.2.0"
    "jest-runtime" "30.2.0"
    "jest-util" "30.2.0"
    "jest-watcher" "30.2.0"
    "jest-worker" "30.2.0"
    "p-limit" "^3.1.0"
    "source-map-support" "0.5.13"

"jest-runtime@30.2.0":
  "integrity" "sha512-p1+GVX/PJqTucvsmERPMgCPvQJpFt4hFbM+VN3n8TMo47decMUcJbt+rgzwrEme0MQUA/R+1de2axftTHkKckg=="
  "resolved" "https://registry.npmjs.org/jest-runtime/-/jest-runtime-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/environment" "30.2.0"
    "@jest/fake-timers" "30.2.0"
    "@jest/globals" "30.2.0"
    "@jest/source-map" "30.0.1"
    "@jest/test-result" "30.2.0"
    "@jest/transform" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "cjs-module-lexer" "^2.1.0"
    "collect-v8-coverage" "^1.0.2"
    "glob" "^10.3.10"
    "graceful-fs" "^4.2.11"
    "jest-haste-map" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-mock" "30.2.0"
    "jest-regex-util" "30.0.1"
    "jest-resolve" "30.2.0"
    "jest-snapshot" "30.2.0"
    "jest-util" "30.2.0"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"

"jest-snapshot@30.2.0":
  "integrity" "sha512-5WEtTy2jXPFypadKNpbNkZ72puZCa6UjSr/7djeecHWOu7iYhSXSnHScT8wBz3Rn8Ena5d5RYRcsyKIeqG1IyA=="
  "resolved" "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@babel/core" "^7.27.4"
    "@babel/generator" "^7.27.5"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"
    "@babel/types" "^7.27.3"
    "@jest/expect-utils" "30.2.0"
    "@jest/get-type" "30.1.0"
    "@jest/snapshot-utils" "30.2.0"
    "@jest/transform" "30.2.0"
    "@jest/types" "30.2.0"
    "babel-preset-current-node-syntax" "^1.2.0"
    "chalk" "^4.1.2"
    "expect" "30.2.0"
    "graceful-fs" "^4.2.11"
    "jest-diff" "30.2.0"
    "jest-matcher-utils" "30.2.0"
    "jest-message-util" "30.2.0"
    "jest-util" "30.2.0"
    "pretty-format" "30.2.0"
    "semver" "^7.7.2"
    "synckit" "^0.11.8"

"jest-util@^29.0.0 || ^30.0.0", "jest-util@30.2.0":
  "integrity" "sha512-QKNsM0o3Xe6ISQU869e+DhG+4CK/48aHYdJZGlFQVTjnbvgpcKyxpzk29fGiO7i/J8VENZ+d2iGnSsvmuHywlA=="
  "resolved" "https://registry.npmjs.org/jest-util/-/jest-util-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "chalk" "^4.1.2"
    "ci-info" "^4.2.0"
    "graceful-fs" "^4.2.11"
    "picomatch" "^4.0.2"

"jest-validate@30.2.0":
  "integrity" "sha512-FBGWi7dP2hpdi8nBoWxSsLvBFewKAg0+uSQwBaof4Y4DPgBabXgpSYC5/lR7VmnIlSpASmCi/ntRWPbv7089Pw=="
  "resolved" "https://registry.npmjs.org/jest-validate/-/jest-validate-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/get-type" "30.1.0"
    "@jest/types" "30.2.0"
    "camelcase" "^6.3.0"
    "chalk" "^4.1.2"
    "leven" "^3.1.0"
    "pretty-format" "30.2.0"

"jest-watcher@30.2.0":
  "integrity" "sha512-PYxa28dxJ9g777pGm/7PrbnMeA0Jr7osHP9bS7eJy9DuAjMgdGtxgf0uKMyoIsTWAkIbUW5hSDdJ3urmgXBqxg=="
  "resolved" "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/test-result" "30.2.0"
    "@jest/types" "30.2.0"
    "@types/node" "*"
    "ansi-escapes" "^4.3.2"
    "chalk" "^4.1.2"
    "emittery" "^0.13.1"
    "jest-util" "30.2.0"
    "string-length" "^4.0.2"

"jest-worker@^27.4.5":
  "integrity" "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jest-worker@30.2.0":
  "integrity" "sha512-0Q4Uk8WF7BUwqXHuAjc23vmopWJw5WH7w2tqBoUOZpOjW/ZnR44GXXd1r82RvnmI2GZge3ivrYXk/BE2+VtW2g=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@types/node" "*"
    "@ungap/structured-clone" "^1.3.0"
    "jest-util" "30.2.0"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.1.1"

"jest@^29.0.0 || ^30.0.0", "jest@^30.0.3":
  "integrity" "sha512-F26gjC0yWN8uAA5m5Ss8ZQf5nDHWGlN/xWZIh8S5SRbsEKBovwZhxGd6LJlbZYxBgCYOtreSUyb8hpXyGC5O4A=="
  "resolved" "https://registry.npmjs.org/jest/-/jest-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/core" "30.2.0"
    "@jest/types" "30.2.0"
    "import-local" "^3.2.0"
    "jest-cli" "30.2.0"

"jmespath@0.16.0":
  "integrity" "sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw=="
  "resolved" "https://registry.npmjs.org/jmespath/-/jmespath-0.16.0.tgz"
  "version" "0.16.0"

"js-md4@^0.3.2":
  "integrity" "sha512-/GDnfQYsltsjRswQhN9fhv3EMw2sCpUdrdxyWDOUK7eyD++r3gRhzgiQgc/x4MAv2i1iuQ4lxO5mvqM3vj4bwA=="
  "resolved" "https://registry.npmjs.org/js-md4/-/js-md4-0.3.2.tgz"
  "version" "0.3.2"

"js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@^4.1.0", "js-yaml@4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsesc@^3.0.2":
  "integrity" "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  "version" "3.1.0"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-parse-even-better-errors@^2.3.0", "json-parse-even-better-errors@^2.3.1":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^2.2.2", "json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonc-parser@3.3.1":
  "integrity" "sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ=="
  "resolved" "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.3.1.tgz"
  "version" "3.3.1"

"jsonfile@^6.0.1":
  "integrity" "sha512-FGuPw30AdOIUTRMC2OMRtQV+jkVj2cfPqSeWXv1NEAJ1qZ5zb1X6z1mFhbfOB/iy3ssJCD+3KuZ8r8C3uVFlAg=="
  "resolved" "https://registry.npmjs.org/jsonfile/-/jsonfile-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonwebtoken@^9.0.0", "jsonwebtoken@9.0.2":
  "integrity" "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ=="
  "resolved" "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "jws" "^3.2.2"
    "lodash.includes" "^4.3.0"
    "lodash.isboolean" "^3.0.3"
    "lodash.isinteger" "^4.0.4"
    "lodash.isnumber" "^3.0.3"
    "lodash.isplainobject" "^4.0.6"
    "lodash.isstring" "^4.0.1"
    "lodash.once" "^4.0.0"
    "ms" "^2.1.1"
    "semver" "^7.5.4"

"jszip@^3.7.1":
  "integrity" "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g=="
  "resolved" "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "setimmediate" "^1.0.5"

"jwa@^1.4.1":
  "integrity" "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw=="
  "resolved" "https://registry.npmjs.org/jwa/-/jwa-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "buffer-equal-constant-time" "^1.0.1"
    "ecdsa-sig-formatter" "1.0.11"
    "safe-buffer" "^5.0.1"

"jws@^3.2.2":
  "integrity" "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA=="
  "resolved" "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "jwa" "^1.4.1"
    "safe-buffer" "^5.0.1"

"keyv@^4.5.4":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"keyv@^5.5.3", "keyv@>=5":
  "integrity" "sha512-h0Un1ieD+HUrzBH6dJXhod3ifSghk5Hw/2Y4/KHBziPlZecrFyE9YOTPU6eOs0V9pYl8gOs86fkr/KN8lUX39A=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-5.5.3.tgz"
  "version" "5.5.3"
  dependencies:
    "@keyv/serialize" "^1.1.1"

"leven@^3.1.0":
  "integrity" "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="
  "resolved" "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  "version" "3.1.0"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"libphonenumber-js@^1.10.20", "libphonenumber-js@^1.11.1":
  "integrity" "sha512-l5IlyL9AONj4voSd7q9xkuQOL4u8Ty44puTic7J88CmdXkxfGsRfoVLXHCxppwehgpb/Chdb80FFehHqjN3ItQ=="
  "resolved" "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.12.24.tgz"
  "version" "1.12.24"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"load-esm@1.0.2":
  "integrity" "sha512-nVAvWk/jeyrWyXEAs84mpQCYccxRqgKY4OznLuJhJCa0XsPSfdOIr2zvBZEj3IHEHbX97jjscKRRV539bW0Gpw=="
  "resolved" "https://registry.npmjs.org/load-esm/-/load-esm-1.0.2.tgz"
  "version" "1.0.2"

"loader-runner@^4.2.0":
  "integrity" "sha512-IWqP2SCPhyVFTBtRcgMHdzlf9ul25NwaFx4wCEH/KjAXuuHY4yNjvPXsBokp8jCB936PyWRaPKUNh8NvylLp2Q=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.1.tgz"
  "version" "4.3.1"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash.groupby@^4.6.0":
  "integrity" "sha512-5dcWxm23+VAoz+awKmBaiBvzox8+RqMgFhi7UvX9DHZr2HdxHXM/Wrf8cfKpsW37RNrvtPn6hSwNqurSILbmJw=="
  "resolved" "https://registry.npmjs.org/lodash.groupby/-/lodash.groupby-4.6.0.tgz"
  "version" "4.6.0"

"lodash.includes@^4.3.0":
  "integrity" "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="
  "resolved" "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  "version" "4.3.0"

"lodash.isboolean@^3.0.3":
  "integrity" "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="
  "resolved" "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isinteger@^4.0.4":
  "integrity" "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="
  "resolved" "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  "version" "4.0.4"

"lodash.isnumber@^3.0.3":
  "integrity" "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="
  "resolved" "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="
  "resolved" "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.isstring@^4.0.1":
  "integrity" "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="
  "resolved" "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  "version" "4.0.1"

"lodash.memoize@^4.1.2":
  "integrity" "sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag=="
  "resolved" "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.once@^4.0.0":
  "integrity" "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="
  "resolved" "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  "version" "4.1.1"

"lodash@^4.17.21", "lodash@4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^4.1.0":
  "integrity" "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="
  "resolved" "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"lop@^0.4.2":
  "integrity" "sha512-RefILVDQ4DKoRZsJ4Pj22TxE3omDO47yFpkIBoDKzkqPRISs5U1cnAdg/5583YPkWPaLIYHOKRMQSvjFsO26cw=="
  "resolved" "https://registry.npmjs.org/lop/-/lop-0.4.2.tgz"
  "version" "0.4.2"
  dependencies:
    "duck" "^0.1.12"
    "option" "~0.2.1"
    "underscore" "^1.13.1"

"lru-cache@^10.2.0":
  "integrity" "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  "version" "10.4.3"

"lru-cache@^11.0.0":
  "integrity" "sha512-F9ODfyqML2coTIsQpSkRHnLSZMtkU8Q+mSfcaIyKwy58u+8k5nvAYeiNhsyMARvzNcXJ9QfWVrcPsC9e9rAxtg=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-11.2.2.tgz"
  "version" "11.2.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"luxon@~3.7.0":
  "integrity" "sha512-vtEhXh/gNjI9Yg1u4jX/0YVPMvxzHuGgCm6tC5kZyb08yjGWGnqAjGJvcXbqQR2P3MyMEFnRbpcdFS6PBcLqew=="
  "resolved" "https://registry.npmjs.org/luxon/-/luxon-3.7.2.tgz"
  "version" "3.7.2"

"magic-string@0.30.17":
  "integrity" "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"
  "version" "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

"make-dir@^4.0.0":
  "integrity" "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "semver" "^7.5.3"

"make-error@^1.1.1", "make-error@^1.3.6":
  "integrity" "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw=="
  "resolved" "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  "version" "1.3.6"

"makeerror@1.0.12":
  "integrity" "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg=="
  "resolved" "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "tmpl" "1.0.5"

"mammoth@^1.11.0":
  "integrity" "sha512-BcEqqY/BOwIcI1iR5tqyVlqc3KIaMRa4egSoK83YAVrBf6+yqdAAbtUcFDCWX8Zef8/fgNZ6rl4VUv+vVX8ddQ=="
  "resolved" "https://registry.npmjs.org/mammoth/-/mammoth-1.11.0.tgz"
  "version" "1.11.0"
  dependencies:
    "@xmldom/xmldom" "^0.8.6"
    "argparse" "~1.0.3"
    "base64-js" "^1.5.1"
    "bluebird" "~3.4.0"
    "dingbat-to-unicode" "^1.0.1"
    "jszip" "^3.7.1"
    "lop" "^0.4.2"
    "path-is-absolute" "^1.0.0"
    "underscore" "^1.13.1"
    "xmlbuilder" "^10.0.0"

"math-intrinsics@^1.1.0":
  "integrity" "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="
  "resolved" "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  "version" "1.1.0"

"media-typer@^1.1.0":
  "integrity" "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw=="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz"
  "version" "1.1.0"

"media-typer@0.3.0":
  "integrity" "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memfs@^3.4.1":
  "integrity" "sha512-EGowvkkgbMcIChjMTMkESFDbZeSh8xZ7kNSF0hAiAN4Jh6jgHCRS0Ga/+C8y6Au+oqpezRHCfPsmJ2+DwAgiwQ=="
  "resolved" "https://registry.npmjs.org/memfs/-/memfs-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "fs-monkey" "^1.0.4"

"merge-descriptors@^2.0.0":
  "integrity" "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g=="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz"
  "version" "2.0.0"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@^1.1.2":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^4.0.0", "micromatch@^4.0.8":
  "integrity" "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "braces" "^3.0.3"
    "picomatch" "^2.3.1"

"mime-db@^1.54.0":
  "integrity" "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz"
  "version" "1.54.0"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@^2.1.27", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime-types@^3.0.0":
  "integrity" "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "mime-db" "^1.54.0"

"mime-types@^3.0.1":
  "integrity" "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "mime-db" "^1.54.0"

"mime@2.6.0":
  "integrity" "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz"
  "version" "2.6.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"minimatch@^10.0.3":
  "integrity" "sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-10.0.3.tgz"
  "version" "10.0.3"
  dependencies:
    "@isaacs/brace-expansion" "^5.0.0"

"minimatch@^3.0.4", "minimatch@^3.1.1", "minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^9.0.4":
  "integrity" "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  "version" "9.0.5"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist@^1.2.5", "minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", "minipass@^7.1.2":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"mkdirp@^0.5.6":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"ms@^2.1.1", "ms@^2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"mssql@^11.0.1", "mssql@^9.1.1 || ^10.0.1 || ^11.0.1":
  "integrity" "sha512-KlGNsugoT90enKlR8/G36H0kTxPthDhmtNUCwEHvgRza5Cjpjoj+P2X6eMpFUDN7pFrJZsKadL4x990G8RBE1w=="
  "resolved" "https://registry.npmjs.org/mssql/-/mssql-11.0.1.tgz"
  "version" "11.0.1"
  dependencies:
    "@tediousjs/connection-string" "^0.5.0"
    "commander" "^11.0.0"
    "debug" "^4.3.3"
    "rfdc" "^1.3.0"
    "tarn" "^3.0.2"
    "tedious" "^18.2.1"

"multer@2.0.2":
  "integrity" "sha512-u7f2xaZ/UG8oLXHvtF/oWTRvT44p9ecwBBqTwgJVq0+4BW1g8OW01TyMEGWBHbyMOYVHXslaut7qEQ1meATXgw=="
  "resolved" "https://registry.npmjs.org/multer/-/multer-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "append-field" "^1.0.0"
    "busboy" "^1.6.0"
    "concat-stream" "^2.0.0"
    "mkdirp" "^0.5.6"
    "object-assign" "^4.1.1"
    "type-is" "^1.6.18"
    "xtend" "^4.0.2"

"mute-stream@^2.0.0":
  "integrity" "sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA=="
  "resolved" "https://registry.npmjs.org/mute-stream/-/mute-stream-2.0.0.tgz"
  "version" "2.0.0"

"nanoid@3.3.4":
  "integrity" "sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz"
  "version" "3.3.4"

"napi-postinstall@^0.3.0":
  "integrity" "sha512-PHI5f1O0EP5xJ9gQmFGMS6IZcrVvTjpXjz7Na41gTE7eE2hK11lg04CECCYEEjdc17EV4DO+fkGEtt7TpTaTiQ=="
  "resolved" "https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.3.4.tgz"
  "version" "0.3.4"

"native-duplexpair@^1.0.0":
  "integrity" "sha512-E7QQoM+3jvNtlmyfqRZ0/U75VFgCls+fSkbml2MpgWkWyz3ox8Y58gNhfuziuQYGNNQAbFZJQck55LHCnCK6CA=="
  "resolved" "https://registry.npmjs.org/native-duplexpair/-/native-duplexpair-1.0.0.tgz"
  "version" "1.0.0"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@^1.0.0":
  "integrity" "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz"
  "version" "1.0.0"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nestjs-i18n@^10.5.1":
  "integrity" "sha512-cJJFz+RUfav23QACpGCq1pdXNLYC3tBesrP14RGoE/YYcD4xosQPX2eyjvDNuo0Ti63Xtn6j57wDNEUKrZqmSw=="
  "resolved" "https://registry.npmjs.org/nestjs-i18n/-/nestjs-i18n-10.5.1.tgz"
  "version" "10.5.1"
  dependencies:
    "accept-language-parser" "^1.5.0"
    "chokidar" "^3.6.0"
    "cookie" "^0.7.0"
    "iterare" "^1.2.1"
    "js-yaml" "^4.1.0"
    "string-format" "^2.0.0"

"nestjs-typeorm3-kit@^0.1.8":
  "integrity" "sha512-xOVSqn96GEk7Sx0FdmxJs4neJP7IYRrHjyoSsWj4dyPOM0HmrwALEXpq/DX4d3YOr9tnmpaEtOp65j5FpmXdKQ=="
  "resolved" "https://registry.npmjs.org/nestjs-typeorm3-kit/-/nestjs-typeorm3-kit-0.1.8.tgz"
  "version" "0.1.8"

"node-abort-controller@^3.0.1":
  "integrity" "sha512-AGK2yQKIjRuqnc6VkX2Xj5d+QW8xZ87pa1UK6yA6ouUyuxfHuMP6umE5QK7UmTeOAymo+Zx1Fxiuw9rVx8taHQ=="
  "resolved" "https://registry.npmjs.org/node-abort-controller/-/node-abort-controller-3.1.1.tgz"
  "version" "3.1.1"

"node-addon-api@^8.3.0":
  "integrity" "sha512-/bRZty2mXUIFY/xU5HLvveNHlswNJej+RnxBjOMkidWfwZzgTbPG1E3K5TOxRLOR+5hX7bSofy8yf1hZevMS8A=="
  "resolved" "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.5.0.tgz"
  "version" "8.5.0"

"node-emoji@1.11.0":
  "integrity" "sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A=="
  "resolved" "https://registry.npmjs.org/node-emoji/-/node-emoji-1.11.0.tgz"
  "version" "1.11.0"
  dependencies:
    "lodash" "^4.17.21"

"node-gyp-build@^4.8.4":
  "integrity" "sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ=="
  "resolved" "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.8.4.tgz"
  "version" "4.8.4"

"node-int64@^0.4.0":
  "integrity" "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw=="
  "resolved" "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-releases@^2.0.21":
  "integrity" "sha512-cCmFDMSm26S6tQSDpBCg/NR8NENrVPhAJSf+XbxBG4rPFaaonlEoE9wHQmun+cls499TQGSb7ZyPBRlzgKfpeg=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.23.tgz"
  "version" "2.0.23"

"nodemailer@^7.0.9":
  "integrity" "sha512-9/Qm0qXIByEP8lEV2qOqcAW7bRpL8CR9jcTwk3NBnHJNmP9fIJ86g2fgmIXqHY+nj55ZEMwWqYAT2QTDpRUYiQ=="
  "resolved" "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.9.tgz"
  "version" "7.0.9"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"oauth@0.10.x":
  "integrity" "sha512-JtFnB+8nxDEXgNyniwz573xxbKSOu3R8D40xQKqcjwJ2CDkYqUDI53o6IuzDJBx60Z8VKCm271+t8iFjakrl8Q=="
  "resolved" "https://registry.npmjs.org/oauth/-/oauth-0.10.2.tgz"
  "version" "0.10.2"

"object-assign@^4", "object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-hash@3.0.0":
  "integrity" "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw=="
  "resolved" "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  "version" "3.0.0"

"object-inspect@^1.13.3":
  "integrity" "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  "version" "1.13.4"

"on-finished@^2.4.1":
  "integrity" "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"once@^1.3.0", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^10.1.0":
  "integrity" "sha512-YgBpdJHPyQ2UE5x+hlSXcnejzAvD0b22U2OuAP+8OnlJT+PjWPxtgmGqKKc+RgTM63U9gN0YzrYc71R2WT/hTA=="
  "resolved" "https://registry.npmjs.org/open/-/open-10.2.0.tgz"
  "version" "10.2.0"
  dependencies:
    "default-browser" "^5.2.1"
    "define-lazy-prop" "^3.0.0"
    "is-inside-container" "^1.0.0"
    "wsl-utils" "^0.1.0"

"openapi3-ts@^3.0.0":
  "integrity" "sha512-/ykNWRV5Qs0Nwq7Pc0nJ78fgILvOT/60OxEmB3v7yQ8a8Bwcm43D4diaYazG/KBn6czA+52XYy931WFLMCUeSg=="
  "resolved" "https://registry.npmjs.org/openapi3-ts/-/openapi3-ts-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "yaml" "^2.2.1"

"option@~0.2.1":
  "integrity" "sha512-pkEqbDyl8ou5cpq+VsnQbe/WlEy5qS7xPzMS1U55OCG9KPvwFD46zDbxQIj3egJSFc3D+XhYOPUzz49zQAVy7A=="
  "resolved" "https://registry.npmjs.org/option/-/option-0.2.4.tgz"
  "version" "0.2.4"

"optionator@^0.9.3":
  "integrity" "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  "version" "0.9.4"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.5"

"ora@5.4.1":
  "integrity" "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ=="
  "resolved" "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^3.0.2", "p-limit@^3.1.0":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"package-json-from-dist@^1.0.0":
  "integrity" "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw=="
  "resolved" "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  "version" "1.0.1"

"pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^5.2.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parseurl@^1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"passport-jwt@^4.0.1":
  "integrity" "sha512-UCKMDYhNuGOBE9/9Ycuoyh7vP6jpeTp/+sfMJl7nLff/t6dps+iaeE0hhNkKN8/HZHcJ7lCdOyDxHdDoxoSvdQ=="
  "resolved" "https://registry.npmjs.org/passport-jwt/-/passport-jwt-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "jsonwebtoken" "^9.0.0"
    "passport-strategy" "^1.0.0"

"passport-local@^1.0.0":
  "integrity" "sha512-9wCE6qKznvf9mQYYbgJ3sVOHmCWoUNMVFoZzNoznmISbhnNNPhN9xfY3sLmScHMetEJeoY7CXwfhCe7argfQow=="
  "resolved" "https://registry.npmjs.org/passport-local/-/passport-local-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "passport-strategy" "1.x.x"

"passport-oauth2@^1.8.0":
  "integrity" "sha512-cjsQbOrXIDE4P8nNb3FQRCCmJJ/utnFKEz2NX209f7KOHPoX18gF7gBzBbLLsj2/je4KrgiwLLGjf0lm9rtTBA=="
  "resolved" "https://registry.npmjs.org/passport-oauth2/-/passport-oauth2-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "base64url" "3.x.x"
    "oauth" "0.10.x"
    "passport-strategy" "1.x.x"
    "uid2" "0.0.x"
    "utils-merge" "1.x.x"

"passport-strategy@^1.0.0", "passport-strategy@1.x.x":
  "integrity" "sha512-CB97UUvDKJde2V0KDWWB3lyf6PC3FaZP7YxZ2G8OAtn9p4HI9j9JLP9qjOGZFvyl8uwNT8qM+hGnz/n16NI7oA=="
  "resolved" "https://registry.npmjs.org/passport-strategy/-/passport-strategy-1.0.0.tgz"
  "version" "1.0.0"

"passport@^0.5.0 || ^0.6.0 || ^0.7.0", "passport@^0.7.0":
  "integrity" "sha512-cPLl+qZpSc+ireUvt+IzqbED1cHHkDoVYMo30jbJIdOOjQ1MQYZBPiNvmi8UM6lJuOpTPXJGZQk0DtC4y61MYQ=="
  "resolved" "https://registry.npmjs.org/passport/-/passport-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "passport-strategy" "1.x.x"
    "pause" "0.0.1"
    "utils-merge" "^1.0.1"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-scurry@^1.11.1":
  "integrity" "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA=="
  "resolved" "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "lru-cache" "^10.2.0"
    "minipass" "^5.0.0 || ^6.0.2 || ^7.0.0"

"path-scurry@^2.0.0":
  "integrity" "sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg=="
  "resolved" "https://registry.npmjs.org/path-scurry/-/path-scurry-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "lru-cache" "^11.0.0"
    "minipass" "^7.1.2"

"path-to-regexp@^8.0.0":
  "integrity" "sha512-7jdwVIRtsP8MYpdXSwOS0YdD0Du+qOoF/AEPIt88PcCFrZCzx41oxku1jD88hZBwbNUIEfpqvuhjFaMAqMTWnA=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.3.0.tgz"
  "version" "8.3.0"

"path-to-regexp@8.2.0":
  "integrity" "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz"
  "version" "8.2.0"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pause@0.0.1":
  "integrity" "sha512-KG8UEiEVkR3wGEb4m5yZkVCzigAD+cVEJck2CzYZO37ZGJfctvVptVO192MwrtPhzONn6go8ylnOdMhKqi4nfg=="
  "resolved" "https://registry.npmjs.org/pause/-/pause-0.0.1.tgz"
  "version" "0.0.1"

"pg-cloudflare@^1.2.7":
  "integrity" "sha512-YgCtzMH0ptvZJslLM1ffsY4EuGaU0cx4XSdXLRFae8bPP4dS5xL1tNB3k2o/N64cHJpwU7dxKli/nZ2lUa5fLg=="
  "resolved" "https://registry.npmjs.org/pg-cloudflare/-/pg-cloudflare-1.2.7.tgz"
  "version" "1.2.7"

"pg-connection-string@^2.9.1":
  "integrity" "sha512-nkc6NpDcvPVpZXxrreI/FOtX3XemeLl8E0qFr6F2Lrm/I8WOnaWNhIPK2Z7OHpw7gh5XJThi6j6ppgNoaT1w4w=="
  "resolved" "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-2.9.1.tgz"
  "version" "2.9.1"

"pg-int8@1.0.1":
  "integrity" "sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw=="
  "resolved" "https://registry.npmjs.org/pg-int8/-/pg-int8-1.0.1.tgz"
  "version" "1.0.1"

"pg-pool@^3.10.1":
  "integrity" "sha512-Tu8jMlcX+9d8+QVzKIvM/uJtp07PKr82IUOYEphaWcoBhIYkoHpLXN3qO59nAI11ripznDsEzEv8nUxBVWajGg=="
  "resolved" "https://registry.npmjs.org/pg-pool/-/pg-pool-3.10.1.tgz"
  "version" "3.10.1"

"pg-protocol@^1.10.3":
  "integrity" "sha512-6DIBgBQaTKDJyxnXaLiLR8wBpQQcGWuAESkRBX/t6OwA8YsqP+iVSiond2EDy6Y/dsGk8rh/jtax3js5NeV7JQ=="
  "resolved" "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.10.3.tgz"
  "version" "1.10.3"

"pg-types@2.2.0":
  "integrity" "sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA=="
  "resolved" "https://registry.npmjs.org/pg-types/-/pg-types-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "pg-int8" "1.0.1"
    "postgres-array" "~2.0.0"
    "postgres-bytea" "~1.0.0"
    "postgres-date" "~1.0.4"
    "postgres-interval" "^1.1.0"

"pg@^8.16.2", "pg@^8.5.1", "pg@>=8.0":
  "integrity" "sha512-enxc1h0jA/aq5oSDMvqyW3q89ra6XIIDZgCX9vkMrnz5DFTw/Ny3Li2lFQ+pt3L6MCgm/5o2o8HW9hiJji+xvw=="
  "resolved" "https://registry.npmjs.org/pg/-/pg-8.16.3.tgz"
  "version" "8.16.3"
  dependencies:
    "pg-connection-string" "^2.9.1"
    "pg-pool" "^3.10.1"
    "pg-protocol" "^1.10.3"
    "pg-types" "2.2.0"
    "pgpass" "1.0.5"
  optionalDependencies:
    "pg-cloudflare" "^1.2.7"

"pgpass@1.0.5":
  "integrity" "sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug=="
  "resolved" "https://registry.npmjs.org/pgpass/-/pgpass-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "split2" "^4.1.0"

"picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.0.4":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomatch@^2.2.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomatch@^4.0.2":
  "integrity" "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz"
  "version" "4.0.3"

"picomatch@4.0.2":
  "integrity" "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  "version" "4.0.2"

"pirates@^4.0.7":
  "integrity" "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA=="
  "resolved" "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz"
  "version" "4.0.7"

"pkg-dir@^4.2.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pluralize@8.0.0":
  "integrity" "sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA=="
  "resolved" "https://registry.npmjs.org/pluralize/-/pluralize-8.0.0.tgz"
  "version" "8.0.0"

"pngjs@^5.0.0":
  "integrity" "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw=="
  "resolved" "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz"
  "version" "5.0.0"

"possible-typed-array-names@^1.0.0":
  "integrity" "sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg=="
  "resolved" "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz"
  "version" "1.1.0"

"postgres-array@~2.0.0":
  "integrity" "sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA=="
  "resolved" "https://registry.npmjs.org/postgres-array/-/postgres-array-2.0.0.tgz"
  "version" "2.0.0"

"postgres-bytea@~1.0.0":
  "integrity" "sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w=="
  "resolved" "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-1.0.0.tgz"
  "version" "1.0.0"

"postgres-date@~1.0.4":
  "integrity" "sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q=="
  "resolved" "https://registry.npmjs.org/postgres-date/-/postgres-date-1.0.7.tgz"
  "version" "1.0.7"

"postgres-interval@^1.1.0":
  "integrity" "sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ=="
  "resolved" "https://registry.npmjs.org/postgres-interval/-/postgres-interval-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "xtend" "^4.0.0"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^3.6.2", "prettier@>=3.0.0":
  "integrity" "sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz"
  "version" "3.6.2"

"pretty-format@^30.0.0", "pretty-format@30.2.0":
  "integrity" "sha512-9uBdv/B4EefsuAL+pWqueZyZS2Ba+LxfFeQ9DN14HU4bN8bhaxKdkpjpB6fs9+pSjIBu+FXQHImEg8j/Lw0+vA=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-30.2.0.tgz"
  "version" "30.2.0"
  dependencies:
    "@jest/schemas" "30.0.5"
    "ansi-styles" "^5.2.0"
    "react-is" "^18.3.1"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A=="
  "resolved" "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"proxy-addr@^2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"punycode@1.3.2":
  "integrity" "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"pure-rand@^7.0.0":
  "integrity" "sha512-oTUZM/NAZS8p7ANR3SHh30kXB+zK2r2BPcEn/awJIbOvq82WoMN4p62AWWp3Hhw50G0xMsw1mhIBLqHw64EcNQ=="
  "resolved" "https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.1.tgz"
  "version" "7.0.1"

"qrcode@^1.5.4":
  "integrity" "sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg=="
  "resolved" "https://registry.npmjs.org/qrcode/-/qrcode-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "dijkstrajs" "^1.0.1"
    "pngjs" "^5.0.0"
    "yargs" "^15.3.1"

"qs@^6.11.2", "qs@^6.14.0":
  "integrity" "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz"
  "version" "6.14.0"
  dependencies:
    "side-channel" "^1.1.0"

"querystring@0.2.0":
  "integrity" "sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g=="
  "resolved" "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@^3.0.0":
  "integrity" "sha512-9G8cA+tuMS75+6G/TzW8OtLzmBDMo8p1JRxN5AZ+LAp8uxGA8V8GZm4GQ4/N5QNQEnLmg6SS7wyuSmbKepiKqA=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.7.0"
    "unpipe" "1.0.0"

"react-is@^18.3.1":
  "integrity" "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  "version" "18.3.1"

"readable-stream@^3.0.2":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.4.0":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^4.2.0":
  "integrity" "sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "abort-controller" "^3.0.0"
    "buffer" "^6.0.3"
    "events" "^3.3.0"
    "process" "^0.11.10"
    "string_decoder" "^1.3.0"

"readable-stream@~2.3.6":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdirp@^4.0.1":
  "integrity" "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz"
  "version" "4.1.2"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"reflect-metadata@^0.1.12 || ^0.2.0", "reflect-metadata@^0.1.13 || ^0.2.0", "reflect-metadata@^0.1.14 || ^0.2.0", "reflect-metadata@^0.2.0", "reflect-metadata@^0.2.2", "reflect-metadata@>= 0.1.12", "reflect-metadata@>=0.1.13":
  "integrity" "sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q=="
  "resolved" "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.2.2.tgz"
  "version" "0.2.2"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"resolve-cwd@^3.0.0":
  "integrity" "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg=="
  "resolved" "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"reusify@^1.0.4":
  "integrity" "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz"
  "version" "1.1.0"

"rfdc@^1.3.0":
  "integrity" "sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA=="
  "resolved" "https://registry.npmjs.org/rfdc/-/rfdc-1.4.1.tgz"
  "version" "1.4.1"

"router@^2.2.0":
  "integrity" "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ=="
  "resolved" "https://registry.npmjs.org/router/-/router-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "debug" "^4.4.0"
    "depd" "^2.0.0"
    "is-promise" "^4.0.0"
    "parseurl" "^1.3.3"
    "path-to-regexp" "^8.0.0"

"run-applescript@^7.0.0":
  "integrity" "sha512-DPe5pVFaAsinSaV6QjQ6gdiedWDcRCbUuiQfQa2wmWV7+xC9bGulGI8+TdRmoFkAPaBXk8CrAbnlY2ISniJ47Q=="
  "resolved" "https://registry.npmjs.org/run-applescript/-/run-applescript-7.1.0.tgz"
  "version" "7.1.0"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rxjs@*", "rxjs@^7.1.0", "rxjs@^7.2.0", "rxjs@^7.8.1", "rxjs@^7.8.2", "rxjs@>=7.2.0":
  "integrity" "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA=="
  "resolved" "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz"
  "version" "7.8.2"
  dependencies:
    "tslib" "^2.1.0"

"rxjs@7.8.1":
  "integrity" "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg=="
  "resolved" "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz"
  "version" "7.8.1"
  dependencies:
    "tslib" "^2.1.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.2.1", "safe-buffer@~5.2.0", "safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex-test@^1.1.0":
  "integrity" "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw=="
  "resolved" "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "is-regex" "^1.2.1"

"safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@>=0.6.0":
  "integrity" "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz"
  "version" "1.4.1"

"sax@1.2.1":
  "integrity" "sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz"
  "version" "1.2.1"

"schema-utils@^3.1.1":
  "integrity" "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.3.0":
  "integrity" "sha512-eflK8wEtyOE6+hsaRVPxvUKYCpRgzLqDTb8krvAsRIwOGlHoSgYLgBXoubGgLd2fT41/OUYdb48v4k4WWHQurA=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"schema-utils@^4.3.2":
  "integrity" "sha512-eflK8wEtyOE6+hsaRVPxvUKYCpRgzLqDTb8krvAsRIwOGlHoSgYLgBXoubGgLd2fT41/OUYdb48v4k4WWHQurA=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"semver@^5.4.1":
  "integrity" "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
  "version" "5.7.2"

"semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^7.3.4", "semver@^7.3.5", "semver@^7.5.1", "semver@^7.5.3", "semver@^7.5.4", "semver@^7.6.0", "semver@^7.7.2", "semver@^7.7.3":
  "integrity" "sha512-SdsKMrI9TdgjdweUSR9MweHA4EJ8YxHn8DFaDisvhVlUOe4BF1tLD7GAj0lIqWVl+dPb/rExr0Btby5loQm20Q=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.7.3.tgz"
  "version" "7.7.3"

"send@^1.1.0", "send@^1.2.0":
  "integrity" "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw=="
  "resolved" "https://registry.npmjs.org/send/-/send-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "debug" "^4.3.5"
    "encodeurl" "^2.0.0"
    "escape-html" "^1.0.3"
    "etag" "^1.8.1"
    "fresh" "^2.0.0"
    "http-errors" "^2.0.0"
    "mime-types" "^3.0.1"
    "ms" "^2.1.3"
    "on-finished" "^2.4.1"
    "range-parser" "^1.2.1"
    "statuses" "^2.0.1"

"serialize-javascript@^6.0.2":
  "integrity" "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "randombytes" "^2.1.0"

"serve-static@^2.2.0":
  "integrity" "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "encodeurl" "^2.0.0"
    "escape-html" "^1.0.3"
    "parseurl" "^1.3.3"
    "send" "^1.2.0"

"set-blocking@^2.0.0":
  "integrity" "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-function-length@^1.2.2":
  "integrity" "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg=="
  "resolved" "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"setimmediate@^1.0.5":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"sha.js@^2.4.11":
  "integrity" "sha512-8LzC5+bvI45BjpfXU8V5fdU2mfeKiQe1D1gIMn7XUlF3OTUrpdJpPPH4EMAnF0DsHHdSZqCdSss5qCmJKuiO3w=="
  "resolved" "https://registry.npmjs.org/sha.js/-/sha.js-2.4.12.tgz"
  "version" "2.4.12"
  dependencies:
    "inherits" "^2.0.4"
    "safe-buffer" "^5.2.1"
    "to-buffer" "^1.2.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shimmer@^1.2.0":
  "integrity" "sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw=="
  "resolved" "https://registry.npmjs.org/shimmer/-/shimmer-1.2.1.tgz"
  "version" "1.2.1"

"side-channel-list@^1.0.0":
  "integrity" "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA=="
  "resolved" "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"

"side-channel-map@^1.0.1":
  "integrity" "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA=="
  "resolved" "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"

"side-channel-weakmap@^1.0.2":
  "integrity" "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A=="
  "resolved" "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"
    "side-channel-map" "^1.0.1"

"side-channel@^1.1.0":
  "integrity" "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"
    "side-channel-list" "^1.0.0"
    "side-channel-map" "^1.0.1"
    "side-channel-weakmap" "^1.0.2"

"signal-exit@^3.0.2":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"signal-exit@^3.0.3":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"signal-exit@^4.0.1", "signal-exit@^4.1.0":
  "integrity" "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  "version" "4.1.0"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"socket.io-adapter@~2.5.2":
  "integrity" "sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg=="
  "resolved" "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.5.5.tgz"
  "version" "2.5.5"
  dependencies:
    "debug" "~4.3.4"
    "ws" "~8.17.1"

"socket.io-parser@~4.2.4":
  "integrity" "sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew=="
  "resolved" "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"

"socket.io@4.8.1":
  "integrity" "sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg=="
  "resolved" "https://registry.npmjs.org/socket.io/-/socket.io-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "accepts" "~1.3.4"
    "base64id" "~2.0.0"
    "cors" "~2.8.5"
    "debug" "~4.3.2"
    "engine.io" "~6.6.0"
    "socket.io-adapter" "~2.5.2"
    "socket.io-parser" "~4.2.4"

"source-map-support@^0.5.21", "source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-support@0.5.13":
  "integrity" "sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz"
  "version" "0.5.13"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map@^0.6.0", "source-map@^0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.4":
  "integrity" "sha512-i5uvt8C3ikiWeNZSVZNWcfZPItFQOsYTUAOkcUPGd8DqDy1uOUikjt5dG+uRlwyvR108Fb9DOd4GvXfT0N2/uQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.6.tgz"
  "version" "0.7.6"

"source-map@0.7.4":
  "integrity" "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"split2@^4.1.0":
  "integrity" "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg=="
  "resolved" "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz"
  "version" "4.2.0"

"sprintf-js@^1.1.3":
  "integrity" "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz"
  "version" "1.1.3"

"sprintf-js@~1.0.2":
  "integrity" "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"sql-highlight@^6.0.0":
  "integrity" "sha512-ed7OK4e9ywpE7pgRMkMQmZDPKSVdm0oX5IEtZiKnFucSF0zu6c80GZBe38UqHuVhTWJ9xsKgSMjCG2bml86KvA=="
  "resolved" "https://registry.npmjs.org/sql-highlight/-/sql-highlight-6.1.0.tgz"
  "version" "6.1.0"

"ssf@~0.11.2":
  "integrity" "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="
  "resolved" "https://registry.npmjs.org/ssf/-/ssf-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "frac" "~1.1.2"

"stack-chain@^1.3.7":
  "integrity" "sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug=="
  "resolved" "https://registry.npmjs.org/stack-chain/-/stack-chain-1.3.7.tgz"
  "version" "1.3.7"

"stack-utils@^2.0.6":
  "integrity" "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ=="
  "resolved" "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"statuses@^2.0.1":
  "integrity" "sha512-DvEy55V3DB7uknRo+4iOGT5fP1slR8wQohVdknigZPMpMstaKJQWhwiYBACJE3Ul2pTnATihhBYnRhZQHGBiRw=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-2.0.2.tgz"
  "version" "2.0.2"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"streamsearch@^1.1.0":
  "integrity" "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg=="
  "resolved" "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.1.1":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@^1.3.0":
  "integrity" "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-format@^2.0.0":
  "integrity" "sha512-bbEs3scLeYNXLecRRuk6uJxdXUSj6le/8rNPHChIJTn2V79aXVTR1EH2OH5zLKKoz0V02fOUKZZcw01pLUShZA=="
  "resolved" "https://registry.npmjs.org/string-format/-/string-format-2.0.0.tgz"
  "version" "2.0.0"

"string-length@^4.0.2":
  "integrity" "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ=="
  "resolved" "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "char-regex" "^1.0.2"
    "strip-ansi" "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^5.0.1", "string-width@^5.1.2":
  "integrity" "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "eastasianwidth" "^0.2.0"
    "emoji-regex" "^9.2.2"
    "strip-ansi" "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.1":
  "integrity" "sha512-gmBGslpoQJtgnMAvOVqGZpEz9dyoKTCzy2nfz/n8aIFhN/jCE/rCmcxabB6jOOHV+0WNnylOxaxBQPSvcWklhA=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.2.tgz"
  "version" "7.1.2"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-bom@^3.0.0":
  "integrity" "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-bom@^4.0.0":
  "integrity" "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w=="
  "resolved" "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"strnum@^2.1.0":
  "integrity" "sha512-7ZvoFTiCnGxBtDqJ//Cu6fWtZtc7Y3x+QOirG15wztbdngGSkht27o2pyGWrVy0b4WAy3jbKmnoK6g5VlVNUUw=="
  "resolved" "https://registry.npmjs.org/strnum/-/strnum-2.1.1.tgz"
  "version" "2.1.1"

"strtok3@^10.2.2":
  "integrity" "sha512-KIy5nylvC5le1OdaaoCJ07L+8iQzJHGH6pWDuzS+d07Cu7n1MZ2x26P8ZKIWfbK02+XIL8Mp4RkWeqdUCrDMfg=="
  "resolved" "https://registry.npmjs.org/strtok3/-/strtok3-10.3.4.tgz"
  "version" "10.3.4"
  dependencies:
    "@tokenizer/token" "^0.3.0"

"superagent@^10.2.3":
  "integrity" "sha512-y/hkYGeXAj7wUMjxRbB21g/l6aAEituGXM9Rwl4o20+SX3e8YOSV6BxFXl+dL3Uk0mjSL3kCbNkwURm8/gEDig=="
  "resolved" "https://registry.npmjs.org/superagent/-/superagent-10.2.3.tgz"
  "version" "10.2.3"
  dependencies:
    "component-emitter" "^1.3.1"
    "cookiejar" "^2.1.4"
    "debug" "^4.3.7"
    "fast-safe-stringify" "^2.1.1"
    "form-data" "^4.0.4"
    "formidable" "^3.5.4"
    "methods" "^1.1.2"
    "mime" "2.6.0"
    "qs" "^6.11.2"

"supertest@^7.1.1":
  "integrity" "sha512-tjLPs7dVyqgItVFirHYqe2T+MfWc2VOBQ8QFKKbWTA3PU7liZR8zoSpAi/C1k1ilm9RsXIKYf197oap9wXGVYg=="
  "resolved" "https://registry.npmjs.org/supertest/-/supertest-7.1.4.tgz"
  "version" "7.1.4"
  dependencies:
    "methods" "^1.1.2"
    "superagent" "^10.2.3"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.1.1":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"swagger-ui-dist@5.21.0":
  "integrity" "sha512-E0K3AB6HvQd8yQNSMR7eE5bk+323AUxjtCz/4ZNKiahOlPhPJxqn3UPIGs00cyY/dhrTDJ61L7C/a8u6zhGrZg=="
  "resolved" "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-5.21.0.tgz"
  "version" "5.21.0"
  dependencies:
    "@scarf/scarf" "=1.4.0"

"symbol-observable@4.0.0":
  "integrity" "sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ=="
  "resolved" "https://registry.npmjs.org/symbol-observable/-/symbol-observable-4.0.0.tgz"
  "version" "4.0.0"

"synckit@^0.11.7", "synckit@^0.11.8":
  "integrity" "sha512-MeQTA1r0litLUf0Rp/iisCaL8761lKAZHaimlbGK4j0HysC4PLfqygQj9srcs0m2RdtDYnF8UuYyKpbjHYp7Jw=="
  "resolved" "https://registry.npmjs.org/synckit/-/synckit-0.11.11.tgz"
  "version" "0.11.11"
  dependencies:
    "@pkgr/core" "^0.2.9"

"tapable@^2.1.1", "tapable@^2.2.0", "tapable@^2.2.1":
  "integrity" "sha512-g9ljZiwki/LfxmQADO3dEY1CbpmXT5Hm2fJ+QaGKwSXUylMybePR7/67YW7jOrrvjEgL1Fmz5kzyAjWVWLlucg=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.3.0.tgz"
  "version" "2.3.0"

"tarn@^3.0.2":
  "integrity" "sha512-51LAVKUSZSVfI05vjPESNc5vwqqZpbXCsU+/+wxlOrUjk2SnFTt97v9ZgQrD4YmxYW1Px6w2KjaDitCfkvgxMQ=="
  "resolved" "https://registry.npmjs.org/tarn/-/tarn-3.0.2.tgz"
  "version" "3.0.2"

"tedious@^18.2.1":
  "integrity" "sha512-9AvErXXQTd6l7TDd5EmM+nxbOGyhnmdbp/8c3pw+tjaiSXW9usME90ET/CRG1LN1Y9tPMtz/p83z4Q97B4DDpw=="
  "resolved" "https://registry.npmjs.org/tedious/-/tedious-18.6.1.tgz"
  "version" "18.6.1"
  dependencies:
    "@azure/core-auth" "^1.7.2"
    "@azure/identity" "^4.2.1"
    "@azure/keyvault-keys" "^4.4.0"
    "@js-joda/core" "^5.6.1"
    "@types/node" ">=18"
    "bl" "^6.0.11"
    "iconv-lite" "^0.6.3"
    "js-md4" "^0.3.2"
    "native-duplexpair" "^1.0.0"
    "sprintf-js" "^1.1.3"

"terser-webpack-plugin@^5.3.11":
  "integrity" "sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw=="
  "resolved" "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz"
  "version" "5.3.14"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    "jest-worker" "^27.4.5"
    "schema-utils" "^4.3.0"
    "serialize-javascript" "^6.0.2"
    "terser" "^5.31.1"

"terser@^5.31.1":
  "integrity" "sha512-nIVck8DK+GM/0Frwd+nIhZ84pR/BX7rmXMfYwyg+Sri5oGVE99/E3KvXqpC2xHFxyqXyGHTKBSioxxplrO4I4w=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.44.0.tgz"
  "version" "5.44.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    "acorn" "^8.15.0"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"test-exclude@^6.0.0":
  "integrity" "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w=="
  "resolved" "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"tmpl@1.0.5":
  "integrity" "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw=="
  "resolved" "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  "version" "1.0.5"

"to-buffer@^1.2.0":
  "integrity" "sha512-db0E3UJjcFhpDhAF4tLo03oli3pwl3dbnzXOUIlRKrp+ldk/VUxzpWYZENsw2SZiuBjHAk7DfB0VU7NKdpb6sw=="
  "resolved" "https://registry.npmjs.org/to-buffer/-/to-buffer-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "isarray" "^2.0.5"
    "safe-buffer" "^5.2.1"
    "typed-array-buffer" "^1.0.3"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"token-types@^6.0.0":
  "integrity" "sha512-kh9LVIWH5CnL63Ipf0jhlBIy0UsrMj/NJDfpsy1SqOXlLKEVyXXYrnFxFT1yOOYVGBSApeVnjPw/sBz5BfEjAQ=="
  "resolved" "https://registry.npmjs.org/token-types/-/token-types-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "@borewit/text-codec" "^0.1.0"
    "@tokenizer/token" "^0.3.0"
    "ieee754" "^1.2.1"

"tree-kill@1.2.2":
  "integrity" "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A=="
  "resolved" "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz"
  "version" "1.2.2"

"ts-api-utils@^2.1.0":
  "integrity" "sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ=="
  "resolved" "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz"
  "version" "2.1.0"

"ts-jest@^29.4.0":
  "integrity" "sha512-HO3GyiWn2qvTQA4kTgjDcXiMwYQt68a1Y8+JuLRVpdIzm+UOLSHgl/XqR4c6nzJkq5rOkjc02O2I7P7l/Yof0Q=="
  "resolved" "https://registry.npmjs.org/ts-jest/-/ts-jest-29.4.5.tgz"
  "version" "29.4.5"
  dependencies:
    "bs-logger" "^0.2.6"
    "fast-json-stable-stringify" "^2.1.0"
    "handlebars" "^4.7.8"
    "json5" "^2.2.3"
    "lodash.memoize" "^4.1.2"
    "make-error" "^1.3.6"
    "semver" "^7.7.3"
    "type-fest" "^4.41.0"
    "yargs-parser" "^21.1.1"

"ts-loader@^9.5.2":
  "integrity" "sha512-nCz0rEwunlTZiy6rXFByQU1kVVpCIgUpc/psFiKVrUwrizdnIbRFu8w7bxhUF0X613DYwT4XzrZHpVyMe758hQ=="
  "resolved" "https://registry.npmjs.org/ts-loader/-/ts-loader-9.5.4.tgz"
  "version" "9.5.4"
  dependencies:
    "chalk" "^4.1.0"
    "enhanced-resolve" "^5.0.0"
    "micromatch" "^4.0.0"
    "semver" "^7.3.4"
    "source-map" "^0.7.4"

"ts-node@^10.7.0", "ts-node@^10.9.1", "ts-node@>=9.0.0":
  "integrity" "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ=="
  "resolved" "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz"
  "version" "10.9.2"
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    "acorn" "^8.4.1"
    "acorn-walk" "^8.1.1"
    "arg" "^4.1.0"
    "create-require" "^1.1.0"
    "diff" "^4.0.1"
    "make-error" "^1.1.1"
    "v8-compile-cache-lib" "^3.0.1"
    "yn" "3.1.1"

"tsconfig-paths-webpack-plugin@4.2.0":
  "integrity" "sha512-zbem3rfRS8BgeNK50Zz5SIQgXzLafiHjOwUAvk/38/o1jHn/V5QAgVUcz884or7WYcPaH3N2CIfUc2u0ul7UcA=="
  "resolved" "https://registry.npmjs.org/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "chalk" "^4.1.0"
    "enhanced-resolve" "^5.7.0"
    "tapable" "^2.2.1"
    "tsconfig-paths" "^4.1.2"

"tsconfig-paths@^4.1.2", "tsconfig-paths@^4.2.0", "tsconfig-paths@4.2.0":
  "integrity" "sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg=="
  "resolved" "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "json5" "^2.2.2"
    "minimist" "^1.2.6"
    "strip-bom" "^3.0.0"

"tslib@^2.1.0", "tslib@^2.2.0", "tslib@^2.4.0", "tslib@^2.6.2", "tslib@^2.8.1", "tslib@2.8.1":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-detect@4.0.8":
  "integrity" "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="
  "resolved" "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^4.41.0":
  "integrity" "sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-4.41.0.tgz"
  "version" "4.41.0"

"type-is@^1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"type-is@^2.0.0", "type-is@^2.0.1":
  "integrity" "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "content-type" "^1.0.5"
    "media-typer" "^1.1.0"
    "mime-types" "^3.0.0"

"typed-array-buffer@^1.0.3":
  "integrity" "sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw=="
  "resolved" "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bound" "^1.0.3"
    "es-errors" "^1.3.0"
    "is-typed-array" "^1.1.14"

"typedarray@^0.0.6":
  "integrity" "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="
  "resolved" "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"typeorm-transactional@^0.5.0", "typeorm-transactional@>=0.5.0":
  "integrity" "sha512-53/CwnXpOIJnWU3oVCNbhHB95FwciKSGbY+m/Hw4e2dBM2c4toiOHwf4pmk83Ne7guznmDgVr/5IUfbp+JTPCg=="
  "resolved" "https://registry.npmjs.org/typeorm-transactional/-/typeorm-transactional-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@types/cls-hooked" "^4.3.3"
    "cls-hooked" "^4.2.2"
    "semver" "^7.5.1"

"typeorm@^0.3.0", "typeorm@>= 0.2.8", "typeorm@>=0.3.0", "typeorm@0.3.25":
  "integrity" "sha512-fTKDFzWXKwAaBdEMU4k661seZewbNYET4r1J/z3Jwf+eAvlzMVpTLKAVcAzg75WwQk7GDmtsmkZ5MfkmXCiFWg=="
  "resolved" "https://registry.npmjs.org/typeorm/-/typeorm-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@sqltools/formatter" "^1.2.5"
    "ansis" "^3.17.0"
    "app-root-path" "^3.1.0"
    "buffer" "^6.0.3"
    "dayjs" "^1.11.13"
    "debug" "^4.4.0"
    "dedent" "^1.6.0"
    "dotenv" "^16.4.7"
    "glob" "^10.4.5"
    "sha.js" "^2.4.11"
    "sql-highlight" "^6.0.0"
    "tslib" "^2.8.1"
    "uuid" "^11.1.0"
    "yargs" "^17.7.2"

"typescript@*", "typescript@^5.8.3", "typescript@>=2.7", "typescript@>=4.3 <6", "typescript@>=4.8.2", "typescript@>=4.8.4", "typescript@>=4.8.4 <6.0.0", "typescript@>=4.9.5", "typescript@>3.6.0":
  "integrity" "sha512-jl1vZzPDinLr9eUt3J/t7V6FgNEw9QjvBPdysz9KfQDD41fQrC2Y4vKQdiaUpFT4bXlb1RHhLpp8wtm6M5TgSw=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-5.9.3.tgz"
  "version" "5.9.3"

"typescript@5.8.3":
  "integrity" "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz"
  "version" "5.8.3"

"uglify-js@^3.1.4":
  "integrity" "sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ=="
  "resolved" "https://registry.npmjs.org/uglify-js/-/uglify-js-3.19.3.tgz"
  "version" "3.19.3"

"uid@2.0.2":
  "integrity" "sha512-u3xV3X7uzvi5b1MncmZo3i2Aw222Zk1keqLA1YkHldREkAhAqi65wuPfe7lHx8H/Wzy+8CE7S7uS3jekIM5s8g=="
  "resolved" "https://registry.npmjs.org/uid/-/uid-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@lukeed/csprng" "^1.0.0"

"uid2@0.0.x":
  "integrity" "sha512-IevTus0SbGwQzYh3+fRsAMTVVPOoIVufzacXcHPmdlle1jUpq7BRL+mw3dgeLanvGZdwwbWhRV6XrcFNdBmjWA=="
  "resolved" "https://registry.npmjs.org/uid2/-/uid2-0.0.4.tgz"
  "version" "0.0.4"

"uint8array-extras@^1.4.0":
  "integrity" "sha512-rvKSBiC5zqCCiDZ9kAOszZcDvdAHwwIKJG33Ykj43OKcWsnmcBRL09YTU4nOeHZ8Y2a7l1MgTd08SBe9A8Qj6A=="
  "resolved" "https://registry.npmjs.org/uint8array-extras/-/uint8array-extras-1.5.0.tgz"
  "version" "1.5.0"

"underscore@^1.13.1":
  "integrity" "sha512-GMXzWtsc57XAtguZgaQViUOzs0KTkk8ojr3/xAxXLITqf/3EMwxC0inyETfDFjH/Krbhuep0HNbbjI9i/q3F3g=="
  "resolved" "https://registry.npmjs.org/underscore/-/underscore-1.13.7.tgz"
  "version" "1.13.7"

"undici-types@~7.14.0":
  "integrity" "sha512-QQiYxHuyZ9gQUIrmPo3IA+hUl4KYk8uSA7cHrcKd/l3p1OTpZcM0Tbp9x7FAtXdAYhlasd60ncPpgu6ihG6TOA=="
  "resolved" "https://registry.npmjs.org/undici-types/-/undici-types-7.14.0.tgz"
  "version" "7.14.0"

"universalify@^2.0.0":
  "integrity" "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  "version" "2.0.1"

"unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unrs-resolver@^1.7.11":
  "integrity" "sha512-bSjt9pjaEBnNiGgc9rUiHGKv5l4/TGzDmYw3RhnkJGtLhbnnA/5qJj7x3dNDCRx/PJxu774LlH8lCOlB4hEfKg=="
  "resolved" "https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "napi-postinstall" "^0.3.0"
  optionalDependencies:
    "@unrs/resolver-binding-android-arm-eabi" "1.11.1"
    "@unrs/resolver-binding-android-arm64" "1.11.1"
    "@unrs/resolver-binding-darwin-arm64" "1.11.1"
    "@unrs/resolver-binding-darwin-x64" "1.11.1"
    "@unrs/resolver-binding-freebsd-x64" "1.11.1"
    "@unrs/resolver-binding-linux-arm-gnueabihf" "1.11.1"
    "@unrs/resolver-binding-linux-arm-musleabihf" "1.11.1"
    "@unrs/resolver-binding-linux-arm64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-arm64-musl" "1.11.1"
    "@unrs/resolver-binding-linux-ppc64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-riscv64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-riscv64-musl" "1.11.1"
    "@unrs/resolver-binding-linux-s390x-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-x64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-x64-musl" "1.11.1"
    "@unrs/resolver-binding-wasm32-wasi" "1.11.1"
    "@unrs/resolver-binding-win32-arm64-msvc" "1.11.1"
    "@unrs/resolver-binding-win32-ia32-msvc" "1.11.1"
    "@unrs/resolver-binding-win32-x64-msvc" "1.11.1"

"update-browserslist-db@^1.1.3":
  "integrity" "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"url@0.10.3":
  "integrity" "sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ=="
  "resolved" "https://registry.npmjs.org/url/-/url-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util@^0.12.4":
  "integrity" "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA=="
  "resolved" "https://registry.npmjs.org/util/-/util-0.12.5.tgz"
  "version" "0.12.5"
  dependencies:
    "inherits" "^2.0.3"
    "is-arguments" "^1.0.4"
    "is-generator-function" "^1.0.7"
    "is-typed-array" "^1.1.3"
    "which-typed-array" "^1.1.2"

"utils-merge@^1.0.1", "utils-merge@1.x.x":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^11.1.0":
  "integrity" "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz"
  "version" "11.1.0"

"uuid@^8.3.0":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"uuid@8.0.0":
  "integrity" "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz"
  "version" "8.0.0"

"v8-compile-cache-lib@^3.0.1":
  "integrity" "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg=="
  "resolved" "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  "version" "3.0.1"

"v8-to-istanbul@^9.0.1":
  "integrity" "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA=="
  "resolved" "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz"
  "version" "9.3.0"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^2.0.0"

"validator@^13.9.0":
  "integrity" "sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A=="
  "resolved" "https://registry.npmjs.org/validator/-/validator-13.15.15.tgz"
  "version" "13.15.15"

"vary@^1", "vary@^1.1.2":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"walker@^1.0.8":
  "integrity" "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ=="
  "resolved" "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "makeerror" "1.0.12"

"watchpack@^2.4.1":
  "integrity" "sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-2.4.4.tgz"
  "version" "2.4.4"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"wcwidth@^1.0.1":
  "integrity" "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="
  "resolved" "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webpack-node-externals@3.0.0":
  "integrity" "sha512-LnL6Z3GGDPht/AigwRh2dvL9PQPFQ8skEpVrWZXLWBYmqcaojHNN0onvHzie6rq7EWKrrBfPYqNEzTJgiwEQDQ=="
  "resolved" "https://registry.npmjs.org/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz"
  "version" "3.0.0"

"webpack-sources@^3.3.3":
  "integrity" "sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.3.tgz"
  "version" "3.3.3"

"webpack@^5.0.0", "webpack@^5.1.0", "webpack@^5.11.0", "webpack@5.100.2":
  "integrity" "sha512-QaNKAvGCDRh3wW1dsDjeMdDXwZm2vqq3zn6Pvq4rHOEOGSaUMgOOjG2Y9ZbIGzpfkJk9ZYTHpDqgDfeBDcnLaw=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-5.100.2.tgz"
  "version" "5.100.2"
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.8"
    "@types/json-schema" "^7.0.15"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    "acorn" "^8.15.0"
    "acorn-import-phases" "^1.0.3"
    "browserslist" "^4.24.0"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.17.2"
    "es-module-lexer" "^1.2.1"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.11"
    "json-parse-even-better-errors" "^2.3.1"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^4.3.2"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.3.11"
    "watchpack" "^2.4.1"
    "webpack-sources" "^3.3.3"

"which-module@^2.0.0":
  "integrity" "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ=="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz"
  "version" "2.0.1"

"which-typed-array@^1.1.16", "which-typed-array@^1.1.2":
  "integrity" "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw=="
  "resolved" "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz"
  "version" "1.1.19"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.4"
    "for-each" "^0.3.5"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-tostringtag" "^1.0.2"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wmf@~1.0.1":
  "integrity" "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="
  "resolved" "https://registry.npmjs.org/wmf/-/wmf-1.0.2.tgz"
  "version" "1.0.2"

"word-wrap@^1.2.5":
  "integrity" "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  "version" "1.2.5"

"word@~0.3.0":
  "integrity" "sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA=="
  "resolved" "https://registry.npmjs.org/word/-/word-0.3.0.tgz"
  "version" "0.3.0"

"wordwrap@^1.0.0":
  "integrity" "sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q=="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz"
  "version" "1.0.0"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^8.1.0":
  "integrity" "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "ansi-styles" "^6.1.0"
    "string-width" "^5.0.1"
    "strip-ansi" "^7.0.1"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^5.0.1":
  "integrity" "sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "imurmurhash" "^0.1.4"
    "signal-exit" "^4.0.1"

"ws@~8.17.1":
  "integrity" "sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz"
  "version" "8.17.1"

"wsl-utils@^0.1.0":
  "integrity" "sha512-h3Fbisa2nKGPxCpm89Hk33lBLsnaGBvctQopaBSOW/uIs6FTe1ATyAnKFJrzVs9vpGdsTe73WF3V4lIsk4Gacw=="
  "resolved" "https://registry.npmjs.org/wsl-utils/-/wsl-utils-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "is-wsl" "^3.1.0"

"xlsx@^0.18.5":
  "integrity" "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ=="
  "resolved" "https://registry.npmjs.org/xlsx/-/xlsx-0.18.5.tgz"
  "version" "0.18.5"
  dependencies:
    "adler-32" "~1.3.0"
    "cfb" "~1.2.1"
    "codepage" "~1.15.0"
    "crc-32" "~1.2.1"
    "ssf" "~0.11.2"
    "wmf" "~1.0.1"
    "word" "~0.3.0"

"xml2js@0.6.2":
  "integrity" "sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA=="
  "resolved" "https://registry.npmjs.org/xml2js/-/xml2js-0.6.2.tgz"
  "version" "0.6.2"
  dependencies:
    "sax" ">=0.6.0"
    "xmlbuilder" "~11.0.0"

"xmlbuilder@^10.0.0":
  "integrity" "sha512-OyzrcFLL/nb6fMGHbiRDuPup9ljBycsdCypwuyg5AAHvyWzGfChJpCXMG88AGTIMFhGZ9RccFN1e6lhg3hkwKg=="
  "resolved" "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-10.1.1.tgz"
  "version" "10.1.1"

"xmlbuilder@~11.0.0":
  "integrity" "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA=="
  "resolved" "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  "version" "11.0.1"

"xtend@^4.0.0", "xtend@^4.0.2":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yaml@^2.2.1":
  "integrity" "sha512-lcYcMxX2PO9XMGvAJkJ3OsNMw+/7FKes7/hgerGUYWIoWu5j/+YQqcZr5JnPZWzOsEBgMbSbiSTn/dv/69Mkpw=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-2.8.1.tgz"
  "version" "2.8.1"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^21.1.1", "yargs-parser@21.1.1":
  "integrity" "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  "version" "21.1.1"

"yargs@^15.3.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^17.7.2":
  "integrity" "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  "version" "17.7.2"
  dependencies:
    "cliui" "^8.0.1"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.3"
    "y18n" "^5.0.5"
    "yargs-parser" "^21.1.1"

"yn@3.1.1":
  "integrity" "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q=="
  "resolved" "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  "version" "3.1.1"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"yoctocolors-cjs@^2.1.2":
  "integrity" "sha512-U/PBtDf35ff0D8X8D0jfdzHYEPFxAI7jJlxZXwCSez5M3190m+QobIfh+sWDWSHMCWWJN2AWamkegn6vr6YBTw=="
  "resolved" "https://registry.npmjs.org/yoctocolors-cjs/-/yoctocolors-cjs-2.1.3.tgz"
  "version" "2.1.3"
