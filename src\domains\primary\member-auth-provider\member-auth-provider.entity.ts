import { Entity, Column, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMember } from '~/common/enums';
import { ApiProperty } from '@nestjs/swagger';

@Entity('member_auth_provider')
@Index(['provider', 'providerId'], { unique: true }) // đảm bảo không trùng
export class MemberAuthProviderEntity extends PrimaryBaseEntity {
  @ApiProperty({ example: '<EMAIL>' })
  @Column({ type: 'uuid' })
  memberId: string; // Foreign key tới bảng member

  @ApiProperty({ example: NSMember.EAuthProviderType.EMAIL })
  @Column({ type: 'varchar', length: 20 })
  provider: NSMember.EAuthProviderType;

  @ApiProperty()
  @Column({ type: 'varchar', length: 100 })
  providerId: string; // email, googleId, facebookId, appleSub, v.v.
}
