import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { Column, Entity, Index } from 'typeorm';
import { NSTask } from '~/common/enums/task.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('tasks')
export class TaskEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Tiêu đề của task' })
    @IsString()
    @IsNotEmpty()
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiPropertyOptional({ description: '<PERSON>ô tả chi tiết công việc' })
    @IsString()
    @IsOptional()
    @Column({ type: 'text', nullable: true })
    description: string;

    @ApiProperty({ description: 'Trạng thái của task', enum: NSTask.EStatus })
    @IsEnum(NSTask.EStatus)
    @Index()
    @Column({
        type: 'enum',
        enum: NSTask.EStatus,
        default: NSTask.EStatus.TO_DO,
    })
    status: NSTask.EStatus;

    @ApiProperty({ description: 'Loại task theo chức năng CRM', enum: NSTask.EType })
    @IsEnum(NSTask.EType)
    @Index()
    @Column({
        type: 'enum',
        enum: NSTask.EType,
        default: NSTask.EType.OTHER,
    })
    type: NSTask.EType;

    @ApiProperty({ description: 'Mức độ ưu tiên', enum: NSTask.EPriority })
    @IsEnum(NSTask.EPriority)
    @Index()
    @Column({
        type: 'enum',
        enum: NSTask.EPriority,
        default: NSTask.EPriority.MEDIUM,
    })
    priority: NSTask.EPriority;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu thực hiện' })
    @IsDateString()
    @IsOptional()
    @Column({ type: 'timestamptz', nullable: true })
    startDate: Date;

    @ApiPropertyOptional({ description: 'Hạn chót hoàn thành (Deadline)' })
    @IsDateString()
    @IsOptional()
    @Index()
    @Column({ type: 'timestamptz', nullable: true })
    deadline: Date;

    @ApiPropertyOptional({ description: 'ID người được giao việc (Member ID)' })
    @IsUUID(4)
    @IsOptional()
    @Index()
    @Column({ type: 'uuid', nullable: true })
    assigneeId: string;
}
