import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUUID,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSNotification } from '~/common/enums/notification.enum';

export class CreateNotificationReq {
    @ApiProperty({ enum: NSNotification.EType })
    @IsEnum(NSNotification.EType)
    type: NSNotification.EType;

    @ApiProperty({ enum: NSNotification.ESeverity })
    @IsEnum(NSNotification.ESeverity)
    severity: NSNotification.ESeverity;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    title: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    content: string;

    @ApiProperty({ enum: NSNotification.ESource })
    @IsEnum(NSNotification.ESource)
    source: NSNotification.ESource;

    @ApiPropertyOptional()
    @IsOptional()
    @IsDateString()
    scheduleDate?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    sendInApp?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    sendEmail?: boolean;

    @ApiProperty({ enum: NSNotification.ERecipientType })
    @IsEnum(NSNotification.ERecipientType)
    recipientType: NSNotification.ERecipientType;

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    memberIds?: string[];

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    roleIds?: string[];

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    departmentIds?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    linkPath?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    linkDrawer?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    linkId?: string;
}

export class MarkReadReq {
    @ApiProperty()
    @IsUUID()
    @IsNotEmpty()
    receiptId: string;
}

export class SetActionReq {
    @ApiProperty()
    @IsUUID()
    @IsNotEmpty()
    receiptId: string;
}

export class ListNotificationQuery extends PageRequest {
    @ApiPropertyOptional({ enum: NSNotification.EStatus })
    @IsOptional()
    @IsEnum(NSNotification.EStatus)
    status?: NSNotification.EStatus;

    @ApiPropertyOptional({ enum: NSNotification.EType })
    @IsOptional()
    @IsEnum(NSNotification.EType)
    type?: NSNotification.EType;

    @ApiPropertyOptional({ enum: NSNotification.ESeverity })
    @IsOptional()
    @IsEnum(NSNotification.ESeverity)
    severity?: NSNotification.ESeverity;

    @ApiPropertyOptional({ enum: NSNotification.ESource })
    @IsOptional()
    @IsEnum(NSNotification.ESource)
    source?: NSNotification.ESource;
}

export class ListReceiptsQuery extends PageRequest {
    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    notificationId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    isRead?: string;

    @ApiPropertyOptional({ enum: NSNotification.EDeliveryStatus })
    @IsOptional()
    @IsEnum(NSNotification.EDeliveryStatus)
    deliveryStatus?: NSNotification.EDeliveryStatus;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    memberId?: string;
}

export class ListMyReceiptsQuery extends PageRequest {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    isRead?: string;

    @ApiPropertyOptional({ enum: NSNotification.EDeliveryStatus })
    @IsOptional()
    @IsEnum(NSNotification.EDeliveryStatus)
    deliveryStatus?: NSNotification.EDeliveryStatus;
}
