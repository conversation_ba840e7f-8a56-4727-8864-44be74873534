import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

// T<PERSON>i liệu khách hàng
@Entity('customer_document')
export class CustomerDocumentEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ nullable: true, type: 'varchar', length: 255 })
    name: string;

    @ApiProperty()
    @Column({ nullable: true, type: 'text' })
    description: string;

    @ApiProperty()
    @Column({ nullable: true, type: 'text' })
    url: string;

    @ApiProperty({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111', description: 'Media ID' })
    @Column({ type: 'uuid', nullable: true })
    mediaId?: string;

    @ApiProperty({ example: '10DAY.png', description: 'Tên file ảnh' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    fileName: string;

    @ApiProperty()
    @Column({ nullable: true, type: 'varchar', length: 50 })
    status: string;

    @ApiProperty()
    @Column('uuid', { nullable: true })
    customerId: string;
}
