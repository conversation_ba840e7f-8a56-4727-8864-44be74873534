import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsOptional,
    IsString,
    IsUUID,
    MaxLength,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class CreateCustomerContactDto {
    @ApiProperty({ example: 'Thăm hỏi khách hàng định kỳ' })
    @IsString()
    @MaxLength(255)
    @IsOptional()
    title: string;

    @ApiPropertyOptional({ description: 'Mã thăm hỏi (tự động generate nếu không có)', example: 'TH0000' })
    @IsOptional()
    @IsString()
    @MaxLength(32)
    code?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Phân loại chuyến thăm' })
    @IsString()
    @IsOptional()
    type?: string;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsOptional()
    @IsString()
    status?: string;

    @ApiProperty()
    @IsUUID(4)
    customerId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID(4)
    addressId?: string;

    @ApiProperty()
    @IsString()
    @MaxLength(255)
    address: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    communeCode?: string;

    @ApiPropertyOptional({ description: 'Người giám sát' })
    @IsOptional()
    @IsUUID(4)
    supervisorMemberId?: string;

    @ApiPropertyOptional({ description: 'Danh sách nhân viên được phân công' })
    @IsOptional()
    @IsArray()
    @IsUUID(4, { each: true })
    assignedMemberIds?: string[];

    @ApiPropertyOptional({ description: 'Số điện thoại' })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiPropertyOptional({ description: 'Email' })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: 'Liên hệ khách hàng' })
    @IsOptional()
    @IsUUID(4)
    customerContactId?: string;

    @ApiPropertyOptional({ description: 'Model' })
    @IsOptional()
    @IsString()
    model?: string;

    @ApiPropertyOptional({ description: 'Ngày dự kiến' })
    @IsOptional()
    @IsDateString()
    dueDate?: string;

    @ApiPropertyOptional({ description: 'Ngày thực tế' })
    @IsOptional()
    @IsDateString()
    actualDate?: string;

    @ApiPropertyOptional({ description: 'Yêu cầu check-in' })
    @IsOptional()
    @IsBoolean()
    requireCheckin?: boolean;

    @ApiPropertyOptional({ description: 'Ngày check-in' })
    @IsOptional()
    @IsDateString()
    checkInDate?: string;

    @ApiPropertyOptional({ description: 'Thời gian check-out' })
    @IsOptional()
    @IsDateString()
    checkOutTime?: string;

    @ApiPropertyOptional({ description: 'Loại phân công' })
    @IsOptional()
    @IsString()
    assignmentType?: string;
}

export class UpdateCustomerContactDto extends CreateCustomerContactDto {
    @ApiProperty()
    @IsUUID(4)
    @IsOptional()
    id: string;
}

export class ListCustomerContactDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Tìm kiếm theo tiêu đề' })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional({ description: 'Tìm kiếm theo mã thăm hỏi' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ description: 'Lọc theo phân loại' })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: 'Lọc theo trạng thái' })
    @IsOptional()
    @IsString()
    status?: string;

    @ApiPropertyOptional({ description: 'Lọc theo khách hàng' })
    @IsOptional()
    @IsUUID(4)
    customerId?: string;

    @ApiPropertyOptional({ description: 'Lọc theo người giám sát' })
    @IsOptional()
    @IsUUID(4)
    supervisorMemberId?: string;

    @ApiPropertyOptional({ description: 'Lọc theo nhân viên được phân công' })
    @IsOptional()
    @IsUUID(4)
    assignedMemberId?: string;

    @ApiPropertyOptional({ description: 'Ngày dự kiến từ' })
    @IsOptional()
    @IsDateString()
    dueDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày dự kiến đến' })
    @IsOptional()
    @IsDateString()
    dueDateTo?: string;

    // Actual date đã được loại bỏ theo entity mới

    @ApiPropertyOptional({ description: 'Ngày tạo từ' })
    @IsOptional()
    @IsDateString()
    createdDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày tạo đến' })
    @IsOptional()
    @IsDateString()
    createdDateTo?: string;
}

export class DetailCustomerContactDto {
    @ApiProperty()
    @IsUUID(4)
    @IsOptional()
    id: string;
}

export class DeleteCustomerContactDto {
    @ApiProperty()
    @IsUUID(4)
    @IsOptional()
    id: string;
}

export class DeleteListCustomerContactDto {
    @ApiProperty({ type: [String] })
    @IsOptional()
    ids: string[];
}

export class UpdateStatusCustomerContactDto {
    @ApiProperty()
    @IsUUID(4)
    @IsOptional()
    id: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    status: string;
}
