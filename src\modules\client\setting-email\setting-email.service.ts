import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { EmailEntity } from '~/domains/primary/email/email.entity';
import { EmailRepo } from '~/domains/primary/email/email.repo';
import { CampaignRepo } from '~/domains/primary/marketing-campaign/campaign.repo';
import { clientSessionContext } from '../client-session.context';
import { UpdateEmailConfigsDto } from './dto/setting-email.dto';

@Injectable()
export class SettingEmailService {
    constructor(
        @InjectRepo(EmailRepo) private readonly emailRepo: EmailRepo,
        private readonly campaignRepo: CampaignRepo,
    ) {}

    /**
     * L<PERSON>y toàn bộ cấu hình email theo Tenant
     */
    async getEmailConfigs(): Promise<EmailEntity[]> {
        const { tenantId } = clientSessionContext;
        return this.emailRepo.find({
            where: { tenantId },
            order: { createdDate: 'DESC' },
        });
    }

    /**
     * Upsert danh sách cấu hình email theo Tenant
     */
    @DefTransaction()
    async updateMailConfigs(dto: UpdateEmailConfigsDto): Promise<{
        data: EmailEntity[];
        diff: { added: string[]; removed: string[]; unchanged: string[] };
    }> {
        const { tenantId } = clientSessionContext;
        const normalize = (e?: string) => (e ?? '').trim().toLowerCase();
        const validConfigs = Array.isArray(dto.configs)
            ? dto.configs.filter(
                  c => !!normalize(c.email) && !!normalize(c.host) && !!normalize(String(c.port)),
              )
            : [];
        const nextEmails = validConfigs.map(c => c.email);

        const existing = await this.emailRepo.find({ where: { tenantId } });
        const prevEmails = existing.map(c => c.email).filter(Boolean);
        const prevSet = new Set(prevEmails.map(normalize));
        const nextSet = new Set(nextEmails.map(normalize));

        const added = nextEmails.filter(e => !prevSet.has(normalize(e)));
        const removed = prevEmails.filter(e => !nextSet.has(normalize(e)));
        const unchanged = nextEmails.filter(e => prevSet.has(normalize(e)));

        // Upsert từng cấu hình
        const upserted: EmailEntity[] = [];
        for (const cfg of validConfigs) {
            const found = existing.find(x => normalize(x.email) === normalize(cfg.email));
            if (found) {
                Object.assign(found, {
                    host: cfg.host,
                    port: cfg.port,
                    username: cfg.username,
                    password: cfg.password,
                });
                upserted.push(await this.emailRepo.save(found));
            } else {
                upserted.push(
                    await this.emailRepo.save({
                        email: cfg.email,
                        host: cfg.host,
                        port: cfg.port,
                        username: cfg.username,
                        password: cfg.password,
                        tenantId,
                    } as EmailEntity),
                );
            }
        }

        // Xoá các cấu hình bị remove và chuẩn hoá campaign
        if (removed.length > 0) {
            const campaigns = await this.campaignRepo.find({
                where: { tenantId, status: NSCampaign.EStatus.NEW },
            });
            for (const email of removed) {
                // Xoá bản ghi cấu hình
                const toDelete = existing.find(x => normalize(x.email) === normalize(email));
                if (toDelete?.id) {
                    await this.emailRepo.delete(toDelete.id);
                }
                // Thay thế trên campaign.mailSend nếu đang dùng email bị xoá
                for (const campaign of campaigns) {
                    if (normalize(campaign.mailSend || '') === normalize(email)) {
                        campaign.mailSend = added[0] || null;
                        await this.campaignRepo.save(campaign);
                    }
                }
            }
        }

        return { data: upserted, diff: { added, removed, unchanged } };
    }
}
