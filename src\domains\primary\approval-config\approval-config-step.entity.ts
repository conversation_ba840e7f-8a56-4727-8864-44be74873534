import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm'
import { ApprovalConfigEntity } from '../approval-config/approval-config.entity'
import { NSApproval } from '~/common/enums/approval.enum'
import { PrimaryBaseEntity } from '../primary-base.entity'
import { ApprovalConfigStepApproverEntity } from './approval-config-step-approver.entity'

@Entity('approval_config_step')
@Index(['configId', 'order'], { unique: true }) // Đ<PERSON>m bảo thứ tự duyệt không bị trùng trong 1 config
export class ApprovalConfigStepEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  @Index()
  configId: string

  // Thứ tự duyệt: 1, 2, 3...
  @Column({ type: 'int' })
  order: number

  // Tiêu đề hiển thị: “Trưởng phòng duyệt”, “Trưởng nhó<PERSON> duyệ<PERSON>”, “<PERSON><PERSON><PERSON> sự duyệt”, ...
  @Column({ type: 'varchar', length: 255 })
  title: string

  // Kiểu duyệt: 1 người / ANY / ALL
  @Column({ type: 'enum', enum: NSApproval.ApprovalLevelType })
  approvalLevelType: NSApproval.ApprovalLevelType

  // Số lượng người tối thiểu cần duyệt (option, dùng cho ANY nâng cao)
  // Ví dụ: ANY, nhưng cần ít nhất 2 người duyệt => minApprovers = 2
  @Column({ type: 'int', nullable: true })
  minApprovers?: number

  // Ghi chú cho cấp duyệt (tùy chọn)
  @Column({ type: 'text', nullable: true })
  note?: string

  // Thời gian dự kiến để hoàn thành bước duyệt (tính bằng giờ)
  // Ví dụ: 24 = 24 giờ, 48 = 48 giờ
  @Column({ type: 'int', nullable: true })
  leadtime?: number

  @OneToMany(() => ApprovalConfigStepApproverEntity, (approver) => approver.step)
  approvers: ApprovalConfigStepApproverEntity[]

  @ManyToOne(() => ApprovalConfigEntity, (config) => config.steps, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'configId' })
  config: ApprovalConfigEntity
}
