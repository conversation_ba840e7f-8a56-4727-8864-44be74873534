export type MailConfig = {
    host: string;
    port: number;
    secure?: boolean;
    user: string;
    pass: string;
    from: string;
};

export interface IMailSender {
    sendMail(options: SendMailOptionsDto): Promise<void>;
}

export interface SendMailOptionsDto {
    templateName?: string;
    to: string | string[];
    subject: string;
    html: string;
    cc?: string | string[];
    bcc?: string | string[];
    attachments?: any[];
    from?: string;
}

export interface SendMailWithTemplateOptionsDto extends SendMailOptionsDto {
    templateName: string;
    data: Record<string, any>;
}
