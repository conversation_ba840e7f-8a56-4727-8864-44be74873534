import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import * as jwt from 'jsonwebtoken';
import { MemberEntity } from '~/domains/primary';

export class JwtPayload implements jwt.JwtPayload {
    @ApiPropertyOptional()
    iss?: string | undefined;
    @ApiPropertyOptional()
    sub?: string | undefined;
    @ApiPropertyOptional()
    aud?: string | string[] | undefined;
    @ApiPropertyOptional()
    exp?: number | undefined;
    @ApiPropertyOptional()
    nbf?: number | undefined;
    @ApiPropertyOptional()
    iat?: number | undefined;
    @ApiPropertyOptional()
    jti?: string | undefined;
}
export class MemberLoginReq extends JwtPayload {
    @ApiProperty()
    @IsNotEmpty()
    username: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;
}

export class ClientSessionDto {
    @ApiProperty()
    @IsNotEmpty()
    accessToken: string;

    @ApiProperty()
    @IsNotEmpty()
    refreshToken: string;

    @ApiProperty()
    @IsNotEmpty()
    member: MemberEntity;
}

// src/modules/auth/dto/user-info.dto.ts
export type AggregatedPerm = {
    canView: boolean;
    canCreate: boolean;
    canUpdate: boolean;
    canDelete: boolean;
    canApprove: boolean;
};

export type PermissionMap = Record<string /* resourceCode */, AggregatedPerm>;

export interface MenuItemDto {
    key: string; // unique key (thường = resourceCode hoặc routePath)
    label: string; // tên hiển thị
    path?: string | null; // routePath
    icon?: string | null; // nếu có
    children?: MenuItemDto[];
}

export interface UserInfoDto {
    id: string;
    fullName?: string | null;
    email?: string | null;
    avatar?: string | null;
    roleId?: string | null;
    roleCode?: string | null;

    // QUAN TRỌNG: FE dùng 2 trường này
    permissions: PermissionMap; // map resourceCode -> flags
    menus: MenuItemDto[]; // menu đã lọc theo canView
}

export class UpdateUserInfoReq {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    fullName?: string | null;

    @ApiPropertyOptional()
    @IsNotEmpty()
    @IsString()
    username: string;
}

export interface ChangePasswordReq {
    oldPassword: string;
    newPassword: string;
}
