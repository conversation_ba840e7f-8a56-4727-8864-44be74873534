import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { existsSync, readFileSync } from 'fs';
import Handlebars from 'handlebars';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { join } from 'path';
import { Between, ILike, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSQuote } from '~/common/enums';
import { NSApproval } from '~/common/enums/approval.enum';
import { NSSettingString } from '~/common/enums/setting-string.enum';
import { formatCurrency, formatDate } from '~/common/helpers/quotation.helper';
import {
    CatalogItemRepo,
    CustomerContactRepo,
    CustomerRepo,
    MediaRepo,
    MemberRepo,
    QuoteItemRepo,
    QuoteRepo,
} from '~/domains/primary';
import { CustomerAddressRepo } from '~/domains/primary/customer-address/customer-address.repo';
import { AlertCronService } from '../alert/alert-cron.service';
import { ApprovalRequestService } from '../approval/approval-request.service';
import { clientSessionContext } from '../client-session.context';
import { EventLogsService } from '../event-log/event-log.service';
import { MailSenderService } from '../mail/mail-sender.service';
import { SettingStringService } from '../setting-string/setting-string.service';
import { UploadService } from '../upload/upload.service';
import { ListQuoteDto, QuoteItemDto, SendQuoteEmailDto, UpdateStatusDto } from './dto';

@Injectable()
export class QuoteService {
    constructor(
        @InjectRepo(QuoteRepo) private quoteRepo: QuoteRepo,
        @InjectRepo(QuoteItemRepo) private quoteItemRepo: QuoteItemRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        @InjectRepo(CustomerContactRepo) private customerContactRepo: CustomerContactRepo,
        @InjectRepo(CustomerAddressRepo) private customerAddressRepo: CustomerAddressRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        @InjectRepo(MediaRepo) private mediaRepo: MediaRepo,

        private readonly catalogItemRepo: CatalogItemRepo,
        private readonly alertCronService: AlertCronService,
        private readonly eventLogsService: EventLogsService,
        private readonly uploadService: UploadService,
        private readonly approvalRequestService: ApprovalRequestService,
        private readonly mailSenderService: MailSenderService,
        private readonly settingStringService: SettingStringService,
    ) {}
    async list(dto: ListQuoteDto) {
        const { tenantId } = clientSessionContext;
        const where: any = {};

        // 🔍 Tìm theo thời gian tạo

        // 🔍 Số báo giá
        if (dto.quotationNumber) {
            where.quotationNumber = ILike(`%${dto.quotationNumber}%`);
        }

        // 🔍 Theo khách hàng
        if (dto.customerId) {
            where.customerId = dto.customerId;
        }

        // 🔍 Theo trạng thái
        if (dto.status) {
            where.status = dto.status;
        }

        // 🔍 Theo ngày tạo
        if (dto.createdFrom && dto.createdTo) {
            where.createdDate = Between(dto.createdFrom, dto.createdTo);
        }

        // 🔍 Theo ngày báo giá
        if (dto.quotationFrom && dto.quotationTo) {
            where.quotationDate = Between(dto.quotationFrom, dto.quotationTo);
        }

        if (dto.createdBy) {
            where.createdBy = dto.createdBy;
        }

        // 🔍 Theo giá trị báo giá
        if (dto.priceFrom && dto.priceTo) {
            where.totalAmount = Between(dto.priceFrom, dto.priceTo);
        }
        if (dto.priceFrom && !dto.priceTo) {
            where.totalAmount = MoreThanOrEqual(dto.priceFrom);
        }
        if (!dto.priceFrom && dto.priceTo) {
            where.totalAmount = LessThanOrEqual(dto.priceTo);
        }

        // 🔍 Theo hạn báo giá (ngày hết hạn) dựa trên createdDate + validityDays
        if (dto.expiryFrom && dto.expiryTo) {
            const quoteIds = await this.quoteRepo
                .createQueryBuilder('q')
                .select('q.id', 'id')
                .where(
                    `q."createdDate" + (q."validityDays" * INTERVAL '1 day') BETWEEN :expiryFrom AND :expiryTo`,
                    {
                        expiryFrom: dto.expiryFrom,
                        expiryTo: dto.expiryTo,
                    },
                )
                .getRawMany();

            where.id = In(quoteIds.length > 0 ? quoteIds.map(item => item.id) : []);
        }

        // 🔍 Nếu tìm theo tên khách hàng
        let customerIds: string[] | undefined = undefined;
        if (dto.customerName) {
            const customers = await this.customerRepo.find({
                where: { name: ILike(`%${dto.customerName}%`) },
                select: ['id'],
            });

            if (customers.length > 0) {
                customerIds = customers.map(c => c.id);
            } else {
                // không tìm thấy khách nào => return luôn
                return { data: [], total: 0 };
            }
        }

        // 📦 Query chính
        const res = await this.quoteRepo.findPagination(
            {
                where: {
                    ...where,
                    ...(customerIds ? { customerId: In(customerIds) } : {}),
                    tenantId,
                },
                order: { createdDate: 'DESC' },
            },
            {
                pageIndex: dto.pageIndex,
                pageSize: dto.pageSize,
            },
        );

        // 🔗 Gắn thêm dữ liệu liên quan
        const data = await Promise.all(
            res.data.map(async quote => {
                const [customer, quotationProducts, createdBy, contact] = await Promise.all([
                    this.customerRepo.findOneBy({ id: quote.customerId }),
                    this.quoteItemRepo.find({ where: { quoteId: quote.id } }),
                    this.memberRepo.findOne({ where: { id: quote.createdBy } }),
                    this.customerContactRepo.findOneBy({ id: quote.memberId }),
                ]);

                return {
                    ...quote,
                    customer,
                    customerName: customer?.name,
                    status: quote.status,
                    quotationProducts,
                    createdByName: createdBy?.fullName,
                    memberPhone: contact?.phone,
                };
            }),
        );
        // Enrich với thông tin approval cho từng Contract
        const dataWithApproval = await Promise.all(
            data.map(async quote => {
                const approvalInfo = await this.approvalRequestService.getApprovalInfoForEntity(
                    quote.id,
                    NSApproval.ApprovalBusinessType.QUOTATION,
                );
                return {
                    ...quote,
                    approval: approvalInfo,
                };
            }),
        );

        return {
            data: dataWithApproval,
            total: res.total,
        };
    }

    async detail(dto: QuoteItemDto) {
        const { tenantId } = clientSessionContext;
        const quote = await this.quoteRepo.findOneBy({ id: dto.id });

        if (!quote) {
            throw new BusinessException('Quote not found');
        }

        //tìm địa chỉ mặc định của khách hàng
        const customerAddress = await this.customerAddressRepo.findOne({
            where: { customerId: quote.customerId, isDefault: true, tenantId },
        });
        if (!customerAddress) {
            throw new BusinessException('Customer address not found');
        }

        const customer = await this.customerRepo.findOneBy({ id: quote.customerId });
        // member nội bộ
        const member = await this.memberRepo.findOne({ where: { id: quote.memberId } });
        const quoteItems = await this.quoteItemRepo.find({ where: { quoteId: dto.id } });

        const contact = await this.customerContactRepo.findOneBy({ id: quote.customerContactId });
        return {
            ...quote,
            quoteItems,
            customerAddress: `${customerAddress?.address}`,
            customerEmail: customer?.email,
            customerName: customer?.name,
            customerTaxCode: customer?.taxNumber,
            dueDate: quote?.dueDate,
            memberName: (member as any)?.fullName,
            contactName: contact?.name,
            contactPhone: contact?.phone,
            contactEmail: contact?.email,
        };
    }

    @DefTransaction()
    async sendEmailToCustomer(dto: SendQuoteEmailDto) {
        const data = await this.detail({ id: dto.id });
        if (!data) {
            throw new BusinessException('Quote not found');
        }
        await this.quoteRepo.update({ id: dto.id }, { status: NSQuote.EStatus.SENT_TO_CUSTOMER });
        const locale = 'vi-VN';

        const subtotal = (data.quoteItems || []).reduce(
            (s, it) => s + Number(it.totalBeforeVat || 0),
            0,
        );
        const totalAfter = (data.quoteItems || []).reduce(
            (s, it) => s + Number(it.totalAfterVat || 0),
            0,
        );
        const vatTotal = totalAfter - subtotal;
        // Lấy VAT từ sản phẩm đầu tiên (giả định các sản phẩm cùng thuế suất)
        const vatPercent = data.quoteItems?.[0]?.vat || 0;

        const validUntil = dayjs(data.createdDate)
            .add(Number(data.validityDays || 0), 'day')
            .toDate();
        const distPath = join(__dirname, '../../../template/quotation-email.hbs');
        const srcPath = join(__dirname, '../../../..', 'src/template/quotation-email.hbs');
        const templatePath = existsSync(distPath) ? distPath : srcPath;
        const tpl = readFileSync(templatePath, 'utf8');
        const year = dayjs().format('YYYY');

        const logoSystem = await this.settingStringService.getDefaultValueByKey(
            'string',
            NSSettingString.EDefaultConfigKey.LOGO,
        );
        console.log(logoSystem);
        const template = Handlebars.compile(tpl);
        const html = template({
            logoSystem,
            quotationNumber: String(data.quotationNumber || ''),
            memberName: String(data.memberName || ''),
            companyTagline: '', // Có thể cập nhật từ cấu hình nếu có
            companyDescription: '', // Có thể cập nhật từ cấu hình nếu có
            contactName: String(data.contactName || ''),
            contactPhone: String(data.contactPhone || ''),
            contactEmail: String(data.contactEmail || ''),

            customerName: String(data.customerName || ''),
            customerAddress: String(data.customerAddress || ''),
            customerEmail: String(data.customerEmail || ''),
            customerTaxCode: String(data.customerTaxCode || ''),

            quotationDate: formatDate(data.quotationDate, locale),
            deliveryDate: formatDate(data.deliveryDate, locale),
            validityDays: data.validityDays,
            dueDate: formatDate(validUntil, locale),

            items: (data.quoteItems || []).map(item => ({
                name: item.name,
                quantity: item.quantity,
                unitPrice: formatCurrency(item.unitPrice, locale),
                total: formatCurrency(item.totalBeforeVat, locale),
            })),

            subtotal: formatCurrency(subtotal, locale),
            vatPercent: vatPercent,
            vatTotal: formatCurrency(vatTotal, locale),
            totalAmount: formatCurrency(data.totalAmount, locale),
            year,
        });

        const to = data.customerEmail as string;
        if (!to) throw new BusinessException('Không có email khách hàng để gửi');
        await this.mailSenderService.sendMail({
            from: dto.emailFrom,
            to,
            subject: `[Báo giá] ${data.quotationNumber}`,
            html,
        });
        return { message: 'Đã gửi email báo giá', to, quotationNumber: data.quotationNumber };
    }

    @DefTransaction()
    async create(body: FormData, file?: Express.Multer.File[]) {
        const data = JSON.parse((body as any).data);
        const { memberId, tenantId } = clientSessionContext;
        const { ...quoteData } = data;
        const quotes = await this.quoteRepo.find({
            where: {
                tenantId,
            },
        });

        const quotationNumber = `BG000${quotes.length + 1}`;

        const quote = this.quoteRepo.create({
            ...quoteData,
            quotationNumber,
        });

        const newQuote = await this.quoteRepo.save({
            ...quote,
            createdBy: memberId,
            status: NSQuote.EStatus.NEW,
        });

        // Upload file mới và lưu vào media table
        let uploadFiles = [];
        if (file && file.length > 0) {
            for (const item of file) {
                try {
                    const itemUpload = await this.uploadService.uploadSingle(item);
                    uploadFiles.push({ file: item, uploaded: itemUpload });
                } catch (error) {
                    throw new BusinessException(error.message);
                }
            }
        }

        // Lưu vào media table cho các file mới upload với entity và entityId
        if (uploadFiles && uploadFiles.length > 0) {
            for (const { file: item, uploaded } of uploadFiles) {
                const mediaEntity = this.mediaRepo.create({
                    tenantId,
                    url: uploaded.fileUrl,
                    fileName: uploaded.fileName || item.originalname,
                    contentType: uploaded.contentType,
                    mediaType: uploaded.mediaType,
                    size: item.size,
                    entity: 'quote',
                    entityId: newQuote.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                await this.mediaRepo.save(mediaEntity);
            }
        }

        const catalogItems = await this.catalogItemRepo.find({ where: { tenantId } });

        if (data.quotationProducts && data.quotationProducts.length > 0) {
            const newItems = data.quotationProducts.map(item => ({
                ...item,
                name: catalogItems.find(q => q.code === item.code)?.name,
                catalogItemId: catalogItems.find(q => q.code === item.code)?.id,
                quoteId: newQuote.id,
                createdBy: memberId,
            }));

            await this.quoteItemRepo.save(newItems as any);
        }
        await this.alertCronService.generateAlertForQuote(newQuote.id);
        try {
            await this.approvalRequestService.createRequestForDocument(
                {
                    businessType: NSApproval.ApprovalBusinessType.QUOTATION,
                    documentId: newQuote.id,
                    documentCode: newQuote.quotationNumber,
                    createdBy: memberId,
                    title: `Duyệt báo giá ${newQuote.quotationNumber}`,
                    initialStatus: NSApproval.ApprovalRequestStatus.PENDING,
                    finalStatuses: [
                        NSApproval.ApprovalRequestStatus.APPROVED,
                        NSApproval.ApprovalRequestStatus.REJECTED,
                    ],
                    approvedStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                },
                // { skipIfNoConfig: true }, // Skip nếu không có config
            );
        } catch (error) {}
        return {
            message: 'Tạo báo giá thành công',
            data: newQuote,
        };
    }

    @DefTransaction()
    async update(body: FormData, file?: Express.Multer.File[]) {
        try {
            const data = JSON.parse((body as any).data);
            const { id, quotationProducts, ...quoteData } = data;
            const quote = await this.quoteRepo.findOneBy({ id });
            if (!quote) {
                throw new BusinessException('Quote not found');
            }
            const { memberId, tenantId } = clientSessionContext;

            // Upload file mới và lưu vào media table
            let uploadFiles = [];
            if (file && file.length > 0) {
                for (const item of file) {
                    try {
                        const itemUpload = await this.uploadService.uploadSingle(item);
                        uploadFiles.push({ file: item, uploaded: itemUpload });
                    } catch (error) {
                        throw new BusinessException(error.message);
                    }
                }
            }

            // Lưu vào media table cho các file mới upload với entity và entityId
            if (uploadFiles && uploadFiles.length > 0) {
                for (const { file: item, uploaded } of uploadFiles) {
                    const mediaEntity = this.mediaRepo.create({
                        tenantId,
                        url: uploaded.fileUrl,
                        fileName: uploaded.fileName || item.originalname,
                        contentType: uploaded.contentType,
                        mediaType: uploaded.mediaType,
                        size: item.size,
                        entity: 'quote',
                        entityId: quote.id,
                        createdBy: memberId,
                        createdDate: new Date(),
                    });
                    await this.mediaRepo.save(mediaEntity);
                }
            }

            const catalogItems = await this.catalogItemRepo.find({ where: { tenantId } });
            const existingItems = await this.quoteItemRepo.find({ where: { quoteId: id } });
            if (quotationProducts) {
                const newItems = quotationProducts.map(item => ({
                    ...item,
                    catalogItemId: catalogItems.find(q => q.code === item.code)?.id,
                    name: catalogItems.find(q => q.code === item.code)?.name,
                    quoteId: id,
                    updatedBy: memberId,
                }));

                await this.quoteItemRepo.save(newItems as any);

                const itemsToDelete = existingItems.filter(
                    item => !quotationProducts.some(newItem => newItem.code === item.code),
                );
                if (itemsToDelete.length > 0) {
                    await this.quoteItemRepo.remove(itemsToDelete);
                }
            }

            await this.quoteRepo.update(id, {
                ...quoteData,
                totalAmount: quoteData.totalAmount,
                updatedBy: memberId,
                updatedDate: new Date(),
                id,
            });

            await this.alertCronService.generateAlertForQuote(id);
            return { message: 'Cập nhật báo giá thành công' };
        } catch (error) {
            throw new BusinessException('Không thể cập nhật báo giá: ' + error.message);
        }
    }

    @DefTransaction()
    async updateStatus(dto: UpdateStatusDto) {
        const { id, status } = dto;
        const quote = await this.quoteRepo.findOneBy({ id });
        if (!quote) {
            throw new BusinessException('Quote not found');
        }
        if (quote.status === status) {
            return { affected: 0 } as any;
        }
        const res = await this.quoteRepo.update(id, { status, id });

        await this.alertCronService.generateAlertForQuote(id);
        return res;
    }
}
