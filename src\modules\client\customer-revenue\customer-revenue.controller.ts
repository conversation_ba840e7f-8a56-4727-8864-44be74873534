import { Query } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { CustomerRevenueService } from './customer-revenue.service';
import { ListCustomerRevenueDto } from './dto/customer-revenue.dto';

@DefController('customer-revenue')
export class CustomerRevenueController {
    constructor(private readonly customerRevenueService: CustomerRevenueService) {}

    @DefGet('list')
    async listCustomerRevenue(@Query() query: ListCustomerRevenueDto) {
        return this.customerRevenueService.listCustomerRevenue(query);
    }
}
