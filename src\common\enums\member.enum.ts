export namespace NSMember {
    //#region Trạng thái
    export enum EStatus {
        INACTIVE = 'INACTIVE',
        ACTIVE = 'ACTIVE',
    }

    export enum EType {
        TENANT_MASTER = 'TENANT_MASTER', // root
        TENANT_USER = 'TENANT_USER', // user con của tenant
    }

    export enum EAuthProviderType {
        EMAIL = 'EMAIL',
        APE = 'APE',
        GOOGLE = 'GOOGLE',
        FACEBOOK = 'FACEBOOK',
        APPLE = 'APPLE',
    }

    export const ESTATUS_LOCALE_LABEL = {
        [EStatus.INACTIVE]: 'enums.NSMember.EStatus.INACTIVE',
        [EStatus.ACTIVE]: 'enums.NSMember.EStatus.ACTIVE',
    };

    export const ETYPE_LOCALE_LABEL = {
        [EType.TENANT_MASTER]: 'enums.NSMember.EType.TENANT_MASTER',
        [EType.TENANT_USER]: 'enums.NSMember.EType.TENANT_USER',
    };
}
