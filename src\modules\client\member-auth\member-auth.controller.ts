import { Body, Req, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import * as express from 'express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import * as querystring from 'querystring';
import { configEnv } from '~/@config/env';
import { optionalApiConnector } from '~/connectors';
import { ApeSsoAuthGuard } from '~/modules/@guards/ape-sso-auth/ape-sso-auth.guard';
import { ApeSsoUserInfo, ApeSsoUserInfoSession } from '~/modules/@guards/ape-sso-auth/dto';
import { ClientAuthGuard } from '~/modules/client/@guards/client-auth/client-auth.guard';
import { MemberLoginReq, UpdateUserInfoReq } from './dto';
import { MemberAuthService } from './member-auth.service';

const { APE_SSO_URL, APE_CLIENT_ID, APE_CLIENT_SECRET, APE_CALLBACK_URL } = configEnv();

@DefController('auth')
export class MemberAuthController {
    constructor(private readonly memberAuthService: MemberAuthService) {}

    /**
     * @vn Đăng nhập
     * @en Login
     */
    @DefPost('login')
    async login(@Body() body: MemberLoginReq) {
        const loginEndpoint = `${APE_SSO_URL}/api/client/auth/login`;
        const userInfoEndpoint = `${APE_SSO_URL}/api/client/auth/userinfo`;
        const loginResult = await optionalApiConnector.post<{ access_token: string }>(
            loginEndpoint,
            {
                ...body,
                clientId: APE_CLIENT_ID,
                clientSecret: APE_CLIENT_SECRET,
                redirectUri: APE_CALLBACK_URL,
            },
        );

        const accessToken = loginResult.access_token;
        const userInfoResult = await optionalApiConnector.get<ApeSsoUserInfo>(
            userInfoEndpoint,
            {},
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            },
        );

        return this.memberAuthService.apeLogin({
            ...userInfoResult,
            providerId: userInfoResult.id, // Use the user's id as providerId
        });
    }

    /**
     * @vn Lấy thông tin profile user
     * @en Get profile user
     */
    @UseGuards(ClientAuthGuard)
    @DefGet('me', {
        summary: 'Get profile user',
    })
    me(@Req() req: express.Request & { user: any }) {
        // lấy thông tin user từ DB
        return req.user;
    }

    @UseGuards(ApeSsoAuthGuard)
    @DefGet('ape')
    apeLogin(@Req() req: express.Request, @Res() res: express.Response) {}

    @DefGet('ape/callback')
    @UseGuards(ApeSsoAuthGuard)
    async apeAuthRedirect(
        @Req() req: express.Request & { user: ApeSsoUserInfoSession },
        @Res() res: express.Response,
    ) {
        const result: any = await this.memberAuthService.apeLogin({
            username: req.user.username,
            providerId: req.user.id,
            fullName: req.user.fullName,
            avatar: req.user.avatar,
            tenantId: req.user.tenantId,
            type: req.user.type,
            ssoAccountId: req.user.id, // ID account bên SOO
        });
        const redirectUri = req.query.state;
        const query = querystring.stringify(result);
        const finalRedirect = `${redirectUri}?${query}`;
        return res.redirect(finalRedirect);
    }

    @DefGet('userinfo')
    @UseGuards(ClientAuthGuard)
    userInfo(@Req() req) {
        const user = req.user;
        return this.memberAuthService.getUserInfo({
            id: user.sub,
        });
    }

    @DefPost('update-user-info')
    @UseInterceptors(FileInterceptor('file'))
    updateUserInfo(@UploadedFile() file: Express.Multer.File, @Body() body: UpdateUserInfoReq) {
        return this.memberAuthService.updateUserInfo(file, body);
    }
}
