import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

// <PERSON><PERSON>n hệ của khách hàng
@Entity('customer_contact')
export class CustomerContactEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mã khách hàng' })
    @Column({ type: 'uuid' })
    @Index()
    customerId: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'Mã liên hệ' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    @Index()
    code: string;

    @ApiProperty({ example: 'Công ty ABC' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    name: string;

    // chức vụ
    @ApiPropertyOptional({ example: 'Trưởng phòng' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    position?: string | null;

    // email
    @ApiPropertyOptional({ example: '<EMAIL>' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    email?: string | null;

    @ApiPropertyOptional({ example: '+84 912 345 678' })
    @Column({ type: 'varchar', length: 50, nullable: true })
    phone?: string | null;

    // Là người ra quyết định
    @ApiPropertyOptional({ example: true })
    @Column({ type: 'boolean', default: false })
    isDecisionMaker?: boolean;

    // Ghi chú
    @ApiPropertyOptional({ example: 'Ghi chú' })
    @Column({ type: 'text', nullable: true })
    note?: string | null;
}
