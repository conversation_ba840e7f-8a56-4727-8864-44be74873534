import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, RequirePermissions } from '../@guards';
import { PermissionGuard } from '../@guards/permission/permission.guard';
import { GroupCustomerService } from './group-customer.service';
import { CreateGroupCustomerDto, ListGroupCustomerDto, UpdateGroupCustomerDto, UpdateGroupStatusDto, DetailGroupCustomerDto } from './dto/group-customer.dto';
import { NSGroupCustomer } from '~/common/enums/group-customer.enum';

@UseGuards(PermissionGuard)
@ApiTags('GroupCustomer')
@DefController('group-customer')
export class GroupCustomerController {
  constructor(private readonly service: GroupCustomerService) {}

  @DefGet('list')
  @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.VIEW])
  @ApiOperation({ summary: 'Danh sách nhóm khách hàng' })
  async list(@Query() query: ListGroupCustomerDto) {
    return this.service.list(query);
  }

  @DefPost('create')
  @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.CREATE])
  @ApiOperation({ summary: 'Tạo nhóm khách hàng' })
  async create(@Body() body: CreateGroupCustomerDto) {
    return this.service.create(body);
  }

  @DefGet('detail')
  @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.VIEW])
  @ApiOperation({ summary: 'Chi tiết nhóm khách hàng' })
  async detail(@Query() query: DetailGroupCustomerDto) {
    return this.service.detail(query);
  }

  @DefPost('update')
  @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.UPDATE])
  @ApiOperation({ summary: 'Cập nhật nhóm khách hàng' })
  async update(@Body() body: UpdateGroupCustomerDto) {
    return this.service.update(body);
  }

  @DefPost('update-status')
  @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.UPDATE])
  @ApiOperation({ summary: 'Cập nhật trạng thái nhóm khách hàng' })
  async updateStatus(@Body() body: UpdateGroupStatusDto) {
    return this.service.updateStatus(body.id, body.status as NSGroupCustomer.EStatus);
  }
}
