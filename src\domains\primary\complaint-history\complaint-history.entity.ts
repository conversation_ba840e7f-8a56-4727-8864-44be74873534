import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSComplaint } from '~/common/enums/complaint.enum';

@Entity('complaint_history')
export class ComplaintHistoryEntity extends PrimaryBaseEntity {
  @ApiProperty({ example: 'uuid complaint' })
  @Column({ type: 'uuid' })
  @Index()
  complaintId: string;

  @ApiPropertyOptional({ example: 'uuid actor' })
  @Column({ type: 'uuid', nullable: true })
  actorId?: string | null;

  @ApiPropertyOptional({ enum: NSComplaint.EComplaintStatus })
  @Column({ type: 'enum', enum: NSComplaint.EComplaintStatus, nullable: true })
  fromStatus?: NSComplaint.EComplaintStatus | null;

  @ApiPropertyOptional({ enum: NSComplaint.EComplaintStatus })
  @Column({ type: 'enum', enum: NSComplaint.EComplaintStatus, nullable: true })
  toStatus?: NSComplaint.EComplaintStatus | null;

  @ApiPropertyOptional({ example: 'Đã gọi khách hàng xác nhận' })
  @Column({ type: 'text', nullable: true })
  note?: string | null;

  @ApiPropertyOptional({ example: { patch: { handlerEmployeeId: '...' } } })
  @Column({ type: 'jsonb', nullable: true })
  meta?: Record<string, any> | null;

  @ApiPropertyOptional({ description: 'ID khiếu nại gốc cũ' })
  @Column({ type: 'uuid', nullable: true })
  fromParentComplaintId?: string | null;

  @ApiPropertyOptional({ description: 'ID khiếu nại gốc mới' })
  @Column({ type: 'uuid', nullable: true })
  toParentComplaintId?: string | null;

  @ApiPropertyOptional({ description: 'Loại quan hệ cũ' })
  @Column({ type: 'varchar', nullable: true })
  fromRelationshipType?: string | null;

  @ApiPropertyOptional({ description: 'Loại quan hệ mới' })
  @Column({ type: 'varchar', nullable: true })
  toRelationshipType?: string | null;
}
