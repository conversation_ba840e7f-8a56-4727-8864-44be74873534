import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { ContractService } from './contract.service';
import {
    ContractTemplateDTO,
    CreateContractTemplateDto,
    PaginationTemplateQueryDto,
} from './dto/contract-template.dto';
import {
    ContractDTO,
    CreateContractDto,
    PaginationQueryDto,
    UpdateStatusDto,
} from './dto/contract.dto';

@UseGuards(PermissionGuard)
@DefController('contract')
export class ContractController {
    constructor(private contractService: ContractService) {}

    @DefPost('create-template')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.CREATE])
    async createTemplate(@Body() body: CreateContractTemplateDto) {
        return await this.contractService.createTemplate(body);
    }

    @DefGet('pagination-template')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.VIEW])
    async pagination(@Query() query: PaginationTemplateQueryDto) {
        return await this.contractService.pagination(query);
    }

    @DefGet('detail-template/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.VIEW])
    async detailTemplate(@Param('id') id: string) {
        return await this.contractService.detailTemplate(id);
    }

    @DefPost('update-template')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.UPDATE])
    async updateTemplate(@Body() body: ContractTemplateDTO) {
        return await this.contractService.updateTemplate(body);
    }

    @DefPost('inactive-template/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.UPDATE])
    async inactiveTemplate(@Param('id') id: string) {
        return await this.contractService.inactiveTemplate(id);
    }

    @DefPost('active-template/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.UPDATE])
    async activeTemplate(@Param('id') id: string) {
        return await this.contractService.activeTemplate(id);
    }

    @DefGet('pagination')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.VIEW])
    async paginationContract(@Query() query: PaginationQueryDto) {
        return await this.contractService.paginationContract(query);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.CREATE])
    async createContract(@Body() body: CreateContractDto) {
        return await this.contractService.createContract(body);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.UPDATE])
    async updateContract(@Body() body: ContractDTO) {
        return await this.contractService.updateContract(body);
    }

    @DefGet('detail/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.VIEW])
    async detailContract(@Param('id') id: string) {
        return await this.contractService.detailContract(id);
    }

    @DefPost('update-status')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTRACT.UPDATE])
    async updateStatusContract(@Body() body: UpdateStatusDto) {
        return await this.contractService.updateStatusContract(body);
    }
}
