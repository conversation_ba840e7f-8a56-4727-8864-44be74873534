import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { DepartmentService } from './department.service';
import {
    CreateDepartmentDto,
    DeleteDepartmentDto,
    FindDepartmentDto,
    PaginationDepartmentDto,
    UpdateDepartmentDto,
} from './dto/department.dto';

@UseGuards(PermissionGuard)
@Controller('department')
export class DepartmentController {
    constructor(private readonly departmentService: DepartmentService) {}

    @Get('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_DEPARTMENT.VIEW])
    async list(@Query() dto: PaginationDepartmentDto) {
        return this.departmentService.list(dto);
    }

    @Post('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_DEPARTMENT.CREATE])
    async create(@Body() body: CreateDepartmentDto) {
        return this.departmentService.create(body);
    }

    @Post('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_DEPARTMENT.UPDATE])
    async update(@Body() body: UpdateDepartmentDto) {
        return this.departmentService.update(body);
    }

    @Post('find-by-id')
    @RequirePermissions([PERMISSION_CODES.SETTING_DEPARTMENT.VIEW])
    async findById(@Body() body: FindDepartmentDto) {
        return this.departmentService.findById(body);
    }

    @Post('delete')
    @RequirePermissions([PERMISSION_CODES.SETTING_DEPARTMENT.UPDATE])
    async delete(@Body() body: DeleteDepartmentDto) {
        return this.departmentService.deleteDepartment(body);
    }
}
