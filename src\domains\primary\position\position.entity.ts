import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { NSPosition } from '~/common/enums/position.enum';

@Entity('position')
export class PositionEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    departmentId?: string;

    @ApiProperty({ example: '<PERSON><PERSON><PERSON>' })
    @Column({ type: 'varchar', nullable: true, length: 255 })
    @Index()
    name: string;

    @ApiProperty({ example: 'NV001' })
    @Column({ type: 'varchar', nullable: true, length: 255 })
    @Index()
    code: string;

    @ApiProperty({ example: 'Description' })
    @Column({ type: 'varchar', nullable: true, length: 255 })
    description?: string;

    @ApiProperty({ example: 'ACTIVE' })
    @Column({ type: 'enum', enum: NSPosition.EStatus, default: NSPosition.EStatus.ACTIVE })
    @Index()
    status: NSPosition.EStatus;

    @ApiProperty({ example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
    @Column({ type: 'uuid', nullable: true })
    positionAuthId?: string;
}
