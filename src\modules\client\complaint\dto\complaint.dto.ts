import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSComplaint } from '~/common/enums/complaint.enum';

export class CreateCustomerComplaintDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    customerId: string;

    @ApiProperty({ example: '<PERSON>hiếu nại về giao hàng trễ' })
    @MaxLength(255)
    @IsOptional()
    @IsString()
    title: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({})
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({
        enum: NSComplaint.EComplaintStatus,
        default: NSComplaint.EComplaintStatus.NEW,
    })
    @IsOptional()
    @IsEnum(NSComplaint.EComplaintStatus)
    status?: NSComplaint.EComplaintStatus;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    followerEmployeeId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    handlerEmployeeId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    communeCode?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsDateString()
    dueDate?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @MaxLength(255)
    addressId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    @MaxLength(255)
    address?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    priority?: string;

    @ApiPropertyOptional({ description: 'Danh sách media IDs' })
    @IsOptional()
    @IsString({ each: true })
    mediaIds?: string[];

    @ApiPropertyOptional({ description: 'ID khiếu nại gốc (parent complaint)' })
    @IsOptional()
    @IsString()
    parentComplaintId?: string;

    @ApiPropertyOptional({
        description: 'Loại quan hệ với khiếu nại gốc',
        enum: NSComplaint.EComplaintRelationshipType,
    })
    @IsOptional()
    @IsEnum(NSComplaint.EComplaintRelationshipType)
    relationshipType?: NSComplaint.EComplaintRelationshipType;
}

export class UpdateCustomerComplaintDto {
    data: CreateCustomerComplaintDto;
}

export class ListCustomerComplaintDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Tìm kiếm theo mã' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ description: 'Tìm kiếm theo tiêu đề' })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional({
        enum: NSComplaint.EComplaintStatus,
        example: NSComplaint.EComplaintStatus.IN_PROGRESS,
    })
    @IsOptional()
    status?: NSComplaint.EComplaintStatus;

    @ApiPropertyOptional({
        description: 'Lọc theo loại khiếu nại',
    })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: 'Lọc theo khách hàng' })
    @IsOptional()
    @IsString()
    customerId?: string;

    @ApiPropertyOptional({ description: 'Lọc theo NV xử lý' })
    @IsOptional()
    @IsString()
    handlerEmployeeId?: string;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu từ' })
    @IsOptional()
    @IsDateString()
    createdDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày bắt đầu đến' })
    @IsOptional()
    @IsDateString()
    createdDateTo?: string;

    @ApiPropertyOptional({ description: 'Ngày hết hạn từ' })
    @IsOptional()
    @IsDateString()
    dueDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày hết hạn đến' })
    @IsOptional()
    @IsDateString()
    dueDateTo?: string;

    @ApiPropertyOptional({ description: 'Lọc theo địa chỉ' })
    @IsOptional()
    @IsString()
    address?: string;

    @ApiPropertyOptional({ description: 'Lọc theo tỉnh/thành phố' })
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional({ description: 'Lọc theo quận/huyện' })
    @IsOptional()
    @IsString()
    communeCode?: string;

    @ApiPropertyOptional({ description: 'Lọc theo mức độ ưu tiên' })
    @IsOptional()
    @IsString()
    priority?: string;

    @ApiPropertyOptional({ description: 'Lọc theo NV theo dõi' })
    @IsOptional()
    @IsString()
    followerEmployeeId?: string;

    @ApiPropertyOptional({ description: 'Lọc theo NV tạo' })
    @IsOptional()
    @IsString()
    createdBy?: string;

    @ApiPropertyOptional({ description: 'Lọc theo khiếu nại gốc' })
    @IsOptional()
    @IsString()
    parentComplaintId?: string;

    @ApiPropertyOptional({
        description: 'Lọc theo loại quan hệ',
        enum: NSComplaint.EComplaintRelationshipType,
    })
    @IsOptional()
    @IsEnum(NSComplaint.EComplaintRelationshipType)
    relationshipType?: NSComplaint.EComplaintRelationshipType;
}
