import { Body, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { UpsertKpiCategoriesDto } from './dto/kpi-categories.dto';
import { KpiCategoriesService } from './kpi-categories.service';

@UseGuards(PermissionGuard)
@DefController('kpi-categories')
export class KpiCategoriesController {
    constructor(private readonly service: KpiCategoriesService) {}

    @DefGet('defaults')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    async getDefaults() {
        return this.service.getDefaultsMerged();
    }

    @DefGet('active')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    async getActive() {
        return this.service.getActiveCategories();
    }

    @DefPost('upsert')
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.UPDATE])
    async upsert(@Body() dto: UpsertKpiCategoriesDto) {
        return this.service.upsert(dto.items);
    }
}
