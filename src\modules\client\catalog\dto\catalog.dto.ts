// src/domains/crm/dto/catalog-service.dto.ts
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, Length, MaxLength } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { CurrencyCode, NSCatalog } from '~/common/enums';

/**
 * CREATE
 */
export class CreateCatalogDto {
    @ApiProperty({ example: 'Gói SEO theo tháng' })
    @IsString()
    @Length(1, 255)
    name: string;

    @ApiProperty({
        example: NSCatalog.EType.SERVICE,
        enum: NSCatalog.EType,
        default: NSCatalog.EType.SERVICE,
    })
    @IsEnum(NSCatalog.EType)
    type: NSCatalog.EType = NSCatalog.EType.SERVICE;

    @ApiPropertyOptional({ example: 'Tối ưu SEO onpage + offpage' })
    @IsOptional()
    @IsString()
    description?: string | null;

    @ApiPropertyOptional({ example: 'tháng' })
    @IsOptional()
    @IsString()
    @MaxLength(64)
    unit?: string | null;

    @ApiProperty({ example: 'VND', enum: CurrencyCode, default: CurrencyCode.VND })
    @IsEnum(CurrencyCode)
    currency: CurrencyCode = CurrencyCode.VND;

    @ApiProperty({
        example: 15000000,
        description: 'Đơn giá (integer). Accept number hoặc string; sẽ lưu xuống numeric(20,0).',
    })
    unitPrice: string;

    @ApiPropertyOptional({
        example: 8.0,
        description: 'Thuế %, tối đa 2 chữ số thập phân. Accept number hoặc string.',
    })
    @IsOptional()
    taxRate?: string;

    //image
    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @IsOptional()
    @IsString()
    images?: string | null;

    //attachment
    @ApiPropertyOptional({ example: 'https://example.com/attachment.pdf' })
    @IsOptional()
    @IsString()
    attachments?: string | null;

    @ApiProperty({
        example: NSCatalog.EStatus.ACTIVE,
        enum: NSCatalog.EStatus,
        default: NSCatalog.EStatus.ACTIVE,
    })
    @IsEnum(NSCatalog.EStatus)
    status: NSCatalog.EStatus = NSCatalog.EStatus.ACTIVE;
}

/**
 * UPDATE
 */
export class UpdateCatalogDto extends PartialType(CreateCatalogDto) {
    @ApiProperty({ description: 'ID của Catalog' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

/**
 * LIST / FILTER + PAGINATION + SORT
 */
export class ListCatalogDto extends PageRequest {
    @ApiPropertyOptional({
        description: 'Tìm kiếm nhanh theo code/name',
        example: 'SEO',
    })
    @IsOptional()
    @IsString()
    q?: string;

    @ApiPropertyOptional({ example: 'SRV-SEO-MONTHLY' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ example: 'Gói SEO theo tháng' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ enum: NSCatalog.EType, example: NSCatalog.EType.SERVICE })
    @IsOptional()
    @IsEnum(NSCatalog.EType)
    type?: NSCatalog.EType;

    @ApiPropertyOptional({ enum: NSCatalog.EStatus, example: NSCatalog.EStatus.ACTIVE })
    @IsOptional()
    @IsEnum(NSCatalog.EStatus)
    status?: NSCatalog.EStatus;

    @ApiPropertyOptional({ example: '2024-01-01' })
    @IsOptional()
    createdFrom?: string;

    @ApiPropertyOptional({ example: '2024-12-31' })
    @IsOptional()
    createdTo?: string;
}
