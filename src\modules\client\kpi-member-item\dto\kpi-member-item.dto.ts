import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

export class UpdateKpiMemberItemDto {
    @ApiPropertyOptional({ example: 120 })
    @IsOptional()
    @IsInt()
    @Min(0)
    targetValue?: number;

    @ApiPropertyOptional({ example: 60 })
    @IsOptional()
    @IsInt()
    @Min(0)
    @Max(100)
    weight?: number;

    @ApiPropertyOptional({ example: 'Ghi chú' })
    @IsOptional()
    @IsString()
    note?: string;
}

