import { ApiProperty } from '@nestjs/swagger';
import { Column } from 'typeorm';
import { NSPermission } from '~/common/enums';
import { PrimaryBaseEntity } from '../primary-base.entity';

// @Entity('permissions')
export class PermissionEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'USER.CREATE' })
    @Column({ type: 'varchar', length: 50, unique: true })
    code: string;

    @ApiProperty({ example: 'Xem danh sách người dùng' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiProperty({ example: 'Cho phép tạo người dùng mới' })
    @Column({ type: 'varchar', length: 500, nullable: true })
    description: string;

    @ApiProperty({ example: NSPermission.EStatus.ACTIVE })
    @Column({ type: 'enum', enum: NSPermission.EStatus, default: NSPermission.EStatus.ACTIVE })
    status: NSPermission.EStatus;
}
