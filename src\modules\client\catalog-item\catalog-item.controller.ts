import {
    Body,
    FileTypeValidator,
    MaxFileSizeValidator,
    ParseFilePipe,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { CatalogService } from './catalog-item.service';
import {
    CreateCatalogItemDto,
    ListCatalogItemDto,
    UpdateCatalogItemDto,
} from './dto/catalog-item.dto';

@UseGuards(PermissionGuard)
@DefController('catalog-item')
export class CatalogItemController {
    constructor(private catalogService: CatalogService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.VIEW])
    async listCatalog(@Query() params: ListCatalogItemDto) {
        return this.catalogService.listCatalog(params);
    }

    @DefGet('select-box')
    async selectBox(@Query() params: { pageSize: number }) {
        return this.catalogService.selectBox(params.pageSize);
    }

    @DefGet('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.VIEW])
    async detailCatalog(@Query() params: { id: string }) {
        return this.catalogService.detailCatalog(params.id);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.CREATE])
    @UseInterceptors(FileInterceptor('file'))
    async createCatalog(
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new FileTypeValidator({ fileType: /image\/(jpeg|png|webp)/ }),
                    new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }),
                ],
                fileIsRequired: false,
            }),
        )
        file: Express.Multer.File,
        @Body() catalog: CreateCatalogItemDto,
    ) {
        return this.catalogService.createCatalog(catalog, file);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    @UseInterceptors(FileInterceptor('file'))
    async updateCatalog(
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new FileTypeValidator({ fileType: /image\/(jpeg|png|webp)/ }),
                    new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }),
                ],
                fileIsRequired: false,
            }),
        )
        file: Express.Multer.File,
        @Body() catalog: UpdateCatalogItemDto,
    ) {
        return this.catalogService.updateCatalog(catalog, file);
    }

    @DefPost('active')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    async activeCatalog(@Body() catalog: UpdateCatalogItemDto) {
        return this.catalogService.activeCatalog(catalog.id);
    }

    @DefPost('inactive')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    async inActiveCatalog(@Body() catalog: UpdateCatalogItemDto) {
        return this.catalogService.inActiveCatalog(catalog.id);
    }

    @DefPost('upload')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.CREATE])
    @UseInterceptors(FileInterceptor('file'))
    async uploadCatalogItem(@UploadedFile() file: Express.Multer.File) {
        return this.catalogService.uploadCatalogItem(file);
    }
}
