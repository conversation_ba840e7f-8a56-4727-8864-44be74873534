import { Body, UseGuards } from '@nestjs/common';
import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES } from '~/common/enums/permission-config.enum';
import { PermissionGuard } from '../@guards/permission/permission.guard';
import { RequirePermissions } from '../@guards/permission/require-permissions.decorator';
import { CreateRoleDto, ListRoleDto, RoleDetailDto, UpdateRoleDto } from './dto';
import { RoleService } from './role.service';

@UseGuards(PermissionGuard)
@DefController('roles')
export class RoleController {
    constructor(private readonly roleService: RoleService) { }

    @DefPost('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.VIEW])
    async list(@Body() params: ListRoleDto) {
        return this.roleService.list(params);
    }

    @DefPost('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.VIEW])
    async detail(@Body() params: RoleDetailDto) {
        return this.roleService.detail(params.id);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.CREATE])
    async create(@Body() data: CreateRoleDto) {
        return this.roleService.create(data);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.UPDATE])
    async update(@Body() data: UpdateRoleDto) {
        return this.roleService.update(data);
    }

    @DefPost('active')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.UPDATE])
    async active(@Body() data: RoleDetailDto) {
        return this.roleService.active(data.id);
    }

    @DefPost('inactive')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.UPDATE])
    async inActive(@Body() data: RoleDetailDto) {
        return this.roleService.inActive(data.id);
    }

    // @DefGet('list-permission', { summary: 'Lấy danh sách tất cả các quyền có trong hệ thống' })
    // async listPermission() {
    //   return this.roleService.listPermission()
    // }

    @DefPost('list-permission')
    @RequirePermissions([PERMISSION_CODES.SETTING_ROLE.VIEW])
    async listPermission() {
        return this.roleService.listPermission()
    }
}
