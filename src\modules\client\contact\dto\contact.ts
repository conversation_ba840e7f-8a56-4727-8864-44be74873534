import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEmail,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUUID,
    ValidateNested,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSCustomer } from '~/common/enums';

/**
 * DETAIL
 */
export class DetailContactDto {
    @ApiProperty({ description: 'ID của liên hệ' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

/**
 * CREATE
 */
export class CreateContactDto {
    @ApiProperty({ description: 'ID của khách hàng' })
    @IsString()
    @IsNotEmpty()
    customerId?: string;

    @ApiProperty({ description: 'Code của liên hệ' })
    @IsString()
    @IsOptional()
    code?: string;

    @ApiProperty({ description: 'Tên liên hệ' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: '<PERSON>ail liên hệ' })
    @IsEmail()
    @IsOptional()
    email: string;

    @ApiProperty({ description: 'Số điện thoại liên hệ' })
    @IsString()
    @IsNotEmpty()
    phone: string;

    @ApiProperty({ description: 'Vị trí liên hệ' })
    @IsString()
    @IsOptional()
    position?: NSCustomer.EPosition;

    @ApiProperty({ description: 'Ghi chú liên hệ' })
    @IsString()
    @IsOptional()
    note?: string;

    @ApiProperty({ description: 'Là người ra quyết định' })
    @IsBoolean()
    @IsNotEmpty()
    isDecisionMaker: boolean;
}

/**
 * UPDATE
 */
export class UpdateContactDto extends PartialType(CreateContactDto) {
    @ApiProperty({ description: 'ID của liên hệ' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class ListUpdateContactDto {
    @ApiProperty({ type: [UpdateContactDto] })
    @IsNotEmpty()
    @IsArray()
    // @ValidateNested({ each: true })
    @Type(() => UpdateContactDto)
    contacts: UpdateContactDto[];
}

export class DeleteListContactDto {
    @ApiProperty({ type: [String] })
    @IsArray()
    @IsUUID('4', { each: true })
    @IsNotEmpty({ each: true })
    ids: string[];
}
/**
 * LIST / FILTER + PAGINATION + SORT
 */
export class ListContactDto extends PageRequest {
    @ApiProperty({ description: 'ID của khách hàng' })
    @IsString()
    @IsOptional()
    customerId?: string;

    @ApiProperty({ description: 'Tên liên hệ' })
    @IsString()
    @IsOptional()
    name?: string;

    @ApiProperty({ description: 'Email liên hệ' })
    @IsEmail()
    @IsOptional()
    email?: string;

    @ApiProperty({ description: 'Số điện thoại liên hệ' })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiProperty({ description: 'Vị trí liên hệ' })
    @IsString()
    @IsOptional()
    position?: NSCustomer.EPosition;
}

export class ListCreateContactDto {
    @ApiProperty({ type: [CreateContactDto] })
    @IsNotEmpty()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateContactDto)
    contacts: CreateContactDto[];
}
