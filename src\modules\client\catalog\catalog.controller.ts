import { DefController, DefGet, DefPost } from "nestjs-typeorm3-kit";
import { Body, Query } from "@nestjs/common";
import { CatalogService } from "./catalog.service";
import { CreateCatalogDto, ListCatalogDto, UpdateCatalogDto } from "./dto/catalog.dto";

@DefController("catalog")
export class CatalogController {
    constructor(
        private catalogService: CatalogService,
    ) {}

    @DefGet("list")
    async listCatalog(@Query() params: ListCatalogDto) {
        return this.catalogService.listCatalog(params);
    }

    @DefGet("detail")
    async detailCatalog(@Query() params: { id: string }) {
        return this.catalogService.detailCatalog(params.id);
    }

    @DefPost("create")
    async createCatalog(@Body() catalog: CreateCatalogDto) {
        return this.catalogService.createCatalog(catalog);
    }

    @DefPost("update")
    async updateCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.updateCatalog(catalog);
    }

    @DefPost("active")
    async activeCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.activeCatalog(catalog.id);
    }

    @DefPost("inactive")
    async inActiveCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.inActiveCatalog(catalog.id);
    }
}
