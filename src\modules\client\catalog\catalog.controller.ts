import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PageRequest } from '~/@systems/utils';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { CatalogService } from './catalog.service';
import { CreateCatalogDto, ListCatalogDto, UpdateCatalogDto } from './dto/catalog.dto';

@UseGuards(PermissionGuard)
@DefController('catalog')
export class CatalogController {
    constructor(private catalogService: CatalogService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.VIEW])
    async listCatalog(@Query() params: ListCatalogDto) {
        return this.catalogService.listCatalog(params);
    }

    @DefGet('select-box')
    async selectBox(@Query() params: PageRequest) {
        return this.catalogService.selectBox(params.pageSize);
    }

    @DefGet('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.VIEW])
    async detailCatalog(@Query() params: { id: string }) {
        return this.catalogService.detailCatalog(params.id);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.CREATE])
    async createCatalog(@Body() catalog: CreateCatalogDto) {
        return this.catalogService.createCatalog(catalog);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    async updateCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.updateCatalog(catalog);
    }

    @DefPost('active')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    async activeCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.activeCatalog(catalog.id);
    }

    @DefPost('inactive')
    @RequirePermissions([PERMISSION_CODES.SETTING_CATALOG.UPDATE])
    async inActiveCatalog(@Body() catalog: UpdateCatalogDto) {
        return this.catalogService.inActiveCatalog(catalog.id);
    }
}
