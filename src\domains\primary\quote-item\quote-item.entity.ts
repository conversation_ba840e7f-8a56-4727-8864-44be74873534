import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('quote_item')
export class QuoteItemEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID sản phẩm' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    catalogItemId: string;

    @ApiProperty({ description: 'ID báo giá' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    quoteId: string;

    @ApiProperty({ description: 'Mã sản phẩm' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    code: string;

    @ApiProperty({ description: 'Tên sản phẩm' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiProperty({ description: '<PERSON><PERSON>i sản phẩm' })
    @Column({ type: 'varchar', length: 100 })
    type: string;

    @ApiPropertyOptional({ description: 'Mô tả' })
    @Column({ type: 'text', nullable: true })
    description?: any;

    @ApiProperty({ description: 'Đơn vị' })
    @Column({ type: 'varchar', length: 64 })
    unit: string;

    @ApiProperty({ description: 'Đơn vị tiền tệ' })
    @Column({ type: 'varchar', length: 10 })
    currency: string;

    @ApiProperty({ description: 'Đơn giá' })
    @Column({ type: 'numeric' })
    unitPrice: number;

    @ApiProperty({ description: 'Thuế %' })
    @Column({ type: 'numeric' })
    vat: number;

    @ApiProperty({ description: 'Số lượng' })
    @Column({ type: 'int' })
    quantity: number;

    @ApiProperty({ description: 'Tổng tiền trước VAT' })
    @Column({ type: 'numeric' })
    totalBeforeVat: number;

    @ApiProperty({ description: 'Tổng tiền sau VAT' })
    @Column({ type: 'numeric' })
    totalAfterVat: number;
}
