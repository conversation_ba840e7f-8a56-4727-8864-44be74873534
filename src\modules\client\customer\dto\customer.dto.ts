// src/domains/crm/dto/customer.dto.ts
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsPhoneNumber,
    IsString,
    IsUrl,
    <PERSON><PERSON>ength,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSCustomer } from '~/common/enums';

// ---------- CREATE ----------
export class ContactDto {
    @ApiProperty({ example: '1234567890' })
    @IsOptional()
    @IsString()
    id?: string;

    @ApiProperty({ example: 'CONT-0001' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty({ example: '<EMAIL>' })
    @IsEmail()
    @MaxLength(255)
    @IsOptional()
    email?: string;

    @ApiProperty({ example: '+84 ***********' })
    @IsPhoneNumber('VN', { message: '<PERSON><PERSON> điện thoại không hợp lệ (VN)' })
    @MaxLength(50)
    phone?: string;

    @ApiPropertyOptional({ example: 'Nguyễn Văn A' })
    @IsNotEmpty()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ example: 'Trưởng phòng' })
    @IsOptional()
    @IsString()
    position?: NSCustomer.EPosition;

    @ApiPropertyOptional({ example: 'Ghi chú' })
    @IsOptional()
    @IsString()
    note?: string;

    @ApiPropertyOptional({ example: true })
    @IsOptional()
    @IsBoolean()
    isDecisionMaker?: boolean;
}

export class CreateCustomerDto {
    @ApiProperty({ example: 'Công ty ABC' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ example: '<EMAIL>' })
    @IsOptional()
    @IsEmail()
    @MaxLength(255)
    email?: string;

    @ApiPropertyOptional({ example: '+84 ***********' })
    @IsOptional()
    @MaxLength(50)
    phone?: string;

    @ApiPropertyOptional({ enum: NSCustomer.EType })
    @IsOptional()
    @IsEnum(NSCustomer.EType)
    type?: NSCustomer.EType;

    @ApiPropertyOptional({ example: 'Công ty ABC Việt Nam' })
    @IsOptional()
    @IsString()
    companyName?: string;

    @ApiPropertyOptional({ example: '0312345678' })
    @IsOptional()
    @IsString()
    taxNumber?: string | null;

    @ApiPropertyOptional({ example: 'https://abc.com' })
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiPropertyOptional({ example: 'Hồ Chí Minh' })
    @IsOptional()
    @IsString()
    addressLine?: string;

    @ApiPropertyOptional({ example: 'VN' })
    @IsOptional()
    @IsString()
    countryCode?: string;

    @ApiPropertyOptional({ example: '79' })
    @IsOptional()
    @IsString()
    provinces?: string;

    @ApiPropertyOptional({ example: '27134' })
    @IsOptional()
    @IsString()
    communes?: string;

    @ApiPropertyOptional({ example: 'Hồ Chí Minh' })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    address?: string;

    @ApiPropertyOptional({ example: '700000', description: 'Mã bưu điện' })
    @IsOptional()
    @IsString()
    @MaxLength(20)
    postalCode?: string;

    @ApiPropertyOptional({ example: 'Khách doanh nghiệp lớn' })
    @IsOptional()
    @IsString()
    note?: string;

    @ApiPropertyOptional({ type: [String] })
    @IsOptional()
    @IsArray()
    salesRep?: string[];

    // Enums
    @ApiPropertyOptional({ example: 'NEW' })
    @IsOptional()
    @IsString()
    customerType?: string;

    @ApiPropertyOptional({ enum: NSCustomer.ESource })
    @IsOptional()
    source?: NSCustomer.ESource;

    @ApiPropertyOptional({ enum: NSCustomer.EMarket })
    @IsOptional()
    market?: NSCustomer.EMarket;

    // @ApiPropertyOptional({ type: [ContactDto] })
    // @IsOptional()
    // contacts?: ContactDto[];

    @ApiPropertyOptional({ example: '1234567890' })
    @IsOptional()
    @IsString()
    fax?: string;

    @ApiPropertyOptional({ description: 'Ngày sinh (ISO date)', example: '1990-01-01' })
    @IsOptional()
    @IsDateString()
    birthday?: string;
}

// ---------- UPDATE (body) ----------
export class UpdateCustomerDto extends PartialType(CreateCustomerDto) {
    @ApiProperty({ description: 'ID khách hàng' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

// ---------- Detail (body) ----------
export class DetailCustomerDto {
    @ApiProperty({ description: 'ID khách hàng' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

// ---------- LIST / FILTER + PAGINATION ----------
export class ListCustomerDto extends PageRequest {
    @ApiPropertyOptional({ example: 'CUST-0001' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ example: 'Công ty ABC' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ example: '<EMAIL>' })
    @IsOptional()
    @IsEmail()
    email?: string;

    @ApiPropertyOptional({ example: '+84912345678' })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiPropertyOptional({ example: 'Khách hàng tiềm năng' })
    @IsOptional()
    @IsString()
    customerType?: string;

    @ApiPropertyOptional({ example: 'Website' })
    @IsOptional()
    @IsString()
    source?: string;

    @ApiPropertyOptional({ example: 'Trong nước' })
    @IsOptional()
    @IsString()
    market?: string;

    @ApiPropertyOptional({ enum: NSCustomer.EActiveStatus })
    @IsOptional()
    @IsEnum(NSCustomer.EActiveStatus)
    status?: NSCustomer.EActiveStatus;

    @ApiPropertyOptional({ example: '79' })
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional({ example: '769' })
    @IsOptional()
    @IsString()
    districtCode?: string;

    @ApiPropertyOptional({ example: '27134' })
    @IsOptional()
    @IsString()
    communeCode?: string;

    @ApiPropertyOptional({ example: 'Hồ Chí Minh' })
    @IsOptional()
    @IsString()
    address?: string;

    @ApiPropertyOptional({ example: 'Khách hàng tiềm năng' })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: 'Lọc từ ngày tạo (ISO)', example: '2025-08-01' })
    @IsOptional()
    @IsDateString()
    createdFrom?: string;

    @ApiPropertyOptional({ description: 'Lọc đến ngày tạo (ISO)', example: '2025-08-18' })
    @IsOptional()
    @IsDateString()
    createdTo?: string;

    @ApiPropertyOptional({ description: 'Sinh nhật từ (ISO date)', example: '1990-01-01' })
    @IsOptional()
    @IsDateString()
    birthdayFrom?: string;

    @ApiPropertyOptional({ description: 'Sinh nhật đến (ISO date)', example: '2000-12-31' })
    @IsOptional()
    @IsDateString()
    birthdayTo?: string;

    @ApiPropertyOptional({ example: 'admin' })
    @IsOptional()
    @IsString()
    createdBy?: string;

    @ApiPropertyOptional({ example: '["user1", "user2"]' })
    @IsOptional()
    salesRep?: string[];
}
