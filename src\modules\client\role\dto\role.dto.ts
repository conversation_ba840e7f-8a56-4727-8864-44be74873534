import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUUID,
    Length,
    MaxLength,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSRole } from '~/common/enums';

/**
 * CREATE
 */
export class CreateRoleDto {
    @ApiProperty({ example: 'Admin' })
    @IsString()
    @Length(1, 255)
    name: string;

    @ApiPropertyOptional({ example: 'Quản trị viên hệ thống' })
    @IsString()
    @IsOptional()
    @MaxLength(500)
    description?: string;

    @ApiProperty({
        example: NSRole.EStatus.ACTIVE,
        enum: NSRole.EStatus,
        default: NSRole.EStatus.ACTIVE,
    })
    @IsEnum(NSRole.EStatus)
    status: NSRole.EStatus = NSRole.EStatus.ACTIVE;

    @ApiPropertyOptional({
        description: 'Danh sách mã quyền (permission codes)',
    })
    @IsArray()
    @IsOptional()
    permissionCodes?: any[];
}

/**
 * UPDATE
 */
export class UpdateRoleDto extends PartialType(CreateRoleDto) {
    @ApiProperty({ description: 'ID của Role' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiPropertyOptional({})
    @IsArray()
    @IsOptional()
    memberIds?: string[];
}

/**
 * LIST / FILTER + PAGINATION + SORT
 */
export class ListRoleDto extends PageRequest {
    @ApiPropertyOptional({ example: 'Admin' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ enum: NSRole.EStatus, example: NSRole.EStatus.ACTIVE })
    @IsOptional()
    @IsEnum(NSRole.EStatus)
    status?: NSRole.EStatus;
}
export class RoleDetailDto {
    @ApiProperty({ description: 'ID của Role' })
    @IsUUID()
    @IsNotEmpty()
    id: string;
}
