import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'nestjs-typeorm3-kit';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { REFIX_MODULE } from '../config-module';
import { ClientAuthModule } from './@guards';
import { ClientMiddleware } from './client.middleware';
import { MailSenderFactory } from './mail/mail-factory';
import { SocketGateway } from './socket/socket.gateway';
import { SocketService } from './socket/socket.service';

const controllers = lazyLoadClasses(__dirname, ['.controller']);
const services = lazyLoadClasses(__dirname, ['.service']);

@ChildModule({
    prefix: REFIX_MODULE.client,
    imports: [PrimaryRepoModule, ClientAuthModule],
    providers: [...services, MailSenderFactory, SocketGateway, SocketService],
    controllers: [...controllers],
})
export class ClientModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(ClientMiddleware)
            .exclude({
                path: `${REFIX_MODULE.client}/auth/*`,
                method: RequestMethod.ALL,
            })
            .forRoutes({
                path: `${REFIX_MODULE.client}/*`,
                method: RequestMethod.ALL,
            });
    }
}
