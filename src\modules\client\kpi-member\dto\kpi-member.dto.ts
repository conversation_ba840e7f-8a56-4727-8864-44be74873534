import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsEnum,
    IsInt,
    IsOptional,
    IsString,
    Max,
    Min,
    ValidateNested,
} from 'class-validator';
import { NSKPI } from '~/common/enums';

export class KpiMemberItemInputDto {
    @ApiProperty({ example: 'CUSTOMERS_COUNT' })
    @IsEnum(NSKPI.ECategoryKey)
    categoryKey: NSKPI.ECategoryKey;

    @ApiProperty({ example: 100 })
    @IsInt()
    @Min(0)
    targetValue: number;

    @ApiPropertyOptional({ example: 50 })
    @IsOptional()
    @IsInt()
    @Min(0)
    @Max(100)
    weight?: number;

    @ApiPropertyOptional({
        description: 'Trọng số theo từng nhân viên',
        example: { 'member-id-1': 60, 'member-id-2': 40 },
    })
    @IsOptional()
    memberWeights?: Record<string, number>;
}

export class CreateKpiMemberDto {
    @ApiProperty({ example: 'MEMBER' })
    @IsEnum(NSKPI.ESubjectType)
    subjectType: NSKPI.ESubjectType;

    @ApiProperty({ example: 'MONTH' })
    @IsEnum(NSKPI.EPeriodType)
    periodType: NSKPI.EPeriodType;

    @ApiProperty({ example: '2025-11-01T00:00:00.000Z' })
    @IsString()
    periodStart: string;

    @ApiProperty({ example: '2025-11-30T23:59:59.999Z' })
    @IsString()
    periodEnd: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    note?: string;

    @ApiPropertyOptional({ example: ['department-id-1', 'department-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    departmentIds?: string[];

    @ApiPropertyOptional({ example: ['role-id-1', 'role-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    roleIds?: string[];

    @ApiPropertyOptional({ example: ['member-id-1', 'member-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    memberIds?: string[];

    @ApiProperty({ type: [KpiMemberItemInputDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => KpiMemberItemInputDto)
    items: KpiMemberItemInputDto[];
}

export class UpdateKpiMemberDto {
    @ApiProperty({ example: 'MEMBER' })
    @IsEnum(NSKPI.ESubjectType)
    subjectType: NSKPI.ESubjectType;

    @ApiProperty({ example: 'MONTH' })
    @IsEnum(NSKPI.EPeriodType)
    periodType: NSKPI.EPeriodType;

    @ApiProperty({ example: '2025-11-01T00:00:00.000Z' })
    @IsString()
    periodStart: string;

    @ApiProperty({ example: '2025-11-30T23:59:59.999Z' })
    @IsString()
    periodEnd: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    title?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    note?: string;

    @ApiPropertyOptional({ example: 'member-id' })
    @IsOptional()
    @IsString()
    memberId?: string;

    @ApiPropertyOptional({ example: ['member-id-1', 'member-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    memberIds?: string[];

    @ApiPropertyOptional({ example: ['department-id-1', 'department-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    departmentIds?: string[];

    @ApiPropertyOptional({ example: ['role-id-1', 'role-id-2'] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    roleIds?: string[];

    @ApiProperty({ type: [KpiMemberItemInputDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => KpiMemberItemInputDto)
    items: KpiMemberItemInputDto[];
}

export class GetKpiMemberListQueryDto {
    @ApiPropertyOptional({ example: 'MEMBER' })
    @IsOptional()
    @IsEnum(NSKPI.ESubjectType)
    subjectType?: NSKPI.ESubjectType;

    @ApiPropertyOptional({ example: 'member-id' })
    @IsOptional()
    @IsString()
    memberId?: string;

    @ApiPropertyOptional({ example: 'MONTH' })
    @IsOptional()
    @IsEnum(NSKPI.EPeriodType)
    periodType?: NSKPI.EPeriodType;

    @ApiPropertyOptional({ example: '2025-11-01T00:00:00.000Z' })
    @IsOptional()
    @IsString()
    periodStart?: string;

    @ApiPropertyOptional({ example: '2025-11-30T23:59:59.999Z' })
    @IsOptional()
    @IsString()
    periodEnd?: string;
}

export class GetActualsDto {
    @ApiProperty({ example: 'MEMBER' })
    @IsEnum(NSKPI.ESubjectType)
    subjectType: NSKPI.ESubjectType;

    @ApiProperty({ example: 'member-id' })
    @IsString()
    memberId: string;

    @ApiProperty({ example: '2025-11-01T00:00:00.000Z' })
    @IsString()
    periodStart: string;

    @ApiProperty({ example: '2025-11-30T23:59:59.999Z' })
    @IsString()
    periodEnd: string;

    @ApiProperty({ example: ['CUSTOMERS_COUNT', 'CONTRACTS_COUNT'] })
    @IsArray()
    @IsEnum(NSKPI.ECategoryKey, { each: true })
    categoryKeys: NSKPI.ECategoryKey[];
}

export class UpdateStatusKpiMemberDto {
    @ApiProperty({ example: 'SENT' })
    @IsEnum(NSKPI.ETargetStatus)
    status: NSKPI.ETargetStatus;
}

export class ReportAverageQueryDto {
    @ApiProperty({ example: '2025-11-01T00:00:00.000Z' })
    @IsString()
    periodStart: string;

    @ApiProperty({ example: '2025-11-30T23:59:59.999Z' })
    @IsString()
    periodEnd: string;

    @ApiPropertyOptional({ example: 'MEMBER' })
    @IsOptional()
    @IsEnum(NSKPI.ESubjectType)
    subjectType?: NSKPI.ESubjectType;

    @ApiPropertyOptional({ example: 10 })
    @IsOptional()
    @Type(() => Number)
    limit?: number;
}
