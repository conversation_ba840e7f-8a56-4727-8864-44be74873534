import { CacheInterceptor } from '@nestjs/cache-manager';
import { Query, UseInterceptors } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { CommunesService } from './communes.service';
import { IPaginationCommunes } from './dto/communes.dto';

@DefController('communes')
@UseInterceptors(CacheInterceptor)
export class CommunesController {
    constructor(private readonly communesService: CommunesService) {}

    @DefGet('pagination')
    pagination(@Query() query: IPaginationCommunes) {
        return this.communesService.pagination(query);
    }
    @DefGet('get-communes-by-code')
    getCommunesByCode(@Query('code') codes: string) {
        return this.communesService.getCommunesByCode(codes);
    }
}
