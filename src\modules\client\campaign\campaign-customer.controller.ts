import { Query } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { CampaignCustomerService } from './campaign-customer.service';
import { ListCampaignCustomerDto } from './dto/campaign-customer.dto';

@DefController('campaign-customer')
export class CampaignCustomerController {
    constructor(private readonly campaignCustomerService: CampaignCustomerService) {}

    @DefGet('list')
    async list(@Query() query: ListCampaignCustomerDto) {
        return this.campaignCustomerService.list(query);
    }
}
