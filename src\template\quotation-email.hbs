<html lang='vi'>
    <head>
        <meta charset='utf-8' />
        <meta name='viewport' content='width=device-width, initial-scale=1.0' />
        <title>Quotation {{quotationNumber}}</title>
        <link
            href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
            rel='stylesheet'
        />
        <style>
            /* Reset styles */
            body {
                margin: 0;
                padding: 0;
                min-width: 100%;
                width: 100% !important;
                height: 100% !important;
                background-color: #f8fafc;
                font-family: 'Inter', Helvetica, Arial, sans-serif;
                color: #1e293b;
                line-height: 1.5;
            }
            table {
                border-spacing: 0;
                border-collapse: collapse;
            }
            td {
                padding: 0;
                vertical-align: top;
            }
            img {
                border: 0;
                outline: none;
                text-decoration: none;
                -ms-interpolation-mode: bicubic;
                display: block;
            }

            /* Container */
            .email-wrapper {
                width: 100%;
                background-color: #f8fafc;
                padding: 40px 10px;
            }
            .email-container {
                max-width: 800px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 16px;
                overflow: hidden;
                box-shadow:
                    0 4px 6px -1px rgba(0, 0, 0, 0.1),
                    0 2px 4px -1px rgba(0, 0, 0, 0.06);
                border: 1px solid #e2e8f0;
            }

            /* Content Sections */
            .header-td {
                padding: 32px;
                border-bottom: 1px solid #e2e8f0;
            }
            .info-td {
                padding: 32px;
                background-color: #f8fafc;
                border-bottom: 1px solid #e2e8f0;
            }
            .items-td {
                padding: 32px;
            }
            .summary-td {
                padding: 0 32px 32px 32px;
            }
            .footer-td {
                padding: 24px 32px;
                text-align: center;
                color: #94a3b8;
                font-size: 13px;
                background-color: #ffffff;
                border-top: 1px solid #e2e8f0;
            }

            /* Typography */
            h1 {
                margin: 0;
                font-size: 20px;
                font-weight: 700;
                color: #0f172a;
            }
            h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 700;
                color: #0f172a;
            }
            h3 {
                margin: 0 0 8px 0;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                color: #64748b;
            }
            p {
                margin: 0 0 4px 0;
                font-size: 14px;
                color: #334155;
            }
            .text-sm {
                font-size: 13px;
                color: #64748b;
            }
            .text-bold {
                font-weight: 600;
                color: #0f172a;
            }

            /* Elements */
            .badge {
                display: inline-block;
                padding: 4px 12px;
                background-color: #eff6ff;
                color: #1d4ed8;
                font-size: 11px;
                font-weight: 700;
                border-radius: 9999px;
                text-transform: uppercase;
                margin-bottom: 8px;
            }
            .logo-box {
                width: 48px;
                height: 48px;
                background-color: #2563eb;
                border-radius: 8px;
                text-align: center;
                line-height: 48px;
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                margin-right: 16px;
            }

            /* Tables */
            .items-table {
                width: 100%;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                overflow: hidden;
            }
            .items-table th {
                background-color: #f1f5f9;
                padding: 12px 16px;
                text-align: left;
                font-size: 12px;
                font-weight: 600;
                color: #475569;
                text-transform: uppercase;
                border-bottom: 1px solid #e2e8f0;
            }
            .items-table td {
                padding: 16px;
                border-bottom: 1px solid #e2e8f0;
                font-size: 14px;
                color: #334155;
            }
            .items-table tr:last-child td {
                border-bottom: none;
            }

            .summary-table {
                width: 100%;
                max-width: 350px;
                margin-left: auto;
            }
            .summary-table td {
                padding: 6px 0;
                font-size: 14px;
                color: #475569;
            }
            .summary-table .amount {
                text-align: right;
                font-weight: 600;
                color: #0f172a;
            }
            .summary-table .total-row td {
                padding-top: 12px;
                border-top: 1px solid #e2e8f0;
                font-size: 16px;
                font-weight: 700;
                color: #0f172a;
            }
            .summary-table .total-row .amount {
                color: #2563eb;
                font-size: 20px;
            }

            /* Helpers */
            .text-right {
                text-align: right;
            }
            .text-center {
                text-align: center;
            }
            .w-half {
                width: 50%;
            }

            /* Mobile Responsive */
            @media only screen and (max-width: 600px) {
                .email-container {
                    width: 100% !important;
                }
                .stack-column {
                    display: block !important;
                    width: 100% !important;
                    padding-bottom: 20px;
                }
                .stack-column.last {
                    padding-bottom: 0;
                }
                .header-content {
                    display: block !important;
                    text-align: center !important;
                }
                .header-right {
                    margin-top: 20px;
                    text-align: center !important;
                }
                .logo-box {
                    margin: 0 auto 10px auto;
                }
                .summary-table {
                    max-width: 100% !important;
                }
            }
        </style>
    </head>
    <body>
        <div class='email-wrapper'>
            <table
                class='email-container'
                role='presentation'
                cellpadding='0'
                cellspacing='0'
                width='100%'
            >
                <!-- HEADER -->
                <tr>
                    <td class='header-td'>
                        <table width='100%' cellpadding='0' cellspacing='0'>
                            <tr>
                                <!-- Brand -->
                                <td
                                    class='stack-column'
                                    width='60%'
                                    style='vertical-align: middle;'
                                >
                                    <table cellpadding='0' cellspacing='0'>
                                        <tr>
                                            <td style='padding-right: 16px;'>
                                                {{#if logoSystem}}
                                                    <img
                                                        src='{{logoSystem}}'
                                                        alt='Company Logo'
                                                        width='48'
                                                        height='48'
                                                        style='width: 48px; height: 48px; border-radius: 8px; object-fit: contain; display: block;'
                                                    />
                                                {{else}}
                                                    <div class='logo-box'>APE CRM</div>
                                                {{/if}}
                                            </td>
                                            <td>
                                                <h1>{{memberName}}</h1>
                                                <p
                                                    class='text-sm'
                                                    style='margin-top: 2px;'
                                                >{{companyTagline}}</p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <!-- Quote Info -->
                                <td
                                    class='stack-column last header-right'
                                    width='40%'
                                    style='vertical-align: middle; text-align: right;'
                                >
                                    <span class='badge'>Quotation</span>
                                    <h2>#{{quotationNumber}}</h2>
                                    <p class='text-sm' style='margin-top: 4px;'>Issued:
                                        {{quotationDate}}</p>
                                    <p class='text-sm'>Delivery: {{deliveryDate}}</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

                <!-- FROM / BILL TO -->
                <tr>
                    <td class='info-td'>
                        <table width='100%' cellpadding='0' cellspacing='0'>
                            <tr>
                                <td class='stack-column' width='50%' style='padding-right: 20px;'>
                                    <h3>From</h3>
                                    <p class='text-bold'>{{memberName}}</p>
                                    <p>{{companyDescription}}</p>

                                </td>
                                <td class='stack-column last' width='50%'>
                                    <h3>Bill To</h3>
                                    <p class='text-bold'>{{customerName}}</p>
                                    <p> Address: {{customerAddress}}</p>
                                    <p>Email: {{customerEmail}}</p>
                                    <p>Tax code: {{customerTaxCode}}</p>
                                    <p>Contact: {{contactName}}</p>
                                    <p>Phone Contact: {{contactPhone}}</p>
                                    <p>Email Contact: {{contactEmail}}</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

                <!-- ITEMS -->
                <tr>
                    <td class='items-td'>
                        <table class='items-table' width='100%' cellpadding='0' cellspacing='0'>
                            <thead>
                                <tr>
                                    <th width='45%'>Description</th>
                                    <th width='15%' class='text-center'>Qty</th>
                                    <th width='20%' class='text-right'>Unit Price</th>
                                    <th width='20%' class='text-right'>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each items}}
                                    <tr>
                                        <td><strong>{{name}}</strong></td>
                                        <td class='text-center'>{{quantity}}</td>
                                        <td class='text-right'>{{unitPrice}}</td>
                                        <td class='text-right'><strong>{{total}}</strong></td>
                                    </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </td>
                </tr>

                <!-- SUMMARY -->
                <tr>
                    <td class='summary-td'>
                        <table width='100%' cellpadding='0' cellspacing='0'>
                            <tr>
                                <td align='right'>
                                    <table class='summary-table' cellpadding='0' cellspacing='0'>
                                        <tr>
                                            <td>Subtotal</td>
                                            <td class='amount'>{{subtotal}}</td>
                                        </tr>
                                        <tr>
                                            <td>VAT ({{vatPercent}}%)</td>
                                            <td class='amount'>{{vatTotal}}</td>
                                        </tr>
                                        <tr class='total-row'>
                                            <td>Total Amount</td>
                                            <td class='amount'>{{totalAmount}}</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

                <!-- FOOTER -->
                <tr>
                    <td class='footer-td'>
                        <p>Valid for {{validityDays}} days &bull; Payment term: {{dueDate}}</p>
                        <p style='margin-top: 8px;'>Thank you for your business!</p>
                    </td>
                </tr>
            </table>
        </div>
    </body>
</html>