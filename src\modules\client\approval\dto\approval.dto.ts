// approval.dto.ts
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUUID,
    MaxLength,
    Min,
    ValidateNested,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSApproval } from '~/common/enums/approval.enum';

// DTO lấy danh sách
export class GetApprovalConfigDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Mã cấu hình duyệt', example: 'HR_LEAVE_DEFAULT' })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    code?: string;

    @ApiPropertyOptional({
        description: 'Tên cấu hình duyệt',
        example: 'Cấu hình duyệt đơn nghỉ phép chuẩn',
    })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    name?: string;

    @ApiPropertyOptional({
        description: '<PERSON>hiệ<PERSON> vụ áp dụng cấu hình (LEAVE_REQUEST, OVERTIME_REQUEST, ...)',
        example: 'LEAVE_REQUEST',
    })
    @IsOptional()
    @IsString()
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType?: NSApproval.ApprovalBusinessType;

    @ApiPropertyOptional({ description: 'Trạng thái kích hoạt', default: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

// DTO Approver cho từng step
export class ApprovalStepApproverDto {
    @ApiProperty({
        enum: NSApproval.ApproverType,
        description: 'Kiểu đối tượng duyệt: MEMBER / ROLE / POSITION / DEPARTMENT',
    })
    @IsEnum(NSApproval.ApproverType)
    approverType: NSApproval.ApproverType;

    // Tách biệt RefId cho từng loại (chỉ 1 trong các RefId có giá trị)
    @ApiPropertyOptional({ description: 'ID member (khi approverType = MEMBER)' })
    @IsOptional()
    @IsUUID('all', { each: true })
    memberRefId?: string;

    @ApiPropertyOptional({ description: 'ID position (khi approverType = POSITION)' })
    @IsOptional()
    @IsUUID('all', { each: true })
    positionRefId?: string;

    @ApiPropertyOptional({ description: 'ID role (khi approverType = ROLE)' })
    @IsOptional()
    @IsUUID('all', { each: true })
    roleRefId?: string;

    @ApiPropertyOptional({
        description:
            'ID department (khi approverType = DEPARTMENT hoặc để filter Position trong Department)',
    })
    @IsOptional()
    @IsUUID('all', { each: true })
    departmentRefId?: string;

    @ApiPropertyOptional({
        description:
            'Nếu true, departmentRefId sẽ resolve từ department của người tạo request (dùng cho POSITION + DEPARTMENT dynamic)',
        default: false,
    })
    @IsOptional()
    @IsBoolean()
    useCreatorDepartment?: boolean;

    // Deprecated: Giữ lại để backward compatibility, sẽ map sang các RefId tương ứng
    @ApiPropertyOptional({
        description:
            'Id tham chiếu (deprecated, dùng memberRefId/positionRefId/roleRefId/departmentRefId thay thế)',
        maxLength: 50,
    })
    @IsOptional()
    @IsString({ each: true })
    @MaxLength(50, { each: true })
    approverRefId?: string;
}

// DTO Step (cấp duyệt)
export class ApprovalStepDto {
    @ApiProperty({ description: 'Thứ tự duyệt (1, 2, 3...)', example: 1 })
    @IsInt()
    @Min(1)
    order: number;

    @ApiProperty({ description: 'Tiêu đề hiển thị cấp duyệt', example: 'Trưởng phòng duyệt' })
    @IsNotEmpty({ message: 'Tiêu đề hiển thị cấp duyệt không được để trống' })
    @IsString()
    @MaxLength(255)
    title: string;

    @ApiProperty({
        enum: NSApproval.ApprovalLevelType,
        description: 'Kiểu duyệt: SINGLE / ANY / ALL',
    })
    @IsEnum(NSApproval.ApprovalLevelType)
    @IsNotEmpty({ message: 'Kiểu duyệt không được để trống' })
    approvalLevelType: NSApproval.ApprovalLevelType;

    @ApiPropertyOptional({
        description: 'Số lượng người tối thiểu cần duyệt (dùng cho ANY nâng cao)',
        example: 1,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    minApprovers?: number;

    @ApiPropertyOptional({
        description: 'Ghi chú cho cấp duyệt',
        example: 'Duyệt khi số tiền > 50,000,000',
    })
    @IsOptional()
    @IsString()
    note?: string;

    @ApiPropertyOptional({
        description: 'Thời gian dự kiến để hoàn thành bước duyệt (tính bằng giờ)',
        example: 24,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    leadtime?: number;

    @ApiProperty({
        description: 'Danh sách người/role/position được duyệt ở cấp này',
        type: [ApprovalStepApproverDto],
    })
    @IsNotEmpty({ message: 'Danh sách người duyệt không được để trống' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ApprovalStepApproverDto)
    approvers: ApprovalStepApproverDto[];
}

// DTO tạo mới cấu hình
export class CreateApprovalConfigDto {
    @ApiProperty({ description: 'Mã cấu hình duyệt', example: 'HR_LEAVE_DEFAULT' })
    @IsNotEmpty()
    @IsString()
    @MaxLength(50)
    code: string;

    @ApiProperty({
        description: 'Tên cấu hình duyệt',
        example: 'Cấu hình duyệt đơn nghỉ phép chuẩn',
    })
    @IsNotEmpty()
    @IsString()
    @MaxLength(255)
    name: string;

    @ApiProperty({
        enum: NSApproval.ApprovalBusinessType,
        description: 'Nghiệp vụ áp dụng cấu hình (LEAVE_REQUEST, OVERTIME_REQUEST, ...)',
    })
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType: NSApproval.ApprovalBusinessType;

    @ApiPropertyOptional({ description: 'Mô tả cấu hình duyệt' })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiPropertyOptional({ description: 'Trạng thái kích hoạt', default: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional({ description: 'Là cấu hình mặc định', default: false })
    @IsOptional()
    @IsBoolean()
    isDefault?: boolean;

    @ApiProperty({
        description: 'Danh sách các cấp duyệt',
        type: [ApprovalStepDto],
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ApprovalStepDto)
    steps: ApprovalStepDto[];
}

// DTO update cấu hình
export class UpdateApprovalConfigDto extends PartialType(CreateApprovalConfigDto) {
    @ApiProperty({ description: 'Id cấu hình duyệt', example: 'uuid' })
    @IsNotEmpty()
    @IsString()
    id: string;
}

// DTO delete cấu hình
export class IdApprovalConfigDto {
    @ApiProperty({ description: 'Id cấu hình duyệt', example: 'uuid' })
    @IsNotEmpty()
    @IsString()
    id: string;
}

// DTO Mapping

export class GetApprovalConfigMappingDto extends PageRequest {
    @ApiProperty({
        description: 'Nghiệp vụ áp dụng cấu hình (LEAVE_REQUEST, OVERTIME_REQUEST, ...)',
        example: 'LEAVE_REQUEST',
    })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType?: NSApproval.ApprovalBusinessType;

    @ApiPropertyOptional({ description: 'Id cấu hình duyệt', example: 'uuid' })
    @IsOptional()
    @IsString()
    approvalConfigId?: string;
}
export class CreateApprovalConfigMappingDto {
    @ApiProperty({ description: 'Id cấu hình duyệt', example: 'uuid' })
    @IsNotEmpty()
    @IsString()
    approvalConfigId: string;

    @ApiProperty({
        description: 'Nghiệp vụ áp dụng cấu hình (LEAVE_REQUEST, OVERTIME_REQUEST, ...)',
        example: 'LEAVE_REQUEST',
    })
    @IsNotEmpty()
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType: NSApproval.ApprovalBusinessType;

    @ApiPropertyOptional({ description: 'Trạng thái mặc định', default: true })
    @IsOptional()
    @IsBoolean()
    isDefault?: boolean;
}

export class UpdateApprovalConfigMappingDto extends PartialType(CreateApprovalConfigMappingDto) {
    @ApiProperty({ description: 'Id cấu hình duyệt', example: 'uuid' })
    @IsNotEmpty()
    @IsString()
    id: string;
}
