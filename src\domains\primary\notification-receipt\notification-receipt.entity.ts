import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSNotification } from '~/common/enums/notification.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('notification_receipts')
export class NotificationReceiptEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Index()
    @Column({ type: 'uuid' })
    notificationId: string;

    @ApiProperty({ example: NSNotification.ERecipientType.MEMBER })
    @Column({ type: 'enum', enum: NSNotification.ERecipientType })
    recipientType: NSNotification.ERecipientType;

    @ApiPropertyOptional()
    @Index()
    @Column({ type: 'uuid', nullable: true })
    memberId?: string;

    @ApiPropertyOptional()
    @Index()
    @Column({ type: 'uuid', nullable: true })
    roleId?: string;

    @ApiPropertyOptional()
    @Index()
    @Column({ type: 'uuid', nullable: true })
    departmentId?: string;

    @ApiProperty({ example: NSNotification.EDeliveryStatus.PENDING })
    @Index()
    @Column({
        type: 'enum',
        enum: NSNotification.EDeliveryStatus,
        default: NSNotification.EDeliveryStatus.PENDING,
    })
    deliveryStatus: NSNotification.EDeliveryStatus;

    @ApiPropertyOptional()
    @Column({ type: 'timestamptz', nullable: true })
    deliveredAt?: Date;

    @ApiProperty({ example: false })
    @Index()
    @Column({ type: 'boolean', default: false })
    isRead: boolean;

    @ApiPropertyOptional()
    @Column({ type: 'timestamptz', nullable: true })
    readAt?: Date;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 1000, nullable: true })
    lastError?: string;
}
