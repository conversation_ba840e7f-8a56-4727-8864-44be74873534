import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { configEnv } from '~/@config/env';
import { GUARD_CODE } from './client-auth.guard';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { MemberRepo } from '~/domains/primary';

@Injectable()
export class ClientAuthStrategy extends PassportStrategy(Strategy, GUARD_CODE) {
    constructor(@InjectRepo(MemberRepo) private memberRepo: MemberRepo) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configEnv().JWT_SECRET,
        });
    }

    async validate(payload: any) {
        console.log(`=====CLIENT AUTH STRATEGY=====`, payload);
        return payload;
    }
}
