// src/domains/crm/catalog-service.entity.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { CurrencyCode, NSCatalog } from '~/common/enums';

// Danh mục
@Entity('catalog')
export class CatalogServiceEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'SRV-SEO-MONTHLY' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    code: string;

    @ApiProperty({ example: 'Gói SEO theo tháng' })
    @Column({ type: 'varchar', length: 255 })
    @Index()
    name: string;

    @ApiProperty({ example: NSCatalog.EType.SERVICE })
    @Column({ type: 'varchar', length: 64, default: NSCatalog.EType.SERVICE })
    type: NSCatalog.EType;

    @ApiPropertyOptional({ example: 'Tối ưu SEO onpage + offpage' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @ApiPropertyOptional({ example: 'tháng' })
    @Column({ type: 'varchar', length: 64, nullable: true })
    unit?: string | null;

    @ApiProperty({ example: 'VND', enum: CurrencyCode })
    @Column({ type: 'varchar', length: 10, default: CurrencyCode.VND })
    currency: CurrencyCode;

    @ApiProperty({ example: 15000000 })
    @Column({ type: 'numeric', precision: 20, scale: 0, nullable: true, default: 0 })
    unitPrice: string; // dùng string để tránh lỗi precision với numeric

    @ApiPropertyOptional({ example: 8.0, description: 'Thuế %' })
    @Column({ type: 'numeric', precision: 5, scale: 2, default: 0, nullable: true })
    taxRate?: string; // phần trăm (%)

    //hình ảnh
    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    images?: string;

    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    attachments?: string;

    @ApiProperty({ example: NSCatalog.EStatus.ACTIVE })
    @Column({ type: 'varchar', length: 64, default: NSCatalog.EStatus.ACTIVE })
    status: NSCatalog.EStatus;
}
