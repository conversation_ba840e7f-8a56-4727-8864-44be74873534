// src/domains/crm/catalog-service.entity.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSCatalog } from '~/common/enums';

// Danh mục catalog category
@Entity('catalog')
export class  CatalogServiceEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'SRV-SEO-MONTHLY' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    code: string;

    @ApiProperty({ example: 'Gói SEO theo tháng' })
    @Column({ type: 'varchar', length: 255 })
    @Index()
    name: string;

    @ApiPropertyOptional({ example: 'Tối ưu SEO onpage + offpage' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    images?: string;

    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    attachments?: string;

    @ApiProperty({ example: NSCatalog.EStatus.ACTIVE })
    @Column({ type: 'varchar', length: 64, default: NSCatalog.EStatus.ACTIVE })
    status: NSCatalog.EStatus;
}
