import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListCustomerAddressDto extends PageRequest {
    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsNotEmpty()
    @IsString()
    customerId: string;

    @ApiPropertyOptional({ example: 'AD-0001', description: 'Tên địa chỉ' })
    @IsOptional()
    @IsString()
    addressName?: string;

    @ApiPropertyOptional({ example: '456 Đường XYZ, Quận 2, TP.HCM', description: 'Địa chỉ' })
    @IsOptional()
    @IsString()
    address?: string;

    @ApiPropertyOptional({ example: '79', description: 'Mã tỉnh/thành phố' })
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional({ example: '769', description: 'Mã quận/huyện' })
    @IsOptional()
    @IsString()
    districtCode?: string;

    @ApiPropertyOptional({ example: '27134', description: 'Mã phường/xã' })
    @IsOptional()
    @IsString()
    wardCode?: string;

    @ApiPropertyOptional({ example: 'Miền Bắc', description: 'Mã vùng' })
    @IsOptional()
    @IsString()
    regionCode?: string;

    @ApiPropertyOptional({ example: true, description: 'Trạng thái' })
    @IsOptional()
    isActive?: boolean;
}
