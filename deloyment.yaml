#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ape-crm-api-dev-config
  namespace: ape-crm-dev
data:
  NODE_ENV: 'production'
  PORT: '80'
  TZ: 'UTC'
  REQUEST_TIMEOUT: '180000'
  APPLICATION_CODE: 'crm'
  #Swagger Config
  SWAGGER_TITLE: 'APE CRM API'
  SWAGGER_DESCRIPTION: 'The APE CRM API'
  SWAGGER_VERSION: '1.0'
  # Primary Database
  DB_PRIMARY_TYPE: 'postgres'
  DB_PRIMARY_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
  DB_PRIMARY_PORT: '5432'
  DB_PRIMARY_USERNAME: 'ape-crm-dev'
  DB_PRIMARY_PASSWORD: 'Ac#23pehA2in'
  DB_PRIMARY_DATABASE: 'ape-crm-dev'
  DB_PRIMARY_SYNCHRONIZE: 'true'
  DB_PRIMARY_SSL: 'true'
  DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: 'false'
  # JWT HS256 config
  JWT_SECRET: '/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo: '
  JWT_EXPIRY: '100d'
  JWT_REFRESH_TOKEN_SECRET: '/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi: '
  JWT_REFRESH_TOKEN_EXPIRY: '300d'
  # APE SSO
  APE_SSO_URL: 'https://authenticator-dev.apetechs.co'
  APE_CLIENT_ID: 'CRM_f12880477d2f44eab09d0c1f6943ce61'
  APE_CLIENT_SECRET: '0527af904cf9842984a10bd013c71237c9960a9f1a651d2dbad5261c0456c80b'
  APE_CALLBACK_URL: 'https://crm-api-dev.apetechs.co/api/client/auth/ape/callback'
  APE_PUBLIC_API_KEY: 'de7ff0854c57ff1bcfa2269f898b93883b8c27b26b3c82cc57c76388555a50b0'

  #S3
  LINK_UPLOAD_S3: 'ape-bl-dev'
  AWS_S3_BUCKET_NAME: 'ape-devs-co'
  AWS_S3_ACCESS_KEY_ID: '********************'
  AWS_S3_SECRET_ACCESS_KEY: '/DIKQa//iyYZUvucHau/cRItB+LCkQ76XWspfrcO'
  #Mail
  SES_HOST: 'email-smtp.ap-southeast-1.amazonaws.com'
  SES_PORT: '587'
  SES_USER: '********************'
  SES_PASS: 'BLiynchkXmPPzmpv6oBQ6Q0QoWR6t5vUCthIjoHWfU8q'
  SES_FROM: '<EMAIL>'

  # API Key để gọi qua APE SSO
  APE_CRM_API_KEY: '5c587758d88d68ebcfb324bd277d930080944e15b48446988dc3fd82764e6a62'
  # API INTEGRATION cho Schedule gọi qua
  SCHEDULE_CRM_KEY: 'e46dc3de-5334-475c-ad1f-01ce61f1b9ca'
---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-crm-api-dev
  namespace: ape-crm-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-crm-api-dev
  template:
    metadata:
      labels:
        app: ape-crm-api-dev
    spec:
      containers:
        - name: ape-crm-api-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-crm-api-dev:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: ape-crm-api-dev-config
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-crm-api-dev
  namespace: ape-crm-dev
  labels:
    run: ape-crm-api-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-crm-api-dev
