import { NSEventLogs } from '~/common/enums/event-logs.enum';

type Json = Record<string, any>;

const SENSITIVE_KEYS = ['password', 'token', 'secret', 'apiKey', 'accessKey', 'refreshToken'];

function isPlainObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function sanitizeValue(value: any): any {
    if (value instanceof Date) {
        const d = new Date(value);
        d.setHours(d.getHours() + 7);
        return d.toISOString().slice(0, 10);
    }
    if (isPlainObject(value)) {
        const out: Json = {};
        for (const k of Object.keys(value)) {
            if (SENSITIVE_KEYS.includes(k)) continue;
            out[k] = sanitizeValue(value[k]);
        }
        return out;
    }
    return value;
}

export function sanitizeObject<T extends Json>(obj: T): T {
    return sanitizeValue(obj) as T;
}

export function buildCreatedDetails(after: Json): Json {
    return { after: sanitizeObject(after) };
}

export function buildDeletedDetails(snapshot: Json): Json {
    return { snapshot: sanitizeObject(snapshot) };
}

export function buildUpdatedDetails(before: Json, after: Json, trackKeys?: string[]): Json {
    const b = sanitizeObject(before || {});
    const a = sanitizeObject(after || {});
    const keys =
        trackKeys && trackKeys.length
            ? trackKeys
            : Array.from(new Set([...Object.keys(b), ...Object.keys(a)]));
    const beforeDiff: Json = {};
    const afterDiff: Json = {};
    for (const k of keys) {
        const bv = b[k];
        const av = a[k];
        const changed = JSON.stringify(bv) !== JSON.stringify(av);
        if (changed) {
            beforeDiff[k] = bv;
            afterDiff[k] = av;
        }
    }
    return { before: beforeDiff, after: afterDiff };
}

export function buildStatusDetails(
    fromStatus: string | number,
    toStatus: string | number,
    meta?: Json,
): Json {
    const details: Json = { fromStatus, toStatus };
    if (meta && Object.keys(meta).length) details.meta = sanitizeObject(meta);
    return details;
}

export const EventTypeBuilders: Record<NSEventLogs.EventTypes, (...args: any[]) => Json> = {
    [NSEventLogs.EventTypes.CREATED]: (after: Json) => buildCreatedDetails(after),
    [NSEventLogs.EventTypes.UPDATED]: (before: Json, after: Json, trackKeys?: string[]) =>
        buildUpdatedDetails(before, after, trackKeys),
    [NSEventLogs.EventTypes.DELETED]: (snapshot: Json) => buildDeletedDetails(snapshot),
    [NSEventLogs.EventTypes.STATUS_CHANGE]: (from: any, to: any, meta?: Json) =>
        buildStatusDetails(from, to, meta),
};
