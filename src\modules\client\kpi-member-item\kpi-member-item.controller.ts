import { Body, Param, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { KpiMemberItemService } from './kpi-member-item.service';
import { UpdateKpiMemberItemDto } from './dto/kpi-member-item.dto';

@UseGuards(PermissionGuard)
@DefController('kpi-member-item')
export class KpiMemberItemController {
    constructor(private readonly service: KpiMemberItemService) {}

    @DefGet(':targetId')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async listByTarget(@Param('targetId') targetId: string) {
        return this.service.listByTarget(targetId);
    }

    @DefPost(':targetId/:itemId')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async update(
        @Param('targetId') targetId: string,
        @Param('itemId') itemId: string,
        @Body() dto: UpdateKpiMemberItemDto,
    ) {
        return this.service.update(targetId, itemId, dto);
    }
}

