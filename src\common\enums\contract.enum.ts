export namespace NSContract {
    //#region Trạng thái
    export enum EStatus {
        NEW = 'NEW',
        SENT = 'SENT',
        APPROVED = 'APPROVED',
        REJECTED = 'REJECTED',
        SENT_TO_CUSTOMER = 'SENT_TO_CUSTOMER',
        SIGNED = 'SIGNED',
        CANCELLED = 'CANCELLED',
    }
    export enum EType {
        SALES = 'SALES',
        SERVICE = 'SERVICE',
        MAINTENANCE = 'MAINTENANCE',
        CONSULTATION = 'CONSULTATION',
        DISTRIBUTION = 'DISTRIBUTION',
        PURCHASE = 'PURCHASE',
    }
}
