import { Column, Entity, Index, OneToMany } from 'typeorm';

import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApprovalConfigStepEntity } from './approval-config-step.entity';
import { ApprovalRequestEntity } from './approval-request.entity';
import { NSApproval } from '~/common/enums/approval.enum';

@Entity('approval_config')
export class ApprovalConfigEntity extends PrimaryBaseEntity {
    // Mã cấu hình duyệt (unique trong cùng tenant)
    @Column({ type: 'varchar', length: 50 })
    @Index()
    code: string;

    // Tên cấu hình duyệt, hiển thị cho user
    @Column({ type: 'varchar', length: 255 })
    name: string;

    // Nghiệp vụ áp dụng: PO, Phiếu đề nghị, Đơn xin nghỉ, ...
    @Column({ type: 'enum', enum: NSApproval.ApprovalBusinessType })
    businessType: string;

    // Mô tả chi tiết (tùy chọn)
    @Column({ type: 'text', nullable: true })
    description?: string;

    // Bật/tắt cấu hình
    @Column({ type: 'boolean', default: true })
    isActive: boolean;

    @OneToMany(() => ApprovalConfigStepEntity, step => step.config)
    steps: ApprovalConfigStepEntity[];

    @Column({ type: 'varchar', length: 100, nullable: true })
    groupKey: string;

    @OneToMany(() => ApprovalRequestEntity, request => request.approvalConfig, {
        onDelete: 'CASCADE',
    })
    requests: ApprovalRequestEntity[];
}
