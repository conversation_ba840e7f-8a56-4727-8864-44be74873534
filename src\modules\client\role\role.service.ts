import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSRole } from '~/common/enums';
import { apeAuthApiConnector } from '~/connectors/api.connector';
import { MemberRepo, RoleRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';
import { CreateRoleDto, ListRoleDto, UpdateRoleDto } from './dto';

@Injectable()
export class RoleService {
    constructor(
        @InjectRepo(RoleRepo) private roleRepo: RoleRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
    ) {}

    async list(params: ListRoleDto) {
        const where: any = {};
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }
        if (params.status) {
            where.status = params.status;
        }
        let res: any = {};
        if (params.pageSize === -1) {
            const [data, total] = await this.roleRepo.findAndCount({
                where: { tenantId: clientSessionContext.tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            res = {
                data,
                total,
            };
        } else {
            res = await this.roleRepo.findPaginationByTenant(
                {
                    where,
                    order: {
                        createdDate: 'DESC',
                    },
                },
                {
                    pageIndex: params.pageIndex,
                    pageSize: params.pageSize,
                },
            );
        }
        return res;
    }

    // Lấy danh sách role theo tenantId và app code là WMS
    async listPermission() {
        const { tenantId } = clientSessionContext;
        const userAuth = await apeAuthApiConnector.post(`/api/public/crm/member/get-tenant-role`, {
            tenantId,
            applicationCode: 'CRM',
        });
        return userAuth;
    }

    async detail(id: string) {
        const check = await this.roleRepo.findOneByTenant({
            where: { id },
        });
        if (!check) {
            throw new BusinessException('Role not found');
        }
        return check;
    }

    @DefTransaction()
    async create(data: CreateRoleDto) {
        try {
            const roleToCreate = this.roleRepo.create({
                ...data,
                permissionCodes: data.permissionCodes || [],
            });

            return this.roleRepo.save(roleToCreate);
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }

    @DefTransaction()
    async update(data: UpdateRoleDto) {
        const { tenantId } = clientSessionContext;
        const { id, permissionCodes, memberIds, ...dataItem } = data;
        const check = await this.detail(id);
        if (!check) {
            throw new BusinessException('Role not found');
        }

        // Cập nhật danh sách quyền nếu có
        if (permissionCodes !== undefined) {
            check.permissionCodes = permissionCodes;
        }

        // Cập nhật danh sách thành viên nếu có
        if (memberIds !== undefined) {
            const members = await this.memberRepo.find({
                where: { id: In(memberIds), tenantId },
            });
            if (members.length !== memberIds.length) {
                throw new BusinessException('One or more members not found');
            }
        }

        Object.assign(check, dataItem);
        return this.roleRepo.update(id, { ...check, id });
    }

    @DefTransaction()
    async active(id: string) {
        const check = await this.roleRepo.findOneByTenant({ where: { id } });
        if (!check) {
            throw new BusinessException('Role not found');
        }
        const updatedRole = await this.roleRepo.update(id, {
            status: NSRole.EStatus.ACTIVE,
            updatedDate: new Date(),
            id,
        });
        return updatedRole;
    }

    @DefTransaction()
    async inActive(id: string) {
        const check = await this.roleRepo.findOneByTenant({ where: { id } });
        if (!check) {
            throw new BusinessException('Role not found');
        }
        const updatedRole = await this.roleRepo.update(id, {
            status: NSRole.EStatus.INACTIVE,
            updatedDate: new Date(),
            id,
        });
        return updatedRole;
    }
}
