import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('email')
export class EmailEntity extends PrimaryBaseEntity {
    @Column({ type: 'varchar', nullable: true })
    email: string;

    @Column({ type: 'varchar', nullable: true })
    host: string;

    @Column({ type: 'varchar', nullable: true })
    port: string;

    @Column({ type: 'varchar', nullable: true })
    username?: string;

    @Column({ type: 'varchar', nullable: true })
    password?: string;
}
