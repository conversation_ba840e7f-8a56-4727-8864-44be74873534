import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { NSMember } from '~/common/enums';

export class ApeSsoUserInfo {
    @ApiProperty()
    id: string;

    @ApiProperty()
    username: string;

    @ApiProperty()
    fullName: string;

    @ApiProperty()
    avatar: string;

    @ApiProperty()
    tenantId: string;

    @ApiProperty()
    type: NSMember.EType;
}

export class ApeSsoUserInfoSession extends ApeSsoUserInfo {
    @ApiPropertyOptional()
    redirectUri?: string;
}
