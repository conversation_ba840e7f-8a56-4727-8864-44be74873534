/**
 * AlertEntity - Qu<PERSON>n lý cảnh báo trong hệ thống
 *
 * Bảng này lưu trữ các cảnh báo về bảo tr<PERSON>, b<PERSON><PERSON> hành, kh<PERSON><PERSON> hao, ki<PERSON><PERSON> kê, <PERSON>hân bổ, v.v.
 * Mỗi cảnh báo có mức độ nghiêm trọng, loại cảnh báo và liên kết với entity liên quan.
 */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { NSAlert } from '~/common/enums/alert.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('alerts')
export class AlertEntity extends PrimaryBaseEntity {
    /**
     * Loại cảnh báo
     */
    @ApiProperty({ example: NSAlert.EType.COMPLAINT_NEW })
    @Column({ type: 'enum', enum: NSAlert.EType })
    type: NSAlert.EType;

    /**
     * <PERSON><PERSON><PERSON> độ nghiêm trọng
     * CRITICAL, HIGH, MEDIUM, LOW
     */
    @ApiProperty({ example: NSAlert.ESeverity.HIGH })
    @Column({ type: 'enum', enum: NSAlert.ESeverity })
    severity: NSAlert.ESeverity;

    /**
     * Tiêu đề cảnh báo
     */
    @ApiProperty({ example: 'Bảo trì quá hạn' })
    @Column({ type: 'varchar', length: 255 })
    title: string;

    /**
     * Mô tả chi tiết cảnh báo
     */
    @ApiProperty({ example: 'Tài sản SN-ABC-12345 đã quá hạn bảo trì 5 ngày' })
    @Column({ type: 'text' })
    description: string;

    //  * ID của khiếu nại liên quan
    //  */
    @ApiPropertyOptional()
    @Column({ type: 'uuid', nullable: true })
    documentId?: string;

    /**
     * ID khách hàng liên quan đến khiếu nại
     */
    @ApiPropertyOptional()
    @Column({ type: 'uuid', nullable: true })
    customerId?: string;

    /**
     * Nguồn phát sinh cảnh báo
     */
    @ApiPropertyOptional({ example: NSAlert.ESource.COMPLAINT_ALERT })
    @Column({ type: 'enum', enum: NSAlert.ESource, nullable: true })
    source?: NSAlert.ESource;

    /**
     * Ngày đến hạn/hết hạn
     * Ngày quan trọng liên quan đến cảnh báo (ngày bảo trì, ngày hết hạn bảo hành, v.v.)
     */
    @ApiPropertyOptional()
    @Column({ type: 'timestamptz', nullable: true })
    dueDate?: Date;

    /**
     * Đã đọc chưa
     * Đánh dấu cảnh báo đã được người dùng đọc
     */
    @ApiProperty({ example: false })
    @Column({ type: 'boolean', default: false })
    isRead: boolean;

    /**
     * Metadata bổ sung
     * Lưu trữ thông tin bổ sung dưới dạng JSON
     */
    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    metadata?: Record<string, any>;
}
