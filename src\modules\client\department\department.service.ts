import { Injectable } from '@nestjs/common';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSDepartment } from '~/common/enums/department.enum';
import { apeAuthApiConnector } from '~/connectors';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { clientSessionContext } from '../client-session.context';
import {
    CreateDepartmentDto,
    DeleteDepartmentDto,
    FindDepartmentDto,
    PaginationDepartmentDto,
    UpdateDepartmentDto,
} from './dto/department.dto';

@Injectable()
export class DepartmentService {
    constructor(private readonly departmentRepo: DepartmentRepo) {}

    async list(params: PaginationDepartmentDto) {
        const { tenantId } = clientSessionContext;
        let where: any = {};

        if (params.parentId) {
            where.parentId = params.parentId;
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }

        if (params.createdBy) {
            where.createdBy = params.createdBy;
        }
        if (params.createdForm && params.createdTo) {
            where.createdAt = Between(params.createdForm, params.createdTo);
        }

        if (params.pageSize === -1) {
            const [data, total] = await this.departmentRepo.findAndCount({
                where: {
                    tenantId,
                },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                data,
                total,
            };
        } else {
            return await this.departmentRepo.findPaginationByTenant(
                {
                    where,
                },
                {
                    pageSize: params.pageSize,
                    pageIndex: params.pageIndex,
                },
            );
        }
    }

    async create(body: CreateDepartmentDto) {
        try {
            const { tenantId } = clientSessionContext;
            let parentRefId = null;
            if (body.parentId) {
                const parentDepartment = await this.departmentRepo.findOne({
                    where: { id: body.parentId },
                });
                if (!parentDepartment) {
                    throw new BusinessException('Không tìm thấy phòng ban cha');
                }
                parentRefId = parentDepartment.departmentAuthId;
            }
            const exitDepartment = await this.departmentRepo.findOne({
                where: { code: body.code, tenantId },
            });
            if (exitDepartment) {
                throw new BusinessException('Mã phòng ban đã tồn tại');
            }

            const authRes = await apeAuthApiConnector.post('/api/public/crm/department/create', {
                code: body.code,
                name: body.name,
                description: body.description,
                parentId: parentRefId,
                status: NSDepartment.EStatus.ACTIVE,
                tenantId,
                createdDate: new Date(),
            });
            if (authRes?.id) {
                await this.departmentRepo.save({ ...body, tenantId, departmentAuthId: authRes.id });
            }
            return { message: 'Tạo phòng ban thành công' };
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }

    async update(body: UpdateDepartmentDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;
            const [department, exitDepartment] = await Promise.all([
                this.departmentRepo.findOne({ where: { id: body.id } }),
                this.departmentRepo.findOne({ where: { code: body.code } }),
            ]);
            if (!department) throw new BusinessException('Không tìm thấy phòng ban');
            if (exitDepartment && exitDepartment.id !== body.id)
                throw new BusinessException('Mã phòng ban đã tồn tại');

            let parentRefId = null;
            if (body.parentId) {
                // Chặn việc chuyển phòng ban vào chính cây con của nó (tránh vòng lặp)
                const allDepartments = await this.departmentRepo.find();

                const childrenMap = new Map<string, string[]>();
                for (const d of allDepartments) {
                    if (d.parentId) {
                        const arr = childrenMap.get(d.parentId) ?? [];
                        arr.push(d.id);
                        childrenMap.set(d.parentId, arr);
                    }
                }

                const queue: string[] = [body.id];
                const descendants = new Set<string>();
                while (queue.length) {
                    const cur = queue.shift()!;
                    const kids = childrenMap.get(cur) ?? [];
                    for (const k of kids) {
                        if (!descendants.has(k)) {
                            descendants.add(k);
                            queue.push(k);
                        }
                    }
                }

                if (descendants.has(body.parentId)) {
                    throw new BusinessException(
                        'Không thể chuyển phòng ban vào chính cấp dưới của nó',
                    );
                }
                const parentDepartment = await this.departmentRepo.findOne({
                    where: { id: body.parentId },
                });
                if (!parentDepartment) {
                    throw new BusinessException('Không tìm thấy phòng ban cha');
                }
                parentRefId = parentDepartment.departmentAuthId;
            }

            department.parentId = body.parentId;
            department.code = body.code;
            department.name = body.name;
            department.description = body.description;
            department.tenantId = tenantId;
            const authRes = await apeAuthApiConnector.post(
                '/api/public/crm/department/update-node',
                {
                    id: department.departmentAuthId,
                    tenantId,
                    code: body.code,
                    name: body.name,
                    description: body.description,
                    parentId: parentRefId,
                    updatedDate: new Date(),
                },
            );
            await this.departmentRepo.update(body.id, {
                ...department,
                departmentAuthId: authRes.id,
                updatedBy: memberId,
                updatedDate: new Date(),
                id: body.id,
            });
            return { message: 'Cập nhật phòng ban thành công' };
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }

    async findById(body: FindDepartmentDto) {
        const { tenantId } = clientSessionContext;
        const department = await this.departmentRepo.findOne({
            where: { id: body.id, tenantId },
        });
        if (!department) {
            throw new BusinessException('Không tìm thấy phòng ban');
        }
        if (department.status !== NSDepartment.EStatus.ACTIVE) {
            throw new BusinessException('Phòng ban không hoạt động');
        }
        return department;
    }

    async deleteDepartment(data: DeleteDepartmentDto) {
        try {
            const { tenantId } = clientSessionContext;
            const department = await this.departmentRepo.findOne({
                where: {
                    id: data.id,
                    tenantId,
                },
            });
            if (!department) {
                throw new BusinessException('Phòng ban không tồn tại');
            }
            await this.departmentRepo.delete(data.id);
            await apeAuthApiConnector.post('/api/public/crm/department/delete', {
                code: department.code,
                tenantId,
            });
            return {
                message: 'Xóa phòng ban thành công',
            };
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }
}
