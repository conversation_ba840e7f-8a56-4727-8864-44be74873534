import { Injectable } from '@nestjs/common';
import { BusinessException } from '~/@systems/exceptions';
import { NSMember } from '~/common/enums';
import { MemberRepo } from '~/domains/primary';
import { CreateMemberDto, SetActiveMemberDto, UpdateMemberDto } from './dto/member.dto';

@Injectable()
export class MemberService {
    constructor(private readonly memberRepo: MemberRepo) {}

    async create(createMemberDto: CreateMemberDto) {
        try {
            const { username, fullName, tenantId, avatar, departmentId, positionId, status } =
                createMemberDto;

            // Kiểm tra xem username đã tồn tại chưa
            const existingMember = await this.memberRepo.findOne({
                where: { username },
            });

            if (existingMember) {
                return existingMember
            }

            // Tạo mới thành viên
            return await this.memberRepo.save({
                username,
                fullName,
                avatar,
                tenantId,
                departmentId,
                positionId,
                status,
                createdDate: new Date(),
            });
        } catch (error) {
            if (error instanceof BusinessException) {
                throw error;
            }
            throw new BusinessException(error);
        }
    }

    async update(updateMemberDto: UpdateMemberDto) {
        try {
            const { fullName, avatar, departmentId, positionId, status } = updateMemberDto;

            // Tìm thành viên cần cập nhật
            const member = await this.memberRepo.findOne({
                where: { username: updateMemberDto.username },
            });

            if (!member) {
                throw new BusinessException('Không tìm thấy thành viên');
            }

            // Cập nhật thông tin
            await this.memberRepo.update(
                { id: member.id },
                {
                    fullName,
                    avatar,
                    departmentId,
                    positionId,
                    status,
                    updatedDate: new Date(),
                },
            );

            await this.memberRepo.findOne({
                where: { id: member.id },
            });

            return true;
        } catch (error) {
            if (error instanceof BusinessException) {
                throw error;
            }
            throw new BusinessException(error);
        }
    }

    async setActive(setActiveMemberDto: SetActiveMemberDto) {
        try {
            const { username, status } = setActiveMemberDto;

            // Tìm thành viên cần cập nhật trạng thái
            const member = await this.memberRepo.findOne({
                where: { username },
            });

            if (!member) {
                throw new BusinessException('Không tìm thấy thành viên');
            }

            // Xác định trạng thái mới
            const newStatus =
                status === NSMember.EStatus.ACTIVE
                    ? NSMember.EStatus.ACTIVE
                    : NSMember.EStatus.INACTIVE;

            // Cập nhật trạng thái
            await this.memberRepo.update(
                { id: member.id },
                {
                    status: newStatus,
                    updatedDate: new Date(),
                },
            );

            return true;
        } catch (error) {
            if (error instanceof BusinessException) {
                throw error;
            }
            throw new BusinessException(error);
        }
    }
}
