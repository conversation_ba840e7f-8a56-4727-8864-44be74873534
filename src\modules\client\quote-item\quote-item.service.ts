import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { BusinessException } from '~/@systems/exceptions';
import { CatalogItemRepo, QuoteItemRepo, QuoteRepo } from '~/domains/primary';
import { EventLogsService } from '../event-log/event-log.service';
import { CreateQuoteItemDto, QuoteItemDto, UpdateQuoteItemDto } from './dto';
@Injectable()
export class QuoteItemService {
    constructor(
        @InjectRepo(QuoteRepo) private quoteRepo: QuoteRepo,
        @InjectRepo(QuoteItemRepo) private quoteItemRepo: QuoteItemRepo,
        @InjectRepo(CatalogItemRepo) private catalogItemRepo: CatalogItemRepo,
        private readonly eventLogsService: EventLogsService,
    ) {}

    // Create
    @DefTransaction()
    async create(dto: CreateQuoteItemDto) {
        const quoteItem = this.quoteItemRepo.create(dto);
        const quote = await this.quoteRepo.findOneBy({ id: dto.quoteId });
        if (!quote) {
            throw new BusinessException('Quote not found');
        }
        const catalog = await this.catalogItemRepo.findOneBy({ id: dto.catalogItemId });
        if (!catalog) {
            throw new BusinessException('Catalog item not found');
        }

        const newQuoteItem = await this.quoteItemRepo.save(quoteItem);

        return newQuoteItem;
    }

    @DefTransaction()
    async update(dto: UpdateQuoteItemDto) {
        const { id, ...quoteItemData } = dto;
        const quoteItem = await this.quoteItemRepo.findOneBy({ id });
        if (!quoteItem) {
            throw new BusinessException('Quote item not found');
        }
        const catalog = await this.catalogItemRepo.findOneBy({ id: quoteItemData.catalogItemId });
        if (!catalog) {
            throw new BusinessException('Catalog item not found');
        }

        const res = await this.quoteItemRepo.update(id, {
            ...quoteItemData,
            catalogItemId: catalog.id,
        });

        return res;
    }

    // Delete
    @DefTransaction()
    async delete(dto: QuoteItemDto) {
        const { id } = dto;
        const quoteItem = await this.quoteItemRepo.findOneBy({ id });
        if (!quoteItem) {
            throw new BusinessException('Quote item not found');
        }
        const res = await this.quoteItemRepo.delete(id);

        return res;
    }
}
