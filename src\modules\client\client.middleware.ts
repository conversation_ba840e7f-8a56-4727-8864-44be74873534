import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { RequestContext } from '~/@core/context';
import { KEY_HEADER, KEY_SESSION_CONTEXT } from '~/common/constants';
import { ClientSessionDto } from './member-auth/dto';

@Injectable()
export class ClientMiddleware implements NestMiddleware {
    constructor(private jwtService: JwtService) {}

    async use(req: Request, res: Response, next: Function) {
        const { JWT_SECRET } = configEnv();
        try {
            const { headers = {} } = req;
            if (!headers || !headers[KEY_HEADER.AUTHORIZATION]) {
                throw new UnauthorizedException('Unauthorized');
            }
            const accessTokenBearer = headers[KEY_HEADER.AUTHORIZATION] as string;
            const accessToken = accessTokenBearer.replace('Bearer', '').trim();
            if (!accessToken) {
                throw new UnauthorizedException('Unauthorized');
            }

            try {
                const payload = await this.jwtService.verifyAsync(accessToken, {
                    secret: JWT_SECRET,
                });

                RequestContext.setAttribute<ClientSessionDto>(KEY_SESSION_CONTEXT.CLIENT_SESSION, {
                    accessToken,
                    refreshToken: '',
                    member: payload,
                });
                next();
            } catch (error) {
                console.log(`==========`, error);
                throw new UnauthorizedException('Unauthorized');
            }
        } catch (error) {
            console.log(error);
            next(new UnauthorizedException('Unauthorized'));
        }
    }
}
