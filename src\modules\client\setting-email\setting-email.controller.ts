import { Body, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { UpdateEmailConfigsDto } from './dto/setting-email.dto';
import { SettingEmailService } from './setting-email.service';

@ApiTags('Setting Email')
@UseGuards(PermissionGuard)
@DefController('setting-email')
export class SettingEmailController {
    constructor(private readonly settingEmailService: SettingEmailService) {}

    @DefGet()
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.VIEW])
    @ApiOperation({ summary: 'Lấy cấu hình email hiện tại' })
    async getEmailConfig() {
        return this.settingEmailService.getEmailConfigs();
    }

    @DefPost()
    @RequirePermissions([PERMISSION_CODES.SETTING_STRING.UPDATE])
    @ApiOperation({ summary: 'Cập nhật cấu hình email' })
    async updateMailConfig(@Body() dto: UpdateEmailConfigsDto) {
        return this.settingEmailService.updateMailConfigs(dto);
    }
}
