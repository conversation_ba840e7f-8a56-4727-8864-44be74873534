import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';

export class CreateCustomerAddressDto {
    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    @IsNotEmpty()
    customerId: string;

    @ApiProperty({ example: 'AD-0001', description: 'Tên địa chỉ' })
    @IsString()
    @IsNotEmpty()
    addressName: string;

    @ApiPropertyOptional({ example: '456 Đường XYZ, Quận 2, TP.HCM', description: 'Địa chỉ' })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    address?: string;

    @ApiPropertyOptional({ example: '79', description: 'Mã tỉnh/thành phố' })
    @IsOptional()
    @IsString()
    provinceCode?: string;

    @ApiPropertyOptional({ example: '27134', description: 'Mã phường/xã' })
    @IsOptional()
    @IsString()
    communeCode?: string;

    @ApiPropertyOptional({ example: 'Miền Bắc', description: 'Mã vùng' })
    @IsOptional()
    @IsString()
    regionCode?: string;

    @ApiPropertyOptional({ example: true, description: 'Địa chỉ mặc định' })
    @IsOptional()
    isDefault?: boolean;

    @ApiPropertyOptional({ example: 'Mô tả', description: 'Ghi chú' })
    @IsOptional()
    @IsString()
    description?: string;
}

export class ListCreateCustomerAddressDto {
    @ApiProperty({ type: [CreateCustomerAddressDto] })
    @IsNotEmpty()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateCustomerAddressDto)
    addresses: CreateCustomerAddressDto[];
}
