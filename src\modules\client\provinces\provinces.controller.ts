import { CacheInterceptor } from '@nestjs/cache-manager';
import { Query, UseInterceptors } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { IPaginationProvinces } from './dto/provinces.dto';
import { ProvincesService } from './provinces.service';

@DefController('provinces')
@UseInterceptors(CacheInterceptor)
export class ProvincesController {
    constructor(private readonly provincesService: ProvincesService) {}

    @DefGet('pagination')
    pagination(@Query() query: IPaginationProvinces) {
        return this.provincesService.pagination(query);
    }

    @DefGet('get-province-by-code')
    getProvincesByCode(@Query('code') codes: string) {
        return this.provincesService.getProvincesByCode(codes);
    }
}
