import { Injectable } from '@nestjs/common';
import { DefTransaction } from 'nestjs-typeorm3-kit';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSPosition } from '~/common/enums/position.enum';
import { apeAuthApiConnector } from '~/connectors';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { PositionRepo } from '~/domains/primary/position/position.repo';
import { clientSessionContext } from '../client-session.context';
import {
    CreatePositionDto,
    FindPositionByIdDto,
    PaginationPositionDto,
    UpdatePositionDto,
} from './dto/position.dto';

@Injectable()
export class PositionService {
    constructor(
        private readonly positionRepo: PositionRepo,
        private readonly departmentRepo: DepartmentRepo,
    ) {}

    async pagination(body: PaginationPositionDto) {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize } = body;
        let where: any = {};
        if (body.name) {
            where.name = ILike(`%${body.name}%`);
        }
        if (body.code) {
            where.code = ILike(`%${body.code}%`);
        }
        if (body.status) {
            where.status = body.status;
        }
        if (body.createdForm && body.createdTo) {
            where.createdDate = Between(body.createdForm, body.createdTo);
        }
        if (body.departmentId) {
            where.departmentId = body.departmentId;
        }
        if (body.status) {
            where.status = body.status;
        }
        if (pageSize === -1) {
            const [data, total] = await this.positionRepo.findAndCount({
                where: { ...where, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                data,
                total,
            };
        } else {
            return await this.positionRepo.findPagination(
                {
                    where: { ...where, tenantId },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                {
                    pageSize,
                    pageIndex,
                },
            );
        }
    }

    @DefTransaction()
    async create(body: CreatePositionDto) {
        try {
            const { tenantId } = clientSessionContext;
            const exitedPosition = await this.positionRepo.findOne({
                where: {
                    code: body.code,
                    tenantId,
                },
            });
            if (exitedPosition) {
                throw new BusinessException('Position code này đã tồn tại');
            }

            const authRes = await apeAuthApiConnector.post('/api/public/crm/position/create', {
                name: body.name,
                code: body.code,
                tenantId,
                createdDate: new Date(),
                departmentId: body.departmentId,
                status: NSPosition.EStatus.ACTIVE,
            });
            if (authRes?.id) {
                await this.positionRepo.save({
                    ...body,
                    status: NSPosition.EStatus.ACTIVE,
                    tenantId,
                    positionAuthId: authRes.id,
                });
            }
            return {
                message: 'Tạo position thành công',
            };
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }

    @DefTransaction()
    async update(body: UpdatePositionDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;

            // 1) Tìm position theo id + tenant
            const position = await this.positionRepo.findOne({
                where: { id: body.id, tenantId },
            });
            if (!position) {
                throw new BusinessException('Position không tồn tại');
            }

            // 3) Chuẩn hoá input
            const nextCode = body.code.trim().toUpperCase();
            const nextName = body.name.trim().replace(/\s+/g, ' ');

            // 4) Kiểm tra department thuộc tenant
            const department = await this.departmentRepo.findOne({
                where: { id: body.departmentId, tenantId },
                select: ['id'],
            });
            if (!department) {
                throw new BusinessException(
                    'Phòng ban không tồn tại hoặc không thuộc tenant hiện tại',
                );
            }

            // 5) Chống trùng trong cùng (tenantId, departmentId) loại trừ chính nó
            const dup = await this.positionRepo.findOne({
                where: [
                    { tenantId, departmentId: body.departmentId, code: nextCode },
                    { tenantId, departmentId: body.departmentId, name: nextName },
                ],
                select: ['id', 'code', 'name'],
            });
            if (dup && dup.id !== body.id) {
                throw new BusinessException('Position code/name đã tồn tại trong phòng ban');
            }

            // 6) Cập nhật local trước (transaction sẽ rollback nếu phần Auth phía dưới lỗi)
            position.code = nextCode;
            position.name = nextName;
            position.description = body.description;
            position.departmentId = body.departmentId || position.departmentId;
            position.status = body.status;
            position.tenantId = tenantId;
            position.updatedBy = memberId;
            position.updatedDate = new Date();
            position.id = body.id;

            const saved = await this.positionRepo.save(position);

            // 7) Đồng bộ Auth/CRM
            if (saved.positionAuthId) {
                // Kiểm tra nếu có thay đổi phòng ban thì lấy departmentAuthId mới lưu
                const newData: any = {};
                if (body.departmentId !== position.departmentId) {
                    const department = await this.departmentRepo.findOne({
                        where: { id: body.departmentId, tenantId },
                        select: ['departmentAuthId'],
                    });
                    if (!department) {
                        throw new BusinessException(
                            'Phòng ban không tồn tại hoặc không thuộc tenant hiện tại',
                        );
                    }
                    newData.departmentAuthId = department.departmentAuthId; // Phải lấy id tham chiếu từ remote
                }
                // Update remote
                await apeAuthApiConnector.post('/api/public/crm/position/update', {
                    id: saved.positionAuthId,
                    name: nextName,
                    code: nextCode,
                    tenantId,
                    departmentId: body.departmentId,
                    ...newData,
                    status: body.status,
                    updatedDate: new Date(),
                    updatedBy: memberId,
                });
            }

            return { message: 'Cập nhật position thành công' };
        } catch (error) {
            if (error instanceof BusinessException) throw error;
            throw new BusinessException(error?.message || 'Lỗi cập nhật position');
        }
    }

    @DefTransaction()
    async setActive(body: FindPositionByIdDto) {
        try {
            const { tenantId, memberId } = clientSessionContext;
            const position = await this.positionRepo.findOne({
                where: {
                    id: body.id,
                    tenantId,
                },
            });
            if (!position) {
                throw new BusinessException('Position không tồn tại');
            }
            if (position.status === NSPosition.EStatus.ACTIVE) {
                position.status = NSPosition.EStatus.INACTIVE;
                position.updatedBy = memberId;
                position.updatedDate = new Date();
                position.id = body.id;
                await this.positionRepo.save(position);
                await apeAuthApiConnector.post('/api/public/crm/position/update', {
                    id: position.positionAuthId,
                    name: position.name,
                    code: position.code,
                    tenantId,
                    updatedDate: new Date(),
                    departmentId: position.departmentId,
                    status: NSPosition.EStatus.INACTIVE,
                });
            } else {
                position.status = NSPosition.EStatus.ACTIVE;
                position.updatedBy = memberId;
                position.updatedDate = new Date();
                position.id = body.id;
                await this.positionRepo.save(position);
                await apeAuthApiConnector.post('/api/public/crm/position/update', {
                    id: position.positionAuthId,
                    name: position.name,
                    code: position.code,
                    tenantId,
                    updatedDate: new Date(),
                    departmentId: position.departmentId,
                    status: NSPosition.EStatus.ACTIVE,
                });
            }
            return {
                message: 'Cập nhật position thành công',
            };
        } catch (error) {
            throw new BusinessException(error.message);
        }
    }
}
