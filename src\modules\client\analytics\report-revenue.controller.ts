import { Query } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { ListRevenueReportDto } from './dto/revenue-report.dto';
import { ReportRevenueService } from './report-revenue.service';

@DefController('report-revenue')
export class ReportRevenueController {
    constructor(private readonly reportService: ReportRevenueService) {}

    @DefGet('list')
    async listRevenueReport(@Query() query: ListRevenueReportDto) {
        return this.reportService.listRevenueReport(query);
    }
}
