export namespace NSComplaint {
    export enum EComplaintPriority {
        URGENT = 'URGENT', // Khẩn cấp
        HIGH = 'HIGH', // Cao
        MEDIUM = 'MEDIUM', // Trung bình
        LOW = 'LOW', // Thấp
    }

    export enum EComplaintStatus {
        NEW = 'NEW', // Mới
        SENT = 'SENT', // Đã duyệt
        APPROVED = 'APPROVED', // Đã duyệt
        REJECTED = 'REJECTED', // Đã từ chối
        IN_PROGRESS = 'PROGRESS', // Đang xử lý
        HOLD = 'HOLD', // Tạm dừng
        RESOLVED = 'RESOLVED', // Đã giải quyết
        CANCELLED = 'CANCELLED', // Đã hủy
    }

    export enum EComplaintRelationshipType {
        DUPLICATE = 'DUPLICATE', // Trùng lặp
        FOLLOW_UP = 'FOLLOW_UP', // <PERSON>õ<PERSON>ELATED_ISSUE = 'RELATED_ISSUE', // Vấn đề liên quan
        SPLIT_CASE = 'SPLIT_CASE', // Tách case
    }

    export const ECOMPLAINT_PRIORITY_LOCALE_LABEL = {
        [EComplaintPriority.URGENT]: 'enums.NSComplaint.EComplaintPriority.URGENT',
        [EComplaintPriority.HIGH]: 'enums.NSComplaint.EComplaintPriority.HIGH',
        [EComplaintPriority.MEDIUM]: 'enums.NSComplaint.EComplaintPriority.MEDIUM',
        [EComplaintPriority.LOW]: 'enums.NSComplaint.EComplaintPriority.LOW',
    };

    export const ECOMPLAINT_STATUS_LOCALE_LABEL = {
        [EComplaintStatus.NEW]: 'enums.NSComplaint.EComplaintStatus.NEW',
        [EComplaintStatus.SENT]: 'enums.NSComplaint.EComplaintStatus.SENT',
        [EComplaintStatus.APPROVED]: 'enums.NSComplaint.EComplaintStatus.APPROVED',
        [EComplaintStatus.REJECTED]: 'enums.NSComplaint.EComplaintStatus.REJECTED',
        [EComplaintStatus.IN_PROGRESS]: 'enums.NSComplaint.EComplaintStatus.IN_PROGRESS',
        [EComplaintStatus.HOLD]: 'enums.NSComplaint.EComplaintStatus.HOLD',
        [EComplaintStatus.RESOLVED]: 'enums.NSComplaint.EComplaintStatus.RESOLVED',
        [EComplaintStatus.CANCELLED]: 'enums.NSComplaint.EComplaintStatus.CANCELLED',
    };

    export const ECOMPLAINT_STATUS_LOCALE_COLOR = {
        [EComplaintStatus.NEW]: '#2ECC71',
        [EComplaintStatus.SENT]: '#F39C12',
        [EComplaintStatus.APPROVED]: '#27AE60',
        [EComplaintStatus.REJECTED]: '#E74C3C',
        [EComplaintStatus.IN_PROGRESS]: '#3498DB',
        [EComplaintStatus.HOLD]: '#F1C40F',
        [EComplaintStatus.RESOLVED]: '#1E8449',
        [EComplaintStatus.CANCELLED]: '#E74C3C',
    };

    export const ECOMPLAINT_RELATIONSHIP_LOCALE_LABEL = {
        [EComplaintRelationshipType.DUPLICATE]:
            'enums.NSComplaint.EComplaintRelationshipType.DUPLICATE',
        [EComplaintRelationshipType.FOLLOW_UP]:
            'enums.NSComplaint.EComplaintRelationshipType.FOLLOW_UP',
        [EComplaintRelationshipType.RELATED_ISSUE]:
            'enums.NSComplaint.EComplaintRelationshipType.RELATED_ISSUE',
        [EComplaintRelationshipType.SPLIT_CASE]:
            'enums.NSComplaint.EComplaintRelationshipType.SPLIT_CASE',
    };

    export const ECOMPLAINT_RELATIONSHIP_LOCALE_COLOR = {
        [EComplaintRelationshipType.DUPLICATE]: '#FF6B6B',
        [EComplaintRelationshipType.FOLLOW_UP]: '#4ECDC4',
        [EComplaintRelationshipType.RELATED_ISSUE]: '#45B7D1',
        [EComplaintRelationshipType.SPLIT_CASE]: '#96CEB4',
    };
}
