import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'ape-nestjs-typeorm3-kit';
import { PermissionGuard } from '../@guards/permission/permission.guard';
import { ApprovalRequestService } from './approval-request.service';
import { ApprovalService } from './approval.service';
import {
    ApproveRequestDto,
    ListApprovalRequestDto,
    ListStepApprovalRequestDto,
    RejectRequestDto,
} from './dto/approval-request.dto';
import {
    CreateApprovalConfigDto,
    CreateApprovalConfigMappingDto,
    GetApprovalConfigDto,
    GetApprovalConfigMappingDto,
    IdApprovalConfigDto,
    UpdateApprovalConfigDto,
    UpdateApprovalConfigMappingDto,
} from './dto/approval.dto';

@DefController('approval-config')
@UseGuards(PermissionGuard)
export class ApprovalController {
    constructor(
        private readonly approvalService: ApprovalService,
        private readonly approvalRequestService: ApprovalRequestService,
    ) {}

    // Lấy danh sách cấu hình duyệt
    @DefGet('list')
    async getConfigs(@Query() dto: GetApprovalConfigDto) {
        return this.approvalService.getConfigs(dto);
    }

    // Chi tiết
    @DefGet('detail')
    async getConfigDetail(@Query() dto: IdApprovalConfigDto) {
        return this.approvalService.getConfigDetail(dto.id);
    }

    // Tạo mới cấu hình duyệt
    @DefPost('create')
    async createConfig(@Body() dto: CreateApprovalConfigDto) {
        return this.approvalService.createConfig(dto);
    }

    // Cập nhật cấu hình duyệt
    @DefPost('update')
    async updateConfig(@Body() dto: UpdateApprovalConfigDto) {
        return this.approvalService.updateConfig(dto);
    }

    // Xóa cấu hình duyệt
    @DefPost('delete')
    async deleteConfig(@Body() dto: IdApprovalConfigDto) {
        return this.approvalService.deleteConfig(dto.id);
    }

    // Lấy danh sách mapping nghiệp vụ
    @DefGet('list-mapping')
    async getMapping(@Query() dto: GetApprovalConfigMappingDto) {
        return this.approvalService.getMapping(dto);
    }

    // Tạo mapping nghiệp vụ
    @DefPost('create-mapping')
    async createMapping(@Body() dto: CreateApprovalConfigMappingDto) {
        return this.approvalService.createMapping(dto);
    }

    // Cập nhật mapping nghiệp vụ
    @DefPost('update-mapping')
    async updateMapping(@Body() dto: UpdateApprovalConfigMappingDto) {
        return this.approvalService.updateMapping(dto);
    }

    // View request approval
    @DefGet('detail-request', {
        summary: 'View detail one request approval',
    })
    async detailRequest(@Query() dto: IdApprovalConfigDto) {
        return this.approvalRequestService.detailRequest(dto.id);
    }

    // View List request approval của 1 member
    @DefPost('list-request', {
        summary: 'Danh sách những yêu cầu duyệt của tôi',
    })
    async listRequestMember(@Body() dto: ListApprovalRequestDto) {
        return this.approvalRequestService.listRequestApproval(dto);
    }

    // Lấy ra những bước duyệt của member
    @DefPost('list-my-request-step', {
        summary: 'Lấy ra những bước duyệt của tôi cần duyệt',
    })
    async listMyRequestStep(@Body() dto: ListStepApprovalRequestDto) {
        return this.approvalRequestService.listMyApprovalRequests(dto);
    }

    // Duyệt request approval
    @DefPost('approve', {
        summary: 'Approve request approval',
    })
    async approveRequest(@Body() dto: ApproveRequestDto) {
        return this.approvalRequestService.approveRequest(dto);
    }

    // Từ chối request approval
    @DefPost('reject', {
        summary: 'Reject request approval',
    })
    async rejectRequest(@Body() dto: RejectRequestDto) {
        return this.approvalRequestService.rejectRequest(dto);
    }

    // Kiểm tra user hiện tại có quyền duyệt không
    @DefPost('check-approve', {
        summary: 'Check user current has permission to approve',
    })
    async checkApprove(@Body() dto: ApproveRequestDto) {
        return this.approvalRequestService.checkApprove(dto);
    }
}
