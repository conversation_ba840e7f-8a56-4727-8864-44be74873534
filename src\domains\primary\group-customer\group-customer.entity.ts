import { ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('group_customer')
export class GroupCustomerEntity extends PrimaryBaseEntity {
  @ApiPropertyOptional({ example: 'GC00001' })
  @Column({ type: 'varchar', length: 64, nullable: true })
  @Index()
  code?: string | null;

  @ApiPropertyOptional({ example: 'Nhóm khách hàng VIP' })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  name?: string | null;

  @ApiPropertyOptional({ example: 'Mô tả nhóm' })
  @Column({ type: 'text', nullable: true })
  description?: string | null;

  @ApiPropertyOptional({ example: ['uuid-1', 'uuid-2'] })
  @Column({ type: 'simple-array', nullable: true })
  customerIds?: string[] | null;

  @ApiPropertyOptional({ example: 'ACTIVE' })
  @Column({ type: 'varchar', length: 32, nullable: true })
  status?: string | null;
}

