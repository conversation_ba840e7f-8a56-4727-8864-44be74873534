import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSApproval } from '~/common/enums/approval.enum';
import { NSCampaignCustomer } from '~/common/enums/campaign-customer.enum';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { CustomerRepo, MemberRepo } from '~/domains/primary';
import { CampaignCustomerRepo } from '~/domains/primary/campaign-customer/campaign-customer.repo';
import { CampaignRepo } from '~/domains/primary/marketing-campaign/campaign.repo';
import { ApprovalRequestService } from '../approval/approval-request.service';
import { clientSessionContext } from '../client-session.context';
import { MailSenderService } from '../mail/mail-sender.service';
import { MemberService } from '../member/member.service';
import { CreateCampaignDto, ListCampaignDto, UpdateCampaignDto } from './dto/campaign.dto';

@Injectable()
export class CampaignService {
    constructor(
        @InjectRepo(CampaignRepo) private campaignRepo: CampaignRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        @InjectRepo(CampaignCustomerRepo) private campaignCustomerRepo: CampaignCustomerRepo,
        private readonly memberService: MemberService,
        private readonly customerRepo: CustomerRepo,
        private readonly mailSenderService: MailSenderService,
        private readonly approvalRequestService: ApprovalRequestService,
    ) {}

    // List
    async listCampaign(params: ListCampaignDto) {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize } = params;
        let where: any = {};

        if (params.status) {
            where.status = params.status;
        }
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }
        if (params.createdDateFrom && params.createdDateTo) {
            where.createdDate = Between(params.createdDateFrom, params.createdDateTo);
        }
        if (params.sendDateFrom && params.sendDateTo) {
            where.sendDate = Between(params.sendDateFrom, params.sendDateTo);
        }
        if (params.mailSend) {
            where.mailSend = params.mailSend;
        }
        if (params.createdBy) {
            where.createdBy = params.createdBy;
        }

        let res: any = [];
        if (pageSize === -1) {
            const [data, total] = await this.campaignRepo.findAndCount({
                where: { ...where, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                total,
                data,
            };
        } else {
            res = await this.campaignRepo.findPagination(
                {
                    where: { ...where, tenantId },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                { pageIndex, pageSize },
            );

            const members = await this.memberRepo.find({
                where: { tenantId },
            });
            const customerData = await this.customerRepo.find({
                where: { tenantId },
            });

            const reMapRes = res.data.map(item => ({
                ...item,
                customerData,
                senderName: members.find(m => m.id === item.createdBy)?.fullName,
            }));
            const dataWithApproval = await Promise.all(
                reMapRes.map(async campaign => {
                    const approvalInfo = await this.approvalRequestService.getApprovalInfoForEntity(
                        campaign.id,
                        NSApproval.ApprovalBusinessType.CAMPAIGN,
                    );
                    return {
                        ...campaign,
                        approval: approvalInfo,
                    };
                }),
            );
            return {
                total: res.total,
                data: dataWithApproval,
            };
        }
    }

    // Create
    @DefTransaction()
    async createCampaign(data: CreateCampaignDto) {
        const { tenantId, memberId } = clientSessionContext;

        // Tạo mã code nếu không có
        if (!data.code) {
            const existingCampaigns = await this.campaignRepo.find({
                where: { tenantId },
            });
            data.code = `CD${String(existingCampaigns.length + 1).padStart(5, '0')}`;
        }

        // Tạo chiến dịch
        const campaign = await this.campaignRepo.save({
            ...data,
            tenantId,
            createdBy: memberId,
            customerIds: data.customerIds,
            groupIds: (data as any).groupIds,
            status: data.status || NSCampaign.EStatus.NEW,
            sendDate: data.sendDate || null,
        });
        const customers = await this.customerRepo.find({
            where: { id: In(data.customerIds), tenantId },
        });

        if (data.customerIds && data.customerIds.length > 0) {
            await this.campaignCustomerRepo.insert(
                data.customerIds.map(customerId => ({
                    campaignId: campaign.id,
                    customerId,
                    email: customers.find(c => c.id === customerId)?.email,
                    status: NSCampaignCustomer.EStatus.NEW,
                    sendDate: campaign.sendDate,
                    tenantId,
                    createdBy: memberId,
                })),
            );
        }
        try {
            await this.approvalRequestService.createRequestForDocument(
                {
                    businessType: NSApproval.ApprovalBusinessType.CAMPAIGN,
                    documentId: campaign.id,
                    documentCode: campaign.code,
                    createdBy: memberId,
                    title: `Duyệt chiến dịch marketing ${campaign.code}`,
                    initialStatus: NSApproval.ApprovalRequestStatus.PENDING,
                    finalStatuses: [
                        NSApproval.ApprovalRequestStatus.APPROVED,
                        NSApproval.ApprovalRequestStatus.REJECTED,
                    ],
                    approvedStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                },
                // { skipIfNoConfig: true }, // Skip nếu không có config
            );
        } catch (error) {}

        return campaign;
    }

    // Update
    @DefTransaction()
    async updateCampaign(body: UpdateCampaignDto) {
        const { tenantId, memberId } = clientSessionContext;
        const data = body;

        // Kiểm tra chiến dịch tồn tại
        const campaign = await this.campaignRepo.findOne({
            where: { code: data.code, tenantId },
        });

        if (!campaign) {
            throw new BusinessException('Không tìm thấy chiến dịch');
        }

        // Cập nhật thông tin
        campaign.name = data.name;
        campaign.description = data.description;
        campaign.content = data.content;
        campaign.sendDate = data.sendDate;
        campaign.mailSend = data.mailSend;
        campaign.customerIds = data.customerIds;
        (campaign as any).groupIds = (data as any).groupIds;
        campaign.status = data.status;
        campaign.updatedBy = memberId;
        campaign.updatedDate = new Date();

        await this.campaignRepo.update(campaign.id, { ...campaign, id: campaign.id });

        const existingCustomers = await this.campaignCustomerRepo.find({
            where: { campaignId: campaign.id, tenantId },
        });

        const existingCustomerIds = existingCustomers.map(item => item.customerId);

        const newCustomerIds =
            data.customerIds?.filter(id => !existingCustomerIds.includes(id)) || [];

        const removeCustomerIds = existingCustomerIds.filter(id => !data.customerIds?.includes(id));

        const customers = await this.customerRepo.find({
            where: { id: In(data.customerIds), tenantId },
        });

        if (newCustomerIds.length > 0) {
            await this.campaignCustomerRepo.insert(
                newCustomerIds.map(customerId => ({
                    campaignId: campaign.id,
                    customerId,
                    status: NSCampaignCustomer.EStatus.NEW,
                    sendDate: campaign.sendDate,
                    tenantId,
                    createdBy: memberId,
                    email: customers.find(c => c.id === customerId)?.email,
                })),
            );
        }

        // Xóa các khách hàng không còn trong danh sách
        if (removeCustomerIds.length > 0) {
            await this.campaignCustomerRepo.delete({
                campaignId: campaign.id,
                customerId: In(removeCustomerIds),
                tenantId,
            });
        }

        return {
            message: 'Cập nhật chiến dịch thành công',
        };
    }

    // Detail
    async detailCampaign(id: string) {
        const { tenantId } = clientSessionContext;
        const campaign = await this.campaignRepo.findOne({ where: { id, tenantId } });

        if (!campaign) {
            throw new Error('Không tìm thấy chiến dịch');
        }

        const members = await this.memberRepo.find({
            where: { tenantId },
        });

        return {
            ...campaign,
            createdName: members.find(m => m.id === campaign.createdBy)?.fullName,
        };
    }

    // Update Status
    @DefTransaction()
    async updateStatus(id: string, status: NSCampaign.EStatus) {
        const { tenantId } = clientSessionContext;
        const campaign = await this.campaignRepo.findOne({
            where: { id, tenantId },
        });

        if (!campaign) {
            throw new BusinessException('Không tìm thấy chiến dịch');
        }

        campaign.status = status;
        await this.campaignRepo.update(id, { ...campaign, id: campaign.id });

        return {
            message: 'Cập nhật trạng thái thành công',
        };
    }

    @DefTransaction()
    async sendNow(id: string) {
        const { tenantId, memberId } = clientSessionContext;
        const campaign = await this.campaignRepo.findOne({
            where: { id, tenantId },
        });

        if (!campaign) {
            throw new BusinessException('Không tìm thấy chiến dịch');
        }

        campaign.status = NSCampaign.EStatus.RESOLVED;
        await this.campaignRepo.update(id, { ...campaign, id: campaign.id });
        const customers = await this.campaignCustomerRepo.find({
            where: { campaignId: campaign.id, tenantId },
        });

        for (const customer of customers) {
            try {
                const res = await this.mailSenderService.sendMail({
                    to: customer.email,
                    subject: `${campaign.name} `,
                    html: campaign.content,
                    from: campaign.mailSend,
                });
                // Nếu gửi thành công, cập nhật trạng thái SENT cho khách hàng đó
                if (res.status === 200) {
                    await this.campaignCustomerRepo.update(
                        { id: customer.id },
                        {
                            status: NSCampaignCustomer.EStatus.SUCCESS,
                            sendDate: new Date(),
                            updatedBy: memberId,
                            id: customer.id,
                        },
                    );
                } else {
                    await this.campaignCustomerRepo.update(
                        { id: customer.id },
                        {
                            status: NSCampaignCustomer.EStatus.FAILED,
                            sendDate: new Date(),
                            updatedBy: memberId,
                            id: customer.id,
                        },
                    );
                }
            } catch (error) {
                // throw new BusinessException('Gửi email thất bại');
                console.error(`Gửi email thất bại cho khách hàng ${customer.email}:`, error);
            }
        }
        return {
            message: 'Gửi chiến dịch thành công',
        };
    }
}
