import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm'
import { PrimaryBaseEntity } from '../primary-base.entity'
import { NSApproval } from '~/common/enums/approval.enum'
import { ApprovalRequestEntity } from './approval-request.entity'
import { ApprovalRequestStepEntity } from './approval-request-step.entity'

@Entity('approval_request_action')
@Index(['requestId', 'stepId'])
export class ApprovalRequestActionEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  @Index()
  requestId: string

  @Column({ type: 'uuid' })
  stepId: string

  // Ngư<PERSON>i thực hiện (memberId)
  @Column({ type: 'uuid' })
  actorId: string

  @Column({
    type: 'enum',
    enum: NSApproval.ApprovalActionType,
  })
  action: NSApproval.ApprovalActionType

  @Column({ type: 'text', nullable: true })
  comment?: string

  @ManyToOne(() => ApprovalRequestStepEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'stepId' })
  step: ApprovalRequestStepEntity

  @ManyToOne(() => ApprovalRequestEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requestId' })
  request: ApprovalRequestEntity
}
