import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PageRequest } from '~/@systems/utils/page.utils';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import {
    CreateNotificationReq,
    ListMyReceiptsQuery,
    ListNotificationQuery,
    ListReceiptsQuery,
    MarkReadReq,
} from './dto/notification.dto';
import { NotificationService } from './notification.service';

@ApiTags('Notification')
@UseGuards(PermissionGuard)
@DefController('notification')
export class NotificationController {
    constructor(private readonly notificationService: NotificationService) {}

    @DefPost()
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.CREATE])
    @ApiOperation({ summary: 'Tạo thông báo' })
    async create(@Body() dto: CreateNotificationReq) {
        const scheduleDate = dto.scheduleDate ? new Date(dto.scheduleDate) : undefined;
        return this.notificationService.createNotification({ ...dto, scheduleDate } as any);
    }

    @DefGet()
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.VIEW])
    @ApiOperation({ summary: 'Danh sách thông báo theo tenant (paginate)' })
    async list(@Query() query: ListNotificationQuery) {
        return this.notificationService.listNotifications(query);
    }

    @DefPost('process-due')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.UPDATE])
    @ApiOperation({ summary: 'Xử lý thông báo đến hạn' })
    async processDue() {
        return this.notificationService.processDueNotifications();
    }

    @DefPost('read')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.UPDATE])
    @ApiOperation({ summary: 'Đánh dấu đã đọc' })
    async markRead(@Body() dto: MarkReadReq) {
        return this.notificationService.markRead(dto.receiptId);
    }

    @DefPost('read-all')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.UPDATE])
    @ApiOperation({ summary: 'Đánh dấu đã đọc tất cả của member hiện tại' })
    async markReadAll() {
        return this.notificationService.markReadAll();
    }

    @DefGet('receipts')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.VIEW])
    @ApiOperation({ summary: 'Danh sách receipts theo tenant (paginate & filter)' })
    async listReceipts(@Query() query: ListReceiptsQuery) {
        const pageRequest: PageRequest = {
            pageIndex: Number(query.pageIndex || 1),
            pageSize: Number(query.pageSize || 20),
        } as any;
        const isRead = typeof query.isRead === 'string' ? query.isRead === 'true' : undefined;
        return this.notificationService.listReceipts(pageRequest, {
            notificationId: query.notificationId,
            deliveryStatus: query.deliveryStatus,
            memberId: query.memberId,
            isRead,
        });
    }

    @DefGet('receipts-by-me')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.VIEW])
    @ApiOperation({ summary: 'Danh sách receipts của chính member (paginate & filter)' })
    async listMyReceipts(@Query() query: ListMyReceiptsQuery) {
        return this.notificationService.listMyReceipts(query);
    }
}
