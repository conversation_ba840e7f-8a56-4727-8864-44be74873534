import { Injectable } from "@nestjs/common";
import { DefTransaction, InjectRepo } from "ape-nestjs-typeorm3-kit";
import { apeAuthApiConnector } from "~/connectors";
import { PositionRepo } from "~/domains/primary";

@Injectable()
export class PositionService  {
    constructor(
        @InjectRepo(PositionRepo) private positionRepo: PositionRepo,
    ){}

    @DefTransaction()
    async compareWithAuth() {
        const [positionHRM, positionAuth] = await Promise.all([
            this.positionRepo.find(),
            apeAuthApiConnector
                .get('/api/public/crm/position/find-all', { pageSize: -1, pageIndex: 1 })
                .then(res => res.data),
        ]);

        // Xử lý tạo mới
        const positionToCreate = positionAuth.filter(
            auth => !positionHRM.some(hrm => hrm.positionAuthId === auth.id),
        );
        for (const auth of positionToCreate) {
            await this.positionRepo.save({ ...auth, positionAuthId: auth.id });
        }

        // Xử lý xóa
        const positionToDelete = positionHRM.filter(
            hrm => !positionAuth.some(auth => auth.id === hrm.positionAuthId),
        );
        for (const hrm of positionToDelete) {
            await this.positionRepo.delete(hrm.id);
        }

        // Xử lý cập nhật nếu thuộc tính thay đổi
        for (const auth of positionAuth) {
            const position = positionHRM.find(c => c.positionAuthId === auth.id);
            if (position) {
                let needsUpdate = false;
                if (position.code !== auth.code) {
                    position.code = auth.code;
                    needsUpdate = true;
                }
                if (position.name !== auth.name) {
                    position.name = auth.name;
                    needsUpdate = true;
                }
                if (position.description !== auth.description) {
                    position.description = auth.description;
                    needsUpdate = true;
                }
                if (position.departmentId !== auth.departmentId) {
                    position.departmentId = auth.departmentId;
                    needsUpdate = true;
                }
                // Thêm các trường khác nếu cần so sánh
                if (needsUpdate) {
                    const { id, ...updateData } = position;
                    await this.positionRepo.update(id, updateData);
                }
            }
        }
    } 
}