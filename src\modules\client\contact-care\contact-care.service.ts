import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ArrayContains, Between, ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSApproval } from '~/common/enums/approval.enum';
import { NSContactCare } from '~/common/enums/contact-care.enum';
import { NSNotification } from '~/common/enums/notification.enum';
import { generateCodeHelper } from '~/common/helpers';
import { ContactCareRepo, CustomerRepo, MemberRepo } from '~/domains/primary';
import { ApprovalRequestService } from '../approval/approval-request.service';
import { clientSessionContext } from '../client-session.context';
import { NotificationService } from '../notification/notification.service';
import {
    CreateCustomerContactDto,
    DeleteCustomerContactDto,
    DeleteListCustomerContactDto,
    DetailCustomerContactDto,
    ListCustomerContactDto,
    UpdateCustomerContactDto,
    UpdateStatusCustomerContactDto,
} from './dto/contact-care.dto';

@Injectable()
export class ContactCareService {
    constructor(
        @InjectRepo(ContactCareRepo) private contactCareRepo: ContactCareRepo,
        @InjectRepo(CustomerRepo) private customerRepo: CustomerRepo,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        private readonly notificationService: NotificationService,
        private readonly approvalRequestService: ApprovalRequestService,
    ) {}

    async listCustomerContact(params: ListCustomerContactDto) {
        const { tenantId, memberId } = clientSessionContext;
        const { pageIndex, pageSize } = params;
        let where: any = { tenantId };

        if (params.title) {
            where.title = ILike(`%${params.title}%`);
        }
        if (params.type) {
            where.type = params.type;
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.customerId) {
            where.customerId = params.customerId;
        }
        if (params.supervisorMemberId) {
            where.supervisorMemberId = params.supervisorMemberId;
        }
        if (params.assignedMemberId) {
            where.assignedMemberIds = ArrayContains([params.assignedMemberId]);
        }
        if (params.dueDateFrom && params.dueDateTo) {
            where.dueDate = Between(params.dueDateFrom, params.dueDateTo);
        }
        if (params.createdDateFrom && params.createdDateTo) {
            where.createdDate = Between(params.createdDateFrom, params.createdDateTo);
        }
        if (params.code) {
            where.code = ILike(`%${params.code}%`);
        }

        const result = await this.contactCareRepo.findPagination(
            {
                where: {
                    ...where,
                },
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex,
                pageSize,
            },
        );

        // Fetch customer and member data for mapping
        const customerIds = result.data.map(item => item.customerId).filter(Boolean) as string[];

        const [customers, members] = await Promise.all([
            customerIds.length > 0
                ? this.customerRepo.find({ where: { id: In(customerIds), tenantId } })
                : Promise.resolve([]),
            this.memberRepo.find({
                where: { tenantId },
            }),
        ]);

        const customerMap = new Map(customers.map(c => [c.id, c]));
        const memberMap = new Map(members.map(m => [m.id, m]));

        // Map customer and member names
        const mappedData = result.data.map((item: any) => {
            const mapped: any = { ...item };

            // Map customer data
            if (item.customerId) {
                const customer = customerMap.get(item.customerId);
                mapped.customerCode = customer?.code || '';
                mapped.customerName = customer?.name || '';
                mapped.sapCode = customer?.sapCode || '';
            }

            // Map supervisor name
            if (item.supervisorMemberId) {
                mapped.supervisorName = memberMap.get(item.supervisorMemberId)?.fullName || '';
            }

            // Map assigned employee names (array IDs)
            if (Array.isArray(item.assignedMemberIds) && item.assignedMemberIds.length > 0) {
                const employeeNames = item.assignedMemberIds
                    .map((id: string) => memberMap.get(id.trim())?.fullName)
                    .filter(Boolean)
                    .join(', ');
                mapped.assignedEmployeeName = employeeNames;
            }
            // Map created by name
            if (item.createdBy) {
                mapped.createdByName = memberMap.get(item.createdBy)?.fullName;
            }

            return mapped;
        });
        // Enrich với thông tin approval cho từng Contract
        const dataWithApproval = await Promise.all(
            mappedData.map(async cc => {
                const approvalInfo = await this.approvalRequestService.getApprovalInfoForEntity(
                    cc.id,
                    NSApproval.ApprovalBusinessType.CONTACT_CARE,
                );
                return {
                    ...cc,
                    approval: approvalInfo,
                };
            }),
        );

        return {
            data: dataWithApproval,
            total: result.total,
        };
    }

    @DefTransaction()
    async createCustomerContact(contact: CreateCustomerContactDto) {
        const { tenantId, memberId } = clientSessionContext;
        const check = await this.customerRepo.findOne({
            where: {
                id: contact.customerId,
                tenantId,
            },
        });
        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin khách hàng');
        }

        const { ...contactData } = contact as any;

        let code = contact.code;
        if (!code) {
            code = await generateCodeHelper.generateSequentialCode(
                'TH',
                this.contactCareRepo,
                clientSessionContext.tenantId,
            );
        }

        const newContact = await this.contactCareRepo.save({
            ...contactData,
            supervisorMemberId: contact.supervisorMemberId,
            assignedMemberIds: contact.assignedMemberIds,
            code: code,
            createdBy: memberId,
            tenantId,
        });

        try {
            const memberIdsRaw = [
                ...(Array.isArray(contact.assignedMemberIds) ? contact.assignedMemberIds : []),
                contact.supervisorMemberId,
            ];
            const memberIds = Array.from(
                new Set(memberIdsRaw.map(id => (id || '').trim()).filter(Boolean)),
            );
            if (memberIds.length) {
                await this.notificationService.createNotification({
                    source: NSNotification.ESource.CUSTOMER,
                    type: NSNotification.EType.SYSTEM_MESSAGE,
                    severity: NSNotification.ESeverity.LOW,
                    title: `Thăm hỏi mới: ${contact.title || ''}`,
                    content: `Khách hàng ${check?.name || ''}. Mô tả: ${contact.description || ''}`,
                    recipientType: NSNotification.ERecipientType.MEMBER,
                    memberIds,
                    sendInApp: true,
                    sendEmail: false,
                    tenantId,
                    createdBy: memberId,
                    linkPath: '/customer-care/contact',
                    linkId: newContact.id,
                });
            }
        } catch (error: any) {}

        try {
            await this.approvalRequestService.createRequestForDocument(
                {
                    businessType: NSApproval.ApprovalBusinessType.CONTACT_CARE,
                    documentId: newContact.id,
                    documentCode: code,
                    createdBy: memberId,
                    title: `Duyệt thăm hỏi khách hàng ${code}`,
                    initialStatus: NSApproval.ApprovalRequestStatus.PENDING,
                    finalStatuses: [
                        NSApproval.ApprovalRequestStatus.APPROVED,
                        NSApproval.ApprovalRequestStatus.REJECTED,
                    ],
                    approvedStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                },
                // { skipIfNoConfig: true }, // Skip nếu không có config
            );
        } catch (error) {}

        return {
            message: 'Tạo thăm hỏi khách hàng thành công',
            data: newContact,
        };
    }

    @DefTransaction()
    async detailCustomerContact(params: DetailCustomerContactDto) {
        const { id } = params;
        const { tenantId } = clientSessionContext;
        if (!id) {
            throw new BusinessException('ID is required');
        }

        const contact = await this.contactCareRepo.findOne({
            where: {
                id,
                tenantId,
            },
        });

        if (!contact) {
            throw new BusinessException('Không tìm thấy thông tin thăm hỏi khách hàng');
        }

        // Fetch customer and member data for mapping
        const customerIds = contact.customerId ? [contact.customerId] : [];
        const supervisorMemberIds = contact.supervisorMemberId ? [contact.supervisorMemberId] : [];
        const assignedEmployeeIds = Array.isArray(contact.assignedMemberIds)
            ? contact.assignedMemberIds
            : [];

        const [customers, members] = await Promise.all([
            customerIds.length > 0
                ? this.customerRepo.find({ where: { id: In(customerIds), tenantId } })
                : Promise.resolve([]),
            [...supervisorMemberIds, ...assignedEmployeeIds].length > 0
                ? this.memberRepo.find({
                      where: { id: In([...supervisorMemberIds, ...assignedEmployeeIds]), tenantId },
                  })
                : Promise.resolve([]),
        ]);

        const customerMap = new Map(customers.map(c => [c.id, c]));
        const memberMap = new Map(members.map(m => [m.id, m]));

        const customer = contact.customerId ? customerMap.get(contact.customerId) : null;

        return {
            ...contact,
            customerCode: customer?.code || '',
            customerName: customer?.name || '',
            sapCode: customer?.sapCode || '',
            supervisorName: contact.supervisorMemberId
                ? memberMap.get(contact.supervisorMemberId)?.fullName || ''
                : '',
            assignedEmployeeName:
                Array.isArray(contact.assignedMemberIds) && contact.assignedMemberIds.length > 0
                    ? contact.assignedMemberIds
                          .map(id => memberMap.get(id.trim())?.fullName)
                          .filter(Boolean)
                          .join(', ')
                    : '',
        };
    }

    @DefTransaction()
    async updateCustomerContact(contact: UpdateCustomerContactDto) {
        const { id, ...contactData } = contact;
        const { tenantId, memberId } = clientSessionContext;

        const { ...contactDataWithoutLocation } = contactData as any;
        const check = await this.contactCareRepo.findOne({
            where: { id, tenantId: clientSessionContext.tenantId },
        });
        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin thăm hỏi khách hàng');
        }
        const customer = await this.customerRepo.findOne({
            where: { id: check.customerId, tenantId: clientSessionContext.tenantId },
        });
        if (!customer) {
            throw new BusinessException('Không tìm thấy thông tin khách hàng');
        }
        const updatedContact = await this.contactCareRepo.update(id, {
            ...contactDataWithoutLocation,
            supervisorMemberId: contact.supervisorMemberId,
            assignedMemberIds: contact.assignedMemberIds,
            updatedBy: clientSessionContext.memberId,
            updatedDate: new Date(),
            id,
        });

        try {
            const memberIdsRaw = [
                ...(Array.isArray(contact.assignedMemberIds) ? contact.assignedMemberIds : []),
                contact.supervisorMemberId,
            ];
            const memberIds = Array.from(
                new Set(memberIdsRaw.map(id => (id || '').trim()).filter(Boolean)),
            );
            if (memberIds.length) {
                await this.notificationService.createNotification({
                    source: NSNotification.ESource.CUSTOMER,
                    type: NSNotification.EType.SYSTEM_MESSAGE,
                    severity: NSNotification.ESeverity.LOW,
                    title: `Cập nhật thăm hỏi: ${contact.title || ''}`,
                    content: `Khách hàng ${customer?.name || ''}. Mô tả: ${contact.description || ''}`,
                    recipientType: NSNotification.ERecipientType.MEMBER,
                    memberIds,
                    sendInApp: true,
                    sendEmail: false,
                    tenantId,
                    createdBy: memberId,
                });
            }
        } catch (error: any) {}

        return {
            message: 'Cập nhật thăm hỏi khách hàng thành công',
            data: updatedContact,
        };
    }

    @DefTransaction()
    async updateStatus(params: UpdateStatusCustomerContactDto) {
        const { id, status } = params;
        const check = await this.contactCareRepo.findOne({
            where: { id, tenantId: clientSessionContext.tenantId },
        });
        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin thăm hỏi khách hàng');
        }

        await this.contactCareRepo.update(id, {
            status: NSContactCare.EStatus[status],
            updatedBy: clientSessionContext.memberId,
            updatedDate: new Date(),
            id,
        });

        return {
            message: 'Cập nhật trạng thái thành công',
        };
    }

    @DefTransaction()
    async deleteCustomerContact(params: DeleteCustomerContactDto) {
        const { id } = params;
        const check = await this.contactCareRepo.findOne({
            where: { id, tenantId: clientSessionContext.tenantId },
        });
        if (!check) {
            throw new BusinessException('Không tìm thấy thông tin thăm hỏi khách hàng');
        }

        await this.contactCareRepo.delete(id);
        return { message: 'Xóa thăm hỏi khách hàng thành công' };
    }

    @DefTransaction()
    async deleteListCustomerContact(params: DeleteListCustomerContactDto) {
        const { ids } = params;
        const { tenantId } = clientSessionContext;

        await this.contactCareRepo.delete({
            id: In(ids),
            tenantId,
        });
        return { message: 'Xóa thăm hỏi khách hàng thành công' };
    }
}
