import { customAlphabet, nanoid } from 'nanoid';
import { v4 } from 'uuid';

const uuidNoDash = () => {
    return v4().replace(/-/g, '').toUpperCase();
};

const generateReferralCode = () => {
    return customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz')(8);
};

const generateTicketNumber = () => {
    return customAlphabet('0123456789')(6);
};

const generateCustomerCode = (prefix: string) => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const id = nanoid(10);
    return `${prefix}-${date}-${id.toUpperCase()}`;
};

const generateSequentialCode = async (
    prefix: string,
    repo: any,
    tenantId?: string,
): Promise<string> => {
    // Tìm tất cả entities có code bắt đầu bằng prefix
    const whereCondition: any = {};

    if (tenantId) {
        whereCondition.tenantId = tenantId;
    }

    // Lấy tất cả entities để filter code có format đúng
    const entities = await repo.find({
        where: whereCondition,
    });

    let maxNumber = -1;

    // Tìm code lớn nhất có format: prefix + số (ví dụ: TH0000, TH0001)
    // Bỏ qua các code có format khác (như TH-20251201-...)
    for (const entity of entities) {
        if (entity?.code && entity.code.startsWith(prefix)) {
            const numberPart = entity.code.substring(prefix.length);
            // Chỉ xử lý nếu phần sau prefix là số thuần (không có dấu gạch ngang)
            if (/^\d+$/.test(numberPart)) {
                const parsedNumber = parseInt(numberPart, 10);
                if (!isNaN(parsedNumber) && parsedNumber > maxNumber) {
                    maxNumber = parsedNumber;
                }
            }
        }
    }

    // Tăng lên 1, bắt đầu từ 0 nếu không tìm thấy code nào
    const nextNumber = maxNumber + 1;

    // Format: TH0000, TH0001, TH0002, ...
    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
};

export const generateCodeHelper = {
    uuidNoDash,
    generateReferralCode,
    generateTicketNumber,
    generateCustomerCode,
    generateSequentialCode
};
