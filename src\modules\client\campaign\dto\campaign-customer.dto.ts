import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSCampaignCustomer } from '~/common/enums/campaign-customer.enum';

export class ListCampaignCustomerDto extends PageRequest {
    @ApiPropertyOptional({ example: '100000' })
    @IsOptional()
    @IsString()
    campaignId?: string;

    @ApiPropertyOptional({ example: '100000' })
    @IsOptional()
    @IsString()
    customerId?: string;

    @ApiPropertyOptional({ example: 'active' })
    @IsOptional()
    @IsString()
    status?: NSCampaignCustomer.EStatus;

    @ApiPropertyOptional({ example: '2023-01-01T00:00:00Z' })
    @IsOptional()
    @IsDateString()
    sendDate?: string;
}
