// src/common/helpers/date.helper.ts
export class DateHelper {
    /**
     * Set thời gian về đầu ngày (00:00:00.000)
     * @param date - Ngày cần set
     * @returns Date với thời gian là 00:00:00.000
     */
    static setStartOfDay(date: Date): Date {
        const newDate = new Date(date);
        newDate.setHours(0, 0, 0, 0);
        return newDate;
    }

    /**
     * Set thời gian về cuối ngày (23:59:59.999)
     * @param date - Ngày cần set
     * @returns Date với thời gian là 23:59:59.999
     */
    static setEndOfDay(date: Date): Date {
        const newDate = new Date(date);
        newDate.setHours(23, 59, 59, 999);
        return newDate;
    }

    /**
     * Tạo khoảng thời gian Between cho TypeORM với startDate và endDate
     * @param startDate - <PERSON><PERSON><PERSON> bắ<PERSON> đầu
     * @param endDate - Ngày kết thúc
     * @returns Object với startDate và endDate đã được format
     */
    static createDateRange(startDate: Date, endDate: Date) {
        return {
            startDate: this.setStartOfDay(startDate),
            endDate: this.setEndOfDay(endDate),
        };
    }

    /**
     * Tạo khoảng thời gian Between cho TypeORM với startDate và endDate
     * @param startDate - Ngày bắt đầu
     * @param endDate - Ngày kết thúc
     * @returns Array [startDate, endDate] để sử dụng với Between()
     */
    static createBetweenRange(startDate: string, endDate: string): [Date, Date] {
        return [this.setStartOfDay(new Date(startDate)), this.setEndOfDay(new Date(endDate))];
    }
}
