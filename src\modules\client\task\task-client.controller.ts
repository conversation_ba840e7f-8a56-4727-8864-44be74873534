import { Body, Param, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from 'nestjs-typeorm3-kit';
import { PermissionGuard } from '../@guards';
import { CreateTaskDto, UpdateTaskDto } from './dtos/create-task.dto';
import { TaskClientService } from './task-client.service';

@ApiTags('Client - Task')
@UseGuards(PermissionGuard)
@DefController('task')
export class TaskClientController {
    constructor(private readonly taskService: TaskClientService) {}

    @DefGet()
    // @RequirePermissions([PERMISSION_CODES.TASK.VIEW])
    @ApiOperation({ summary: 'Lấy danh sách task (theo quyền)' })
    async list() {
        return this.taskService.list();
    }

    @DefGet(':id')
    // @RequirePermissions([PERMISSION_CODES.TASK.VIEW])
    @ApiOperation({ summary: 'Xem chi tiết task' })
    async detail(@Param('id') id: string) {
        return this.taskService.detail(id);
    }

    @DefPost()
    // @RequirePermissions([PERMISSION_CODES.TASK.CREATE])
    // @ApiOperation({ summary: 'Tạo mới task' })
    async create(@Body() dto: CreateTaskDto) {
        return this.taskService.create(dto);
    }

    @DefPut(':id')
    // @RequirePermissions([PERMISSION_CODES.TASK.UPDATE])
    @ApiOperation({ summary: 'Cập nhật task (thông tin hoặc trạng thái)' })
    async update(@Param('id') id: string, @Body() dto: UpdateTaskDto) {
        return this.taskService.update(id, dto);
    }

    @DefDelete(':id')
    // @RequirePermissions([PERMISSION_CODES.TASK.DELETE])
    @ApiOperation({ summary: 'Xóa task' })
    async delete(@Param('id') id: string) {
        return this.taskService.delete(id);
    }
}
