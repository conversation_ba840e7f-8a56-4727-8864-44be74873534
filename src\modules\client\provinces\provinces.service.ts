import { Injectable } from '@nestjs/common';
import { apePublicApiConnector } from '~/connectors';
import { IPaginationProvinces } from './dto/provinces.dto';

const Endpoint = {
    pagination: '/api/public/provinces/pagination',
    getProvincesByCode: '/api/public/provinces/get-provinces-by-code',
};

@Injectable()
export class ProvincesService {
    async pagination(query: IPaginationProvinces) {
        return await apePublicApiConnector.get(Endpoint.pagination, query);
    }

    async getProvincesByCode(codes: string) {
        return await apePublicApiConnector.get(`${Endpoint.getProvincesByCode}?code=${codes}`);
    }
}
