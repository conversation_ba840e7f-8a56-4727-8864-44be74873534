import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import {
    CreateKpiMemberDto,
    GetActualsDto,
    GetKpiMemberListQueryDto,
    ReportAverageQueryDto,
    UpdateKpiMemberDto,
    UpdateStatusKpiMemberDto,
} from './dto/kpi-member.dto';
import { KpiMemberService } from './kpi-member.service';

@UseGuards(PermissionGuard)
@DefController('kpi-member')
export class KpiMemberController {
    constructor(private readonly service: KpiMemberService) {}

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async create(@Body() dto: CreateKpiMemberDto) {
        return this.service.create(dto);
    }

    @DefPost('update/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async update(@Param('id') id: string, @Body() dto: UpdateKpiMemberDto) {
        return this.service.update(id, dto);
    }

    @DefGet(':id')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async getDetail(@Param('id') id: string) {
        return this.service.getDetail(id);
    }

    @DefGet('')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async list(@Query() query: GetKpiMemberListQueryDto) {
        return this.service.list(query);
    }

    @DefGet('report/average')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async reportAverage(@Query() query: ReportAverageQueryDto) {
        return this.service.reportAverage(query);
    }

    @DefPost('actuals')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async getActuals(@Body() dto: GetActualsDto) {
        return this.service.getActualsForRange(
            dto.subjectType,
            dto.memberId,
            new Date(dto.periodStart),
            new Date(dto.periodEnd),
            dto.categoryKeys,
        );
    }

    @DefPost('update-status/:id')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async updateStatus(@Param('id') id: string, @Body() dto: UpdateStatusKpiMemberDto) {
        return this.service.updateStatus(id, dto.status);
    }
}
