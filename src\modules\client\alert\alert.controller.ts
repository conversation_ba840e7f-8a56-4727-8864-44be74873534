import { Body, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { AlertService } from './alert.service';
import {
    IAlertSummary,
    IListAlertsResponse,
    IMarkAsReadResponse,
    ListAlertsRequestDto,
    MarkAsReadRequestDto,
} from './dto/alert.dto';

@UseGuards(PermissionGuard)
@DefController('alerts')
export class AlertController {
    constructor(private readonly alertService: AlertService) {}

    @DefPost('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.VIEW])
    async list(@Body() data: ListAlertsRequestDto): Promise<IListAlertsResponse> {
        return this.alertService.list(data);
    }

    @DefPost('mark-as-read')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.UPDATE])
    async markAsRead(@Body() body: MarkAsReadRequestDto) {
        return this.alertService.markAsRead(body);
    }

    @DefPost('mark-all-as-read')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.UPDATE])
    async markAllAsRead(): Promise<IMarkAsReadResponse> {
        return this.alertService.markAllAsRead();
    }

    @DefGet('summary')
    @RequirePermissions([PERMISSION_CODES.SETTING_ALERTS.VIEW])
    async getSummary(): Promise<IAlertSummary> {
        return this.alertService.getSummary();
    }
}
