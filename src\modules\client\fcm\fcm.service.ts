import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { MemberRepo } from '~/domains/primary/member/member.repo';
import { RegisterFcmTokenDto } from './dto/fcm.dto';

@Injectable()
export class FcmService {
    constructor(
        @InjectRepo(MemberRepo) private readonly memberRepo: MemberRepo,
    ) { }

    async registerFcmToken(userId: string, dto: RegisterFcmTokenDto) {
        const user = await this.memberRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException('Tài khoản không tồn tại.');
        }
        const updateResult = await this.memberRepo.update(
            { id: userId },
            { fcmToken: dto.fcmToken },
        );
        return updateResult;
    }
}