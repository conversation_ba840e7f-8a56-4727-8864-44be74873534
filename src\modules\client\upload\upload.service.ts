import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { nanoid } from 'nanoid';
import { configEnv } from '~/@config/env';
import { NSMedia } from '~/common/enums';

@Injectable()
export class UploadService {
    AWS_S3_BUCKET_NAME: string;
    s3: AWS.S3;
    // Add your service methods here
    constructor() {
        const { AWS_S3_ACCESS_KEY_ID, AWS_S3_SECRET_ACCESS_KEY, AWS_S3_BUCKET_NAME } = configEnv();
        this.s3 = new AWS.S3({
            accessKeyId: AWS_S3_ACCESS_KEY_ID,
            secretAccessKey: AWS_S3_SECRET_ACCESS_KEY,
        });
        this.AWS_S3_BUCKET_NAME = AWS_S3_BUCKET_NAME;
    }

    /**
     * <PERSON><PERSON><PERSON> <PERSON><PERSON> media type từ MIME type
     */
    private getMediaTypeFromContentType(contentType: string): NSMedia.EType {
        if (!contentType) {
            return NSMedia.EType.DOCUMENT;
        }
        const mimeType = contentType.toLowerCase();
        if (
            mimeType.startsWith('image/')
        )
            return NSMedia.EType.IMAGE;
        if (
            mimeType.startsWith('video/')
        )
            return NSMedia.EType.VIDEO;
        if (
            mimeType.startsWith('audio/')
        )
            return NSMedia.EType.AUDIO;

        const documentTypes = ['pdf', 'msword', 'wordprocessingml', 'spreadsheetml', 'presentation', 'text/'];
        if (documentTypes.some(type => mimeType.includes(type))) {
            return NSMedia.EType.DOCUMENT;
        }
        return NSMedia.EType.DOCUMENT;
    }

    async uploadSingle(file: Express.Multer.File) {
        const current = new Date();
        let temp: string[] = file?.originalname ? file.originalname.split('.') : [];
        let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : '';
        let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3;
        let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}${ext}`;
        const key = `${LINK_UPLOAD_S3}/${fileName}`;

        const contentType = file.mimetype || '';
        const mediaType = this.getMediaTypeFromContentType(contentType);

        const params = {
            Bucket: this.AWS_S3_BUCKET_NAME,
            Key: key, // File name you want to save as in S3
            Body: file.buffer,
            ACL: 'public-read',
            ContentType: contentType || undefined,
        };
        return await new Promise<any>((resolve, reject) => {
            this.s3.upload(params, (err: any, data: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        fileName,
                        fileUrl: data.Location,
                        contentType,
                        mediaType,
                    });
                }
            });
        });
    }
}
