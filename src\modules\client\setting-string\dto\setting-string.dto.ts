import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsIn, IsString, ValidateNested } from 'class-validator';

class SettingPairDto {
    @ApiProperty({ type: 'string' })
    @IsString()
    key: string;

    @ApiProperty({})
    value: any;
}

export class SettingStringUpdateDto {
    @ApiProperty({ enum: ['string', 'option'] })
    @IsIn(['string', 'option'])
    type: 'string' | 'option';

    @ApiProperty({
        type: 'array',
        description:
            'Các cặp key-value của cấu hình mặc định',
        items: { $ref: '#/components/schemas/SettingPairDto' },
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => SettingPairDto)
    config: SettingPairDto[];
}
