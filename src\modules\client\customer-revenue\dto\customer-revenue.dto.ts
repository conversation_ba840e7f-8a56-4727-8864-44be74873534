import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CustomerRevenueDto {
    @ApiProperty({ description: 'Doanh thu', example: 1000000 })
    @IsNumber()
    revenue: number;

    @ApiProperty({ description: 'Mã khách hàng', example: 'KH001' })
    @IsString()
    contractCode: string;

    @ApiProperty({ description: 'Tên khách hàng', example: 'Công ty ABC' })
    @IsString()
    contractName: string;

    @ApiProperty({ description: 'Mã báo giá', example: 'BG001' })
    @IsString()
    @IsOptional()
    quotationNumber?: string;
}

export class ListCustomerRevenueDto {
    @ApiProperty({ description: 'ID khách hàng', example: 1 })
    @IsString()
    @IsOptional()
    customerId?: string;

    @ApiProperty({ description: 'Mã khách hàng', example: 'KH001' })
    @IsString()
    @IsOptional()
    customerCode?: string;

    @ApiProperty({ description: 'Tên khách hàng', example: 'Công ty ABC' })
    @IsString()
    @IsOptional()
    customerName?: string;

    @ApiProperty({ description: 'Mã báo giá', example: 'BG001' })
    @IsString()
    @IsOptional()
    quotationNumber?: string;

    @ApiProperty({ description: 'Ngày bắt đầu doanh thu (YYYY-MM-DD)', example: '2023-01-01' })
    @IsString()
    @IsOptional()
    revenueFrom?: number;

    @ApiProperty({ description: 'Ngày kết thúc doanh thu (YYYY-MM-DD)', example: '2023-12-31' })
    @IsString()
    @IsOptional()
    revenueTo?: number;

    @ApiProperty({ description: 'Ngày bắt đầu tạo báo giá (YYYY-MM-DD)', example: '2023-01-01' })
    @IsString()
    @IsOptional()
    createdDateFrom?: string;

    @ApiProperty({ description: 'Ngày kết thúc tạo báo giá (YYYY-MM-DD)', example: '2023-12-31' })
    @IsString()
    @IsOptional()
    createdDateTo?: string;
}
