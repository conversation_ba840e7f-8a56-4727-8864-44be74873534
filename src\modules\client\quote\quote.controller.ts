import { Body, Query, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';

import { DefController, DefGet, DefPost } from 'ape-nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { ListQuoteDto, QuoteItemDto, SendQuoteEmailDto, UpdateStatusDto } from './dto';
import { QuoteService } from './quote.service';

@UseGuards(PermissionGuard)
@UseGuards(ClientAuthGuard)
@DefController('quote')
export class QuoteController {
    constructor(private readonly quoteService: QuoteService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.VIEW])
    async list(@Query() dto: ListQuoteDto) {
        return this.quoteService.list(dto);
    }

    @DefGet('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.VIEW])
    async detail(@Query() dto: QuoteItemDto) {
        return this.quoteService.detail(dto);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.CREATE])
    @UseInterceptors(FilesInterceptor('file', 10))
    async create(@UploadedFiles() files: Express.Multer.File[], @Body() body: FormData) {
        return this.quoteService.create(body, files);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.UPDATE])
    @UseInterceptors(FilesInterceptor('file', 10))
    async update(@UploadedFiles() files: Express.Multer.File[], @Body() body: FormData) {
        return this.quoteService.update(body, files);
    }

    @DefPost('update-status')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.UPDATE])
    async updateStatus(@Body() dto: UpdateStatusDto) {
        return this.quoteService.updateStatus(dto);
    }

    @DefPost('sent-to-customer')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.UPDATE])
    async sendEmail(@Body() dto: SendQuoteEmailDto) {
        return this.quoteService.sendEmailToCustomer(dto);
    }
}
