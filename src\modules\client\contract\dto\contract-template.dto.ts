import { ApiProperty } from '@nestjs/swagger';
import { NSContractTemplate } from '~/common/enums/contract-template.enum';

export class ContractTemplateDTO {
    @ApiProperty({
        example: '12345678-1234-1234-1234-123456789012',
        description: 'ID mẫu hợp đồng',
    })
    id: string;

    @ApiProperty({
        example: '12345678-1234-1234-1234-123456789012',
        description: 'ID tenant',
    })
    tenantId: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'Mã mẫu hợp đồng' })
    code: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'Tên mẫu hợp đồng' })
    name: string;

    @ApiProperty({ example: '<p>Mẫu hợp đồng CNTT-0001</p>', description: 'Mô tả mẫu hợp đồng' })
    html: string;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái mẫu hợp đồng' })
    status: NSContractTemplate.EStatus;
}
export class CreateContractTemplateDto {
    @ApiProperty({ example: 'CNTT-0001', description: 'Mã mẫu hợp đồng' })
    code: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'Tên mẫu hợp đồng' })
    name: string;

    @ApiProperty({ example: '<p>Mẫu hợp đồng CNTT-0001</p>', description: 'Mô tả mẫu hợp đồng' })
    html: string;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái mẫu hợp đồng' })
    status: NSContractTemplate.EStatus = NSContractTemplate.EStatus.ACTIVE;
}
export class PaginationTemplateQueryDto extends ContractTemplateDTO {
    @ApiProperty({ example: 1, description: 'Số trang' })
    pageIndex: number;

    @ApiProperty({ example: 10, description: 'Số lượng trên mỗi trang' })
    pageSize: number;

    @ApiProperty({ example: '2023-01-01', description: 'Ngày bắt đầu' })
    dateFrom?: string | Date;

    @ApiProperty({ example: '2023-01-31', description: 'Ngày kết thúc' })
    dateTo?: string | Date;
}
