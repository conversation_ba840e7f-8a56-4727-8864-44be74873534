/// <reference types="node" />

declare namespace NodeJS {
    interface ProcessEnv {
        PORT: number;
        REQUEST_TIMEOUT: number;
        APPLICATION_CODE: string;
        // Swagger Config
        SWAGGER_TITLE: string;
        SWAGGER_DESCRIPTION: string;
        SWAGGER_VERSION: string;
        // DB Config
        DB_PRIMARY_TYPE: 'postgres' | 'mssql' | 'mysql';
        DB_PRIMARY_HOST: string;
        DB_PRIMARY_PORT: number;
        DB_PRIMARY_USERNAME: string;
        DB_PRIMARY_PASSWORD: string;
        DB_PRIMARY_DATABASE: string;
        DB_PRIMARY_SYNCHRONIZE: boolean;
        DB_PRIMARY_SSL: boolean;
        DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: boolean;
        // JWT HS256 config
        JWT_SECRET: string;
        JWT_EXPIRY: stringValue;
        JWT_REFRESH_TOKEN_SECRET: string;
        JWT_REFRESH_TOKEN_EXPIRY: stringValue;
        // APE AUTH
        APE_SSO_URL: string;
        APE_CLIENT_ID: string;
        APE_CLIENT_SECRET: string;
        APE_CALLBACK_URL: string;
        APE_CLIENT_CALLBACK_URL: string;
        APE_CRM_API_KEY: string;
        APE_PUBLIC_API_KEY: string;
        SCHEDULE_CRM_KEY: string;
        #S3;
        LINK_UPLOAD_S3: string;
        AWS_S3_BUCKET_NAME: string;
        AWS_S3_ACCESS_KEY_ID: string;
        AWS_S3_SECRET_ACCESS_KEY: string;
        // Mail SES Config
        SES_HOST: string;
        SES_PORT: number;
        SES_SECURE: boolean;
        SES_USER: string;
        SES_PASS: string;
        SES_FROM: string;
    }
}
