// src/domains/crm/catalog-service.entity.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSCatalog } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

// Danh mục catalog item
@Entity('catalog_item')
export class CatalogItemEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ type: 'uuid' })
    @Index()
    catalogId: string;

    @ApiProperty({ example: 'SRV-SEO-MONTHLY' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    code: string;

    @ApiProperty({ example: 'Gói SEO theo tháng' })
    @Column({ type: 'varchar', length: 255 })
    @Index()
    name: string;

    @ApiPropertyOptional({ example: 'Tối ưu SEO onpage + offpage' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    // đường dẫn ảnh (mới)
    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    imageUrl?: string | null;

    @ApiPropertyOptional({ example: 'kg' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    unit?: string | null; // đơn vị tính

    @ApiPropertyOptional({ example: 1000000 })
    @Column({ type: 'bigint', nullable: true })
    unitPrice?: number | null;

    @ApiProperty({ example: NSCatalog.EStatus.ACTIVE })
    @Column({ type: 'varchar', length: 64, default: NSCatalog.EStatus.ACTIVE })
    status: NSCatalog.EStatus;
}
