import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class PaginationDepartmentDto extends PageRequest {
    @ApiProperty({ description: 'Tenant ID', required: false })
    @IsOptional()
    @IsString()
    tenantId?: string;

    @ApiProperty({ description: 'Created date filter', required: false })
    @IsOptional()
    @IsString()
    createdForm?: string;

    @ApiProperty({ description: 'Created date filter', required: false })
    @IsOptional()
    @IsString()
    createdTo?: string;

    @ApiProperty({ description: 'Created by user filter', required: false })
    @IsOptional()
    @IsString()
    createdBy?: string;

    @ApiProperty({ description: 'Updated by user filter', required: false })
    @IsOptional()
    @IsString()
    updatedBy?: string;

    @ApiProperty({ description: 'Department name filter', required: false })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ description: 'Department code filter', required: false })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty({ description: 'Department description filter', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Parent department ID filter', required: false })
    @IsOptional()
    @IsString()
    parentId?: string;

    @ApiProperty({ description: 'Department status filter', required: false })
    @IsOptional()
    @IsString()
    status?: string;
}

export class CreateDepartmentDto {
    @ApiProperty({ description: 'Department name' })
    @IsNotEmpty()
    @IsString()
    name: string;

    @ApiProperty({ description: 'Department code' })
    @IsNotEmpty()
    @IsString()
    code: string;

    @ApiProperty({ description: 'Department description', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Parent department ID', required: false })
    @IsOptional()
    @IsString()
    parentId?: string;

    @ApiProperty({ description: 'Tenant ID', required: false })
    @IsOptional()
    @IsString()
    tenantId?: string;
}

export class UpdateDepartmentDto extends CreateDepartmentDto {
    @ApiProperty({ description: 'Department ID' })
    @IsNotEmpty()
    @IsString()
    id: string;
}

export class DeleteDepartmentDto {
    @ApiProperty({ description: 'Department ID' })
    @IsNotEmpty()
    @IsString()
    id: string;
}

export class FindDepartmentDto {
    @ApiProperty({ description: 'Department ID' })
    @IsNotEmpty()
    @IsString()
    id: string;
}
