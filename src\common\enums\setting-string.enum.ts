export namespace NSSettingString {
    export enum EDefaultConfigKey {
        EMAILS = 'EMAILS',
        LOGO = 'LOGO',
        APPLICATION_CODE = 'APPLICATION_CODE',
        NAME_COMPANY = 'NAME_COMPANY',
        WARNING_LEVEL_CRITICAL = 'WARNING_LEVEL_CRITICAL',
        WARNING_LEVEL_HIGH = 'WARNING_LEVEL_HIGH',
        WARNING_LEVEL_MEDIUM = 'WARNING_LEVEL_MEDIUM',
    }
    export const EDefaultConfig = {
        [EDefaultConfigKey.EMAILS]: '<EMAIL>',
        [EDefaultConfigKey.LOGO]: 'https://example.com/logo.png',
        [EDefaultConfigKey.APPLICATION_CODE]: 'CRM',
        [EDefaultConfigKey.NAME_COMPANY]: 'APE CRM',
        [EDefaultConfigKey.WARNING_LEVEL_CRITICAL]: 3,
        [EDefaultConfigKey.WARNING_LEVEL_HIGH]: 7,
        [EDefaultConfigKey.WARNING_LEVEL_MEDIUM]: 14,
    };
}
