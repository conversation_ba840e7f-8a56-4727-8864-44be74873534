import { InternalServerErrorException } from '@nestjs/common';
import { createTransport } from 'nodemailer';
import { IMailSender, MailConfig, SendMailOptionsDto } from './mail.types';

export class SmtpAdapter implements IMailSender {
    private readonly transporter;
    private readonly mailFrom: string;

    constructor(private readonly config: MailConfig) {
        if (!this.config.host || !this.config.user) {
            throw new InternalServerErrorException('SMTP cấu hình không hợp lệ.');
        }

        this.transporter = createTransport({
            host: this.config.host,
            port: this.config.port,
            secure: this.config.secure,
            auth: {
                user: this.config.user,
                pass: this.config.pass,
            },
        });
        this.mailFrom = this.config.from;
    }

    async sendMail(options: SendMailOptionsDto): Promise<void> {
        const mailOptions = { ...options, from: options.from || this.mailFrom };
        try {
            await this.transporter.sendMail(mailOptions);
        } catch (error) {
            console.error('Lỗi gửi email với SMTP:', error);
            throw error;
        }
    }
}
