// approval-request.service.ts
import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'ape-nestjs-typeorm3-kit';
import { Between, Brackets, ILike, In } from 'typeorm';

import { NSApproval } from '~/common/enums/approval.enum';
import { MemberRepo, RoleRepo } from '~/domains/primary';

import { NSMember } from '~/common/enums';
import { NSSettingString } from '~/common/enums/setting-string.enum';
import { ApprovalConfigMappingRepository } from '~/domains/primary/approval-config/approval-config-mapping.repo';
import { ApprovalConfigStepApproverEntity } from '~/domains/primary/approval-config/approval-config-step-approver.entity';
import { ApprovalConfigStepApproverRepository } from '~/domains/primary/approval-config/approval-config-step-approver.repo';
import { ApprovalConfigStepRepository } from '~/domains/primary/approval-config/approval-config-step.repo';
import { ApprovalConfigEntity } from '~/domains/primary/approval-config/approval-config.entity';
import { ApprovalConfigRepository } from '~/domains/primary/approval-config/approval-config.repo';
import { ApprovalRequestActionEntity } from '~/domains/primary/approval-config/approval-request-action.entity';
import { ApprovalRequestActionRepository } from '~/domains/primary/approval-config/approval-request-action.repo';
import { ApprovalRequestStepApproverSnapshotEntity } from '~/domains/primary/approval-config/approval-request-step-approver-snapshot.entity';
import { ApprovalRequestStepApproverSnapshotRepository } from '~/domains/primary/approval-config/approval-request-step-approver-snapshot.repo';
import { ApprovalRequestStepApproverEntity } from '~/domains/primary/approval-config/approval-request-step-approver.entity';
import { ApprovalRequestStepApproverRepository } from '~/domains/primary/approval-config/approval-request-step-approver.repo';
import { ApprovalRequestStepEntity } from '~/domains/primary/approval-config/approval-request-step.entity';
import { ApprovalRequestStepRepository } from '~/domains/primary/approval-config/approval-request-step.repo';
import { ApprovalRequestEntity } from '~/domains/primary/approval-config/approval-request.entity';
import { ApprovalRequestRepository } from '~/domains/primary/approval-config/approval-request.repo';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { PositionRepo } from '~/domains/primary/position/position.repo';
import { clientSessionContext } from '../client-session.context';
import { SettingStringService } from '../setting-string/setting-string.service';
import { ApprovalHookService } from './approval-hook.service';
import {
    ApproveRequestDto,
    CheckActorCanApproveStepDto,
    CreateRequestApprovalDto,
    ListApprovalRequestDto,
    ListStepApprovalRequestDto,
    RejectRequestDto,
} from './dto/approval-request.dto';

@Injectable()
export class ApprovalRequestService {
    constructor(
        @InjectRepo(ApprovalConfigRepository)
        private readonly approvalConfigRepo: ApprovalConfigRepository,
        @InjectRepo(ApprovalConfigStepRepository)
        private readonly approvalConfigStepRepo: ApprovalConfigStepRepository,
        @InjectRepo(ApprovalConfigStepApproverRepository)
        private readonly approvalConfigStepApproverRepo: ApprovalConfigStepApproverRepository,
        @InjectRepo(ApprovalRequestRepository)
        private readonly approvalRequestRepo: ApprovalRequestRepository,
        @InjectRepo(ApprovalRequestStepRepository)
        private readonly approvalRequestStepRepo: ApprovalRequestStepRepository,
        @InjectRepo(ApprovalRequestActionRepository)
        private readonly approvalRequestActionRepo: ApprovalRequestActionRepository,
        @InjectRepo(ApprovalConfigMappingRepository)
        private approvalConfigMappingRepo: ApprovalConfigMappingRepository,
        @InjectRepo(MemberRepo)
        private readonly memberRepo: MemberRepo,
        @InjectRepo(ApprovalRequestStepApproverRepository)
        private readonly approvalRequestStepApproverRepo: ApprovalRequestStepApproverRepository,
        @InjectRepo(DepartmentRepo)
        private readonly departmentRepo: DepartmentRepo,
        @InjectRepo(PositionRepo)
        private readonly positionRepo: PositionRepo,
        private readonly approvalHookService: ApprovalHookService,
        private readonly settingStringService: SettingStringService,
        private readonly approvalRequestStepApproverSnapshotRepo: ApprovalRequestStepApproverSnapshotRepository,
        @InjectRepo(RoleRepo)
        private readonly roleRepo: RoleRepo,
    ) {}

    /**
     * Lấy ra những request approval của 1 member
     */
    async listRequestApproval(dto: ListApprovalRequestDto) {
        const { pageIndex, pageSize } = dto;
        const wheres: any = { createdBy: clientSessionContext.ssoAccountId };
        if (dto.status) {
            wheres.status = dto.status;
        }
        if (dto.title) {
            wheres.title = ILike(`%${dto.title}%`);
        }
        if (dto.businessType) {
            wheres.businessType = dto.businessType;
        }
        if (dto.createdDateFrom && dto.createdDateTo) {
            wheres.createdDate = Between(dto.createdDateFrom, dto.createdDateTo);
        }
        const requests = await this.approvalRequestRepo.findPaginationByTenant(
            {
                where: wheres,
                relations: ['steps', 'steps.configStep', 'steps.configStep.approvers', 'actions'],
            },
            {
                pageIndex,
                pageSize,
            },
        );
        return requests;
    }

    /**
     * Xem chi tiết yêu cầu duyệt:
     * - Flow duyệt (steps)
     * - Lịch sử duyệt (actions)
     * - Tên người duyệt, phòng ban, vị trí (từ employee)
     */
    async detailRequest(requestId: string) {
        const { memberId } = clientSessionContext;
        const request = await this.approvalRequestRepo.findOneByTenant({
            where: { id: requestId },
            relations: ['steps', 'steps.configStep', 'steps.approvers', 'actions'],
        });

        if (!request) {
            throw new NotFoundException('Không tìm thấy yêu cầu duyệt');
        }

        // Lấy steps (runtime) có order ASC + kèm configStep + approvers
        const steps = await this.approvalRequestStepRepo.findByTenant({
            where: { requestId: request.id },
            relations: ['configStep', 'approvers'],
            order: { order: 'ASC' },
        });

        // Lấy actions (nếu muốn có thứ tự thời gian)
        const actions = await this.approvalRequestActionRepo.findByTenant({
            where: { requestId: request.id },
            order: { createdDate: 'ASC' },
        });

        // --- Thông tin người tạo ---
        let createdByDetail: any = null;

        if (request.createdBy) {
            const createdByInfo = await this.memberRepo.findOneByTenant({
                where: { ssoAccountId: request.createdBy },
                select: ['id', 'fullName', 'avatar', 'departmentId', 'positionId'],
            });

            if (createdByInfo) {
                const [createdDept, createdPos] = await Promise.all([
                    createdByInfo.departmentId
                        ? this.departmentRepo.findOneByTenant({
                              where: { id: createdByInfo.departmentId },
                              select: ['id', 'name'],
                          })
                        : null,
                    createdByInfo.positionId
                        ? this.positionRepo.findOneByTenant({
                              where: { id: createdByInfo.positionId },
                              select: ['id', 'name'],
                          })
                        : null,
                ]);
                createdByDetail = {
                    id: createdByInfo.id,
                    fullName: createdByInfo.fullName,
                    avatar: createdByInfo.avatar,
                    departmentName: createdDept?.name ?? null,
                    positionName: createdPos?.name ?? null,
                };
            }
        }

        // --- 1) Thu thập memberId cần load ---
        // 1.1: từ snapshot approver (approval_request_step_approver)
        const memberRefIdsFromSnapshot =
            steps
                ?.flatMap(s => s.approvers || [])
                .filter(a => a.approverType === NSApproval.ApproverType.MEMBER)
                .map(a => a.approverRefId) || [];

        // 1.2: từ actions (actorId – memberId)
        const actorIdsFromActions = actions?.map(a => a.actorId) || [];

        const allMemberIds = Array.from(
            new Set([...memberRefIdsFromSnapshot, ...actorIdsFromActions]),
        );

        let memberMap = new Map<string, any>();

        if (allMemberIds.length > 0) {
            const members = await this.memberRepo.findByTenant({
                where: { id: In(allMemberIds) },
                select: ['id', 'fullName', 'avatar', 'departmentId', 'positionId'],
            });

            memberMap = new Map(members.map(m => [m.id, m]));
        }

        const departmentCache = new Map<string, { id?: string; name?: string } | null>();
        const positionCache = new Map<string, { id?: string; name?: string } | null>();

        const buildEmployeeDataFromMember = async (member: any) => {
            if (!member) return null;
            let department: { id?: string; name?: string } | null = null;
            let position: { id?: string; name?: string } | null = null;
            if (member.departmentId) {
                if (departmentCache.has(member.departmentId)) {
                    department = departmentCache.get(member.departmentId) || null;
                } else {
                    const dept = await this.departmentRepo.findOneByTenant({
                        where: { id: member.departmentId },
                        select: ['id', 'name'],
                    });
                    departmentCache.set(member.departmentId, dept || null);
                    department = dept || null;
                }
            }
            if (member.positionId) {
                if (positionCache.has(member.positionId)) {
                    position = positionCache.get(member.positionId) || null;
                } else {
                    const pos = await this.positionRepo.findOneByTenant({
                        where: { id: member.positionId },
                        select: ['id', 'name'],
                    });
                    positionCache.set(member.positionId, pos || null);
                    position = pos || null;
                }
            }
            return {
                id: member.id,
                fullName: member.fullName,
                avatarUrl: member.avatar,
                // Nếu member không còn property phone thì có thể bỏ hoặc để null/undefined
                phone: member['phone'] || null,
                departmentName: department?.name ?? null,
                positionName: position?.name ?? null,
            };
        };

        // --- 3) Inject employee vào steps.approvers + actions ---
        // Dùng Promise.all để resolve async map
        const mappedSteps = await Promise.all(
            (steps || []).map(async step => ({
                ...step,
                warning: await this.getStepWarningStatus(step), // Thêm thông tin cảnh báo
                approvers: await Promise.all(
                    (step.approvers || []).map(async approver => {
                        if (approver.approverType !== NSApproval.ApproverType.MEMBER) {
                            return {
                                ...approver,
                                employee: null,
                            };
                        }
                        const member = memberMap.get(approver.approverRefId);
                        return {
                            ...approver,
                            employee: await buildEmployeeDataFromMember(member),
                        };
                    }),
                ),
                // Giữ lại configStep nếu FE cần
                configStep: step.configStep,
            })),
        );

        const mappedActions = await Promise.all(
            (actions || []).map(async action => {
                const member = memberMap.get(action.actorId);
                return {
                    ...action,
                    employee: await buildEmployeeDataFromMember(member),
                };
            }),
        );

        const result = {
            ...request,
            createdBy: createdByDetail,
            steps: mappedSteps,
            actions: mappedActions,
        };

        return result;
    }

    /**
     * Lấy danh sách các bước duyệt của member
     * @param title Tìm theo title
     * @param status Tìm theo status
     * @param createdDateFrom Tìm theo createdDateFrom
     * @param createdDateTo Tìm theo createdDateTo
     * @param businessType Tìm theo businessType
     * @returns
     */
    async listMyApprovalRequests(params: ListStepApprovalRequestDto) {
        const skip = (params.pageIndex - 1) * params.pageSize;
        const take = params.pageSize;
        const { title, status, createdDateFrom, createdDateTo, businessType } = params;

        const { memberId } = clientSessionContext;

        const pendingStepStatuses = [
            NSApproval.ApprovalStepStatus.IN_PROGRESS,
            NSApproval.ApprovalStepStatus.PENDING,
        ];

        const activeRequestStatuses = [
            NSApproval.ApprovalRequestStatus.IN_PROGRESS,
            NSApproval.ApprovalRequestStatus.PENDING,
        ];

        // Load member không dùng relations (vì MemberEntity không định nghĩa relations này hoặc để tối ưu)
        const member = await this.memberRepo.findOneByTenant({
            where: { id: memberId },
        });

        if (!member) {
            return { data: [], total: 0 };
        }

        const positionId = member.positionId;
        const departmentId = member.departmentId;
        // MemberEntity có column roleIds type text[], không phải relation roles
        const roleIds = member.roleIds || [];

        // Query với index tối ưu: Filter theo từng approverType từ runtime table
        const qb = this.approvalRequestStepRepo
            .createQueryBuilder('step')
            .innerJoinAndSelect('step.request', 'request')
            .leftJoin('step.approvers', 'approver')
            .where('step.status IN (:...pendingStepStatuses)', { pendingStepStatuses })
            .andWhere('request.status IN (:...activeRequestStatuses)', { activeRequestStatuses })
            .andWhere('approver.status = :pendingStatus', {
                pendingStatus: NSApproval.ApprovalStepStatus.PENDING,
            })
            .andWhere(
                new Brackets(qb => {
                    // MEMBER: check trực tiếp
                    qb.where(
                        '(approver.approverType = :memberType AND approver.memberRefId = :memberId)',
                        {
                            memberType: NSApproval.ApproverType.MEMBER,
                            memberId: memberId,
                        },
                    );

                    // POSITION: check real-time (có hoặc không có department)
                    if (positionId) {
                        if (departmentId) {
                            qb.orWhere(
                                '(approver.approverType = :positionType AND approver.positionRefId = :positionId AND (approver.departmentRefId IS NULL OR approver.departmentRefId = :departmentId))',
                                {
                                    positionType: NSApproval.ApproverType.POSITION,
                                    positionId: positionId,
                                    departmentId: departmentId,
                                },
                            );
                        } else {
                            qb.orWhere(
                                '(approver.approverType = :positionType AND approver.positionRefId = :positionId AND approver.departmentRefId IS NULL)',
                                {
                                    positionType: NSApproval.ApproverType.POSITION,
                                    positionId: positionId,
                                },
                            );
                        }
                    }

                    // DEPARTMENT: check real-time
                    if (departmentId) {
                        qb.orWhere(
                            '(approver.approverType = :deptType AND approver.departmentRefId = :departmentId)',
                            {
                                deptType: NSApproval.ApproverType.DEPARTMENT,
                                departmentId: departmentId,
                            },
                        );
                    }

                    // ROLE: check real-time
                    if (roleIds.length > 0) {
                        qb.orWhere(
                            '(approver.approverType = :roleType AND approver.roleRefId IN (:...roleIds))',
                            {
                                roleType: NSApproval.ApproverType.ROLE,
                                roleIds: roleIds,
                            },
                        );
                    }

                    // Backward compatibility: check approverRefId nếu không có các RefId riêng
                    qb.orWhere(
                        '(approver.approverType = :memberType AND approver.approverRefId = :memberId AND approver.memberRefId IS NULL AND approver.positionRefId IS NULL AND approver.roleRefId IS NULL AND approver.departmentRefId IS NULL)',
                        {
                            memberType: NSApproval.ApproverType.MEMBER,
                            memberId: memberId,
                        },
                    );
                }),
            );

        if (status) {
            qb.andWhere('request.status = :reqStatus', { reqStatus: status });
        }

        if (title) {
            qb.andWhere('request.title ILIKE :title', { title: `%${title}%` });
        }

        if (businessType) {
            qb.andWhere('request.businessType = :businessType', { businessType });
        }

        if (createdDateFrom && createdDateTo) {
            qb.andWhere('request.createdDate BETWEEN :from AND :to', {
                from: createdDateFrom,
                to: createdDateTo,
            });
        }

        qb.orderBy('request.createdDate', 'DESC')
            .groupBy('step.id, request.id') // Group để tránh duplicate khi có nhiều approvers match
            .skip(skip)
            .take(take);

        const [steps, total] = await qb.getManyAndCount();

        const data = steps.map(step => {
            const req = step.request;

            return {
                requestId: req.id,
                title: req.title,
                businessType: req.businessType,
                documentId: req.documentId,
                documentCode: req.documentCode,

                requestStatus: req.status,
                requestCreatedDate: req.createdDate,

                stepId: step.id,
                stepOrder: step.order,
                stepStatus: step.status,

                currentStepOrder: req.currentStepOrder,
            };
        });

        return { data, total };
    }

    /**
     * Kiểm tra xem có cấu hình duyệt sẵn sàng cho businessType không
     * @param businessType Loại nghiệp vụ
     * @param approvalConfigId (optional) ID config cụ thể nếu muốn check
     * @returns true nếu có config active và đầy đủ steps + approvers
     */
    async hasApprovalConfig(
        businessType: NSApproval.ApprovalBusinessType,
        approvalConfigId?: string,
    ): Promise<boolean> {
        try {
            let config: ApprovalConfigEntity | null = null;

            if (approvalConfigId) {
                config = await this.approvalConfigRepo.findOneByTenant({
                    where: { id: approvalConfigId, businessType, isActive: true },
                    relations: ['steps', 'steps.approvers'],
                });
            } else {
                const mapping = await this.approvalConfigMappingRepo.findOneByTenant({
                    where: { businessType, isDefault: true },
                });
                if (!mapping) return false;

                config = await this.approvalConfigRepo.findOneByTenant({
                    where: { id: mapping.approvalConfigId, isActive: true },
                    relations: ['steps', 'steps.approvers'],
                });
            }

            if (!config || !config.steps || config.steps.length === 0) {
                return false;
            }

            // Check tất cả steps đều có ít nhất 1 approver
            const hasAllApprovers = config.steps.every(
                step => step.approvers && step.approvers.length > 0,
            );

            return hasAllApprovers;
        } catch {
            return false;
        }
    }

    /**
     * Tạo yêu cầu duyệt (Approval Request) cho một chứng từ.
     *
     * @description
     * - Tự động sinh các bước duyệt dựa trên cấu hình duyệt đang active.
     * - Cho phép chỉ định cụ thể cấu hình duyệt nếu muốn.
     * - Lưu lại thông tin người tạo, mã chứng từ, tiêu đề… phục vụ hiển thị.
     *
     * @required documentId       ID chứng từ.
     * @required businessType     Nghiệp vụ của chứng từ.
     * @required finalStatuses    Danh sách trạng thái kết thúc.
     * @required approvedStatus   Trạng thái khi duyệt.
     * @required initialStatus    Trạng thái ban đầu.
     *
     * @optional approvalConfigId Nếu truyền → dùng đúng config này.
     *                            Nếu không truyền → lấy config đang active theo businessType.
     *
     * @optional documentCode     Mã chứng từ (nếu muốn lưu để hiển thị).
     * @optional createdBy        ID người tạo request (nếu cần override).
     * @optional title            Tiêu đề request hiển thị trong danh sách duyệt.
     *
     * @optional options.skipIfNoConfig Nếu true, return null khi không có config thay vì throw error
     *
     * @returns ApprovalRequestEntity sau khi được khởi tạo + các bước duyệt runtime, hoặc null nếu skipIfNoConfig=true và không có config.
     */
    @DefTransaction()
    async createRequestForDocument(
        params: CreateRequestApprovalDto,
        options?: { skipIfNoConfig?: boolean },
    ): Promise<ApprovalRequestEntity | null> {
        const {
            title,
            businessType,
            documentId,
            documentCode,
            createdBy,
            approvalConfigId,
            initialStatus,
            finalStatuses,
            approvedStatus,
        } = params;
        const skipIfNoConfig = options?.skipIfNoConfig ?? false;

        // Fix: Validate finalStatuses format
        if (
            finalStatuses &&
            (!Array.isArray(finalStatuses) || !finalStatuses.every(s => typeof s === 'string'))
        ) {
            throw new ForbiddenException('finalStatuses phải là mảng các chuỗi');
        }

        // Fix: Check duplicate request
        const existingRequest = await this.approvalRequestRepo.findOneByTenant({
            where: { documentId, businessType },
        });
        if (existingRequest) {
            // Kiểm tra request cũ đã kết thúc chưa
            const isFinished = existingRequest.finalStatuses?.includes(existingRequest.status);
            if (!isFinished) {
                throw new ForbiddenException(
                    'Đã tồn tại yêu cầu duyệt chưa kết thúc cho chứng từ này',
                );
            }
        }

        // 0. Mapping nghiệp vụ
        const mapping = await this.approvalConfigMappingRepo.findOneByTenant({
            where: { businessType, isDefault: true },
        });
        if (!mapping) {
            if (skipIfNoConfig) {
                return null;
            }
            throw new NotFoundException(
                `Không tìm thấy cấu hình duyệt mặc định cho nghiệp vụ: ${businessType}`,
            );
        }

        // 1. Xác định config sử dụng
        let config: ApprovalConfigEntity | null = null;

        if (approvalConfigId) {
            config = await this.approvalConfigRepo.findOneByTenant({
                where: { id: approvalConfigId, businessType, isActive: true },
                relations: ['steps', 'steps.approvers'],
                order: { steps: { order: 'ASC' } as any },
            });
            if (!config) {
                if (skipIfNoConfig) {
                    return null;
                }
                throw new NotFoundException(
                    `Không tìm thấy cấu hình duyệt với ID: ${approvalConfigId} hoặc cấu hình đã bị vô hiệu hóa`,
                );
            }
        } else {
            config = await this.approvalConfigRepo.findOneByTenant({
                where: { id: mapping.approvalConfigId, isActive: true },
                relations: ['steps', 'steps.approvers'],
                order: { steps: { order: 'ASC' } as any },
            });
            if (!config) {
                if (skipIfNoConfig) {
                    return null;
                }
                throw new NotFoundException(
                    `Cấu hình duyệt mặc định (ID: ${mapping.approvalConfigId}) không tồn tại hoặc đã bị vô hiệu hóa. Vui lòng kiểm tra lại mapping.`,
                );
            }
        }

        if (!config.steps || config.steps.length === 0) {
            if (skipIfNoConfig) {
                return null;
            }
            throw new NotFoundException('Cấu hình duyệt không có cấp duyệt nào');
        }

        // Fix: Validate tất cả steps đều có ít nhất 1 approver
        const stepsWithoutApprovers = config.steps.filter(
            step => !step.approvers || step.approvers.length === 0,
        );
        if (stepsWithoutApprovers.length > 0) {
            if (skipIfNoConfig) {
                return null;
            }
            const stepTitles = stepsWithoutApprovers.map(s => s.title).join(', ');
            throw new ForbiddenException(
                `Các bước duyệt sau không có người duyệt: ${stepTitles}. Vui lòng cập nhật cấu hình.`,
            );
        }

        // 2. Sắp xếp step theo order
        const orderedSteps = [...config.steps].sort((a, b) => a.order - b.order);
        const firstStep = orderedSteps[0];

        // 3. Tạo ApprovalRequest với metadata
        const request = await this.approvalRequestRepo.save({
            businessType,
            documentId,
            approvalConfigId: config.id,
            documentCode,
            status: initialStatus || 'IN_PROGRESS',
            // Metadata cho status management
            finalStatuses: finalStatuses || ['APPROVED', 'REJECTED', 'CANCELLED'],
            approvedStatus: approvedStatus || 'APPROVED',
            // Step info
            currentStepOrder: firstStep.order,
            currentStepId: firstStep.id,
            createdBy,
            title: title || this.getApprovalTitle(businessType, documentCode),
        } as Partial<ApprovalRequestEntity>);

        // 4. Tạo ApprovalRequestStep + snapshot approvers
        for (const stepConfig of orderedSteps) {
            const isFirstStep = stepConfig.id === firstStep.id;
            const startedAt = isFirstStep ? new Date() : null;
            // Tính expectedFinishedAt từ leadtime nếu có
            const expectedFinishedAt =
                stepConfig.leadtime && startedAt
                    ? new Date(startedAt.getTime() + stepConfig.leadtime * 60 * 60 * 1000)
                    : null;
            const requestStep = await this.approvalRequestStepRepo.save({
                requestId: request.id,
                configStepId: stepConfig.id,
                order: stepConfig.order,
                approvalLevelType: stepConfig.approvalLevelType,
                minApprovers: stepConfig.minApprovers,
                status:
                    stepConfig.id === firstStep.id
                        ? NSApproval.ApprovalStepStatus.IN_PROGRESS
                        : NSApproval.ApprovalStepStatus.PENDING,
                startedAt: stepConfig.id === firstStep.id ? new Date() : null,
                expectedFinishedAt,
            } as Partial<ApprovalRequestStepEntity>);

            // 4.1 Copy nguyên config approvers vào RUNTIME + Tạo snapshot
            if (stepConfig.approvers && stepConfig.approvers.length > 0) {
                for (const approverConfig of stepConfig.approvers) {
                    // Resolve department từ creator nếu useCreatorDepartment = true
                    let resolvedDepartmentId = approverConfig.departmentRefId;

                    if (approverConfig.useCreatorDepartment) {
                        // Lấy memberId từ clientSessionContext (người đang tạo request)
                        const { memberId } = clientSessionContext;

                        if (!memberId) {
                            console.warn(
                                `Không tìm thấy memberId trong session, không thể resolve department cho approver ${approverConfig.id}`,
                            );
                        } else {
                            // Load member để lấy department
                            const creator = await this.memberRepo.findOneByTenant({
                                where: { id: memberId },
                            });

                            if (creator?.departmentId) {
                                resolvedDepartmentId = creator.departmentId;
                            } else {
                                // Nếu creator không có department, log warning và giữ nguyên null
                                console.warn(
                                    `Creator ${memberId} không có department, không thể resolve department cho approver ${approverConfig.id}`,
                                );
                            }
                        }
                    }

                    // Copy nguyên config vào runtime table (không expand) với department đã resolve
                    await this.approvalRequestStepApproverRepo.save({
                        stepId: requestStep.id,
                        configStepApproverId: approverConfig.id,
                        approverType: approverConfig.approverType, // Giữ nguyên type
                        memberRefId: approverConfig.memberRefId,
                        positionRefId: approverConfig.positionRefId,
                        roleRefId: approverConfig.roleRefId,
                        departmentRefId: resolvedDepartmentId, // Dùng department đã resolve
                        // Backward compatibility
                        approverRefId:
                            approverConfig.memberRefId ||
                            approverConfig.positionRefId ||
                            approverConfig.roleRefId ||
                            resolvedDepartmentId,
                        status: NSApproval.ApprovalStepStatus.PENDING,
                    } as Partial<ApprovalRequestStepApproverEntity>);

                    // Tạo snapshot: Expand và lưu vào history table
                    // Tạo một approver config tạm với department đã resolve để expand
                    const approverConfigForExpand = {
                        ...approverConfig,
                        departmentRefId: resolvedDepartmentId,
                    };
                    const expandedMemberIds = await this.expandApprovers(approverConfigForExpand);

                    for (const memberId of expandedMemberIds) {
                        // Load member info để lưu vào snapshot
                        const member = await this.memberRepo.findOneByTenant({
                            where: { id: memberId },
                        });
                        const position = await this.positionRepo.findOneByTenant({
                            where: { id: member.positionId },
                        });
                        const department = await this.departmentRepo.findOneByTenant({
                            where: { id: member.departmentId },
                        });

                        if (member) {
                            await this.approvalRequestStepApproverSnapshotRepo.save({
                                requestId: request.id,
                                stepId: requestStep.id,
                                configStepApproverId: approverConfig.id,
                                memberId: memberId,
                                snapshotDate: new Date(),
                                memberFullName: member.fullName,
                                memberPositionId: member.positionId,
                                memberPositionName: position?.name,
                                memberDepartmentId: member.departmentId,
                                memberDepartmentName: department?.name,
                                approverType: approverConfig.approverType,
                                positionRefId: approverConfig.positionRefId,
                                departmentRefId: resolvedDepartmentId, // Dùng department đã resolve
                                roleRefId: approverConfig.roleRefId,
                            } as Partial<ApprovalRequestStepApproverSnapshotEntity>);
                        }
                    }
                }
            }
        }

        return this.approvalRequestRepo.findOneByTenant({
            where: { id: request.id },
            relations: ['steps'],
        });
    }

    /**
     * DUYỆT 1 REQUEST THEO STEP HIỆN TẠI
     *
     * @description
     * Hàm được sử dụng trong các service nghiệp vụ khi người dùng bấm nút "Duyệt".
     * Hệ thống sẽ xác định step hiện tại, kiểm tra quyền duyệt, ghi log hành động
     * và xử lý chuyển bước duyệt tiếp theo hoặc hoàn tất quy trình duyệt.
     *
     * Hook nghiệp vụ:
     * - Sau mỗi lần hoàn thành 1 step, hệ thống gọi approvalHookService.updateDocumentAfterApproval
     *   để nghiệp vụ xử lý tùy theo cấp duyệt.
     * - Tham số gửi vào hook bao gồm:
     *      @param businessType nghiệp vụ đang xử lý
     *      @param documentId   chứng từ tương ứng
     *      @param requestStatus trạng thái request sau khi duyệt
     *      @param stepOrder thứ tự step vừa duyệt xong
     *
     * Lưu ý:
     * - Nếu step hoàn tất và không còn step tiếp theo → request chuyển sang APPROVED.
     * - Nếu còn step → request vẫn IN_PROGRESS nhưng hook vẫn được gọi sau mỗi step.
     *
     * @param params.requestId   ID của request duyệt đang thao tác
     * @param params.comment      Comment người duyệt (nếu có)
     * @returns ApprovalRequestEntity sau khi cập nhật trạng thái
     */

    @DefTransaction()
    async approveRequest(params: ApproveRequestDto): Promise<ApprovalRequestEntity> {
        const { memberId } = clientSessionContext;
        const { requestId, comment } = params;
        const actorEmployeeId = memberId;

        // 1. Lấy lock trên bản ghi request để tránh race condition (không join để tránh lỗi FOR UPDATE với LEFT JOIN)
        const txRepo = this.approvalRequestRepo.manager.getRepository(ApprovalRequestEntity);
        await txRepo
            .createQueryBuilder('request')
            .where('request.id = :requestId', { requestId })
            .setLock('pessimistic_write')
            .getOne();
        // 2. Sau khi đã lock, load lại entity với relations
        const request = await txRepo.findOne({
            where: { id: requestId },
            relations: ['approvalConfig', 'steps'],
        });

        if (!request) {
            throw new NotFoundException('Không tìm thấy yêu cầu duyệt');
        }

        // Kiểm tra request đã kết thúc chưa (dựa vào finalStatuses đã lưu)
        if (request.finalStatuses?.includes(request.status)) {
            throw new ForbiddenException('Yêu cầu đã kết thúc, không thể duyệt thêm');
        }

        const currentSteps = request.steps || [];
        if (!currentSteps.length) {
            throw new NotFoundException('Không tìm thấy bước duyệt hiện tại');
        }

        // Fix: Tìm currentStep theo order thay vì configStepId
        const currentStep = currentSteps.find(s => s.order === request.currentStepOrder);
        if (!currentStep) {
            throw new NotFoundException('Không tìm thấy bước duyệt hiện tại');
        }

        // Lấy configStep + approvers
        const configStep = await this.approvalConfigStepRepo.findOneByTenant({
            where: { id: currentStep.configStepId },
            relations: ['approvers'],
        });

        if (!configStep) {
            throw new NotFoundException('Không tìm thấy cấu hình step duyệt');
        }

        // 2. Check actor có quyền duyệt step này không
        // 2.1 Lấy ra employee của actorEmployeeId
        const actorEmployee = await this.memberRepo.findOneByTenant({
            where: { id: actorEmployeeId },
        });
        if (!actorEmployee) {
            throw new NotFoundException('Không tìm thấy nhân viên');
        }
        const canApprove = await this.checkActorCanApproveStep({
            actorMemberId: actorEmployeeId,
            requestStepId: currentStep.id,
        });

        if (!canApprove) {
            throw new ForbiddenException('Bạn không có quyền duyệt ở bước này');
        }

        await this.approvalRequestActionRepo.save({
            requestId: request.id,
            stepId: currentStep.id,
            actorId: actorEmployeeId,
            action: NSApproval.ApprovalActionType.APPROVE,
            comment,
        } as Partial<ApprovalRequestActionEntity>);

        // 3. Kiểm tra actor đã hành động ở step này chưa (tránh ghi action trùng)
        const approvers = await this.approvalRequestStepApproverRepo.findByTenant({
            where: { stepId: currentStep.id },
        });

        // Logic tìm approver record khớp với actor (Member hoặc Position)
        // Cần load lại member + employee để check khớp
        const member = await this.memberRepo.findOneByTenant({
            where: { id: actorEmployeeId },
            select: ['id', 'positionId', 'departmentId', 'roleIds'],
        });

        if (!member) {
            throw new NotFoundException('Không tìm thấy nhân viên');
        }
        const position = member.positionId
            ? await this.positionRepo.findOneByTenant({
                  where: { id: member.positionId },
              })
            : null;
        const department = member.departmentId
            ? await this.departmentRepo.findOneByTenant({
                  where: { id: member.departmentId },
              })
            : null;
        const roles = member.roleIds
            ? await this.roleRepo.find({
                  where: { id: In(member.roleIds) },
              })
            : [];
        // Tìm approver record khớp với actor (real-time check từ runtime table)
        let matchedApprover = approvers.find(approver => {
            if (approver.status !== NSApproval.ApprovalStepStatus.PENDING) {
                return false;
            }

            if (approver.approverType === NSApproval.ApproverType.MEMBER) {
                if (approver.memberRefId === actorEmployeeId) {
                    return true;
                }
                // Backward compatibility: check approverRefId nếu không có memberRefId
                if (!approver.memberRefId && approver.approverRefId === actorEmployeeId) {
                    return true;
                }
            } else if (approver.approverType === NSApproval.ApproverType.POSITION) {
                if (position && position.id === approver.positionRefId) {
                    if (approver.departmentRefId) {
                        return department && department.id === approver.departmentRefId;
                    }
                    return true;
                }
            } else if (approver.approverType === NSApproval.ApproverType.DEPARTMENT) {
                return department && department.id === approver.departmentRefId;
            } else if (approver.approverType === NSApproval.ApproverType.ROLE) {
                return roles?.some(role => role.id === approver.roleRefId) || false;
            }

            return false;
        });

        if (matchedApprover) {
            // Tìm thấy approver record, update status
            // FIX: Dùng save thay vì update để có entity mới nhất trong transaction
            matchedApprover.status = NSApproval.ApprovalStepStatus.APPROVED;
            matchedApprover.actedAt = new Date();
            matchedApprover.comment = comment;
            await this.approvalRequestStepApproverRepo.save(matchedApprover);
        } else {
            throw new ForbiddenException(
                'Không tìm thấy approver record phù hợp. Có thể do thay đổi cấu hình sau khi tạo request.',
            );
        }

        // 4. Kiểm tra step đã "đủ điều kiện hoàn thành" chưa
        // FIX: Reload lại step với approvers mới nhất để đảm bảo có dữ liệu sau khi update
        const currentStepWithApprovers = await this.approvalRequestStepRepo.findOneByTenant({
            where: { id: currentStep.id },
            relations: ['approvers'],
        });
        if (!currentStepWithApprovers) {
            throw new NotFoundException('Không tìm thấy bước duyệt');
        }
        const stepCompleted = await this.checkStepCompletedAfterApprove(currentStepWithApprovers);
        if (!stepCompleted) {
            // Chưa đủ điều kiện hoàn thành step
            return this.approvalRequestRepo.findOneByTenant({
                where: { id: request.id },
                relations: ['steps'],
                order: { steps: { order: 'ASC' } as any },
            });
        }

        // 5. Nếu step đã hoàn thành -> update step + move to next
        await this.approvalRequestStepRepo.update(currentStep.id, {
            status: NSApproval.ApprovalStepStatus.APPROVED,
            finishedAt: new Date(),
        });

        // Tìm step tiếp theo
        const allSteps = await this.approvalRequestStepRepo.findByTenant({
            where: { requestId: request.id },
            order: { order: 'ASC' },
        });

        const nextStep = allSteps.find(s => s.order > currentStep.order);

        if (!nextStep) {
            // Không còn step nào → dùng approvedStatus từ metadata
            await this.approvalRequestRepo.update(request.id, {
                status: request.approvedStatus,
                currentStepId: null,
                currentStepOrder: null,
            });

            await this.approvalHookService.updateDocumentAfterApproval({
                businessType: request.businessType,
                documentId: request.documentId,
                requestStatus: NSApproval.ApprovalRequestStatus.APPROVED,
                orderStep: currentStep.order,
                numberStep: allSteps.length,
            });

            return this.approvalRequestRepo.findOneByTenant({
                where: { id: request.id },
                relations: ['steps'],
                order: { steps: { order: 'ASC' } as any },
            });
        }

        // Còn step tiếp theo -> active step đó
        // Load configStep để lấy leadtime
        const nextConfigStep = await this.approvalConfigStepRepo.findOneByTenant({
            where: { id: nextStep.configStepId },
        });

        const nextStartedAt = new Date();
        const nextExpectedFinishedAt = nextConfigStep?.leadtime
            ? new Date(nextStartedAt.getTime() + nextConfigStep.leadtime * 60 * 60 * 1000)
            : null;

        await this.approvalRequestStepRepo.update(nextStep.id, {
            status: NSApproval.ApprovalStepStatus.IN_PROGRESS,
            startedAt: nextStartedAt,
            expectedFinishedAt: nextExpectedFinishedAt,
        });

        // currentStepId phải là id của approval_config_step
        await this.approvalRequestRepo.update(request.id, {
            status: 'IN_PROGRESS',
            currentStepId: nextStep.configStepId,
            currentStepOrder: nextStep.order,
        });

        return this.approvalRequestRepo.findOneByTenant({
            where: { id: request.id },
            relations: ['steps'],
            order: { steps: { order: 'ASC' } as any },
        });
    }
    /**
     * TỪ CHỐI 1 REQUEST (STEP HIỆN TẠI)
     *
     * @description
     * - Được gọi khi người dùng bấm nút "Từ chối" trong quá trình duyệt.
     * - Kiểm tra quyền duyệt, ghi log hành động và chuyển request sang REJECTED.
     * - Gọi approvalHookService.updateDocumentAfterApproval để nghiệp vụ xử lý tùy theo cấp duyệt.
     *
     * @param params.requestId ID của request duyệt đang thao tác
     * @param params.comment Comment người duyệt (nếu có)
     * @returns ApprovalRequestEntity sau khi cập nhật trạng thái
     */
    @DefTransaction()
    async rejectRequest(params: RejectRequestDto): Promise<ApprovalRequestEntity> {
        const { requestId, comment } = params;
        const { memberId } = clientSessionContext;
        const actorEmployeeId = memberId;

        // Fix: Thêm lock để tránh race condition
        const request = await this.approvalRequestRepo
            .createQueryBuilder('request')
            // .setLock('pessimistic_write')
            .where('request.id = :requestId', { requestId })
            .leftJoinAndSelect('request.steps', 'steps')
            .getOne();

        if (!request) {
            throw new NotFoundException('Không tìm thấy request duyệt');
        }

        // Kiểm tra request đã kết thúc chưa (dựa vào finalStatuses đã lưu)
        if (request.finalStatuses?.includes(request.status)) {
            throw new ForbiddenException('Request đã kết thúc, không thể từ chối');
        }

        // Fix: Tìm currentStep theo order thay vì configStepId
        const currentStep = (request.steps || []).find(s => s.order === request.currentStepOrder);
        if (!currentStep) {
            throw new NotFoundException('Không tìm thấy bước duyệt hiện tại');
        }

        // Fix: Thêm check quyền duyệt khi reject
        const canApprove = await this.checkActorCanApproveStep({
            actorMemberId: actorEmployeeId,
            requestStepId: currentStep.id,
        });

        if (!canApprove) {
            throw new ForbiddenException('Bạn không có quyền từ chối ở bước này');
        }

        await this.approvalRequestActionRepo.save({
            requestId: request.id,
            stepId: currentStep.id,
            actorId: actorEmployeeId,
            action: NSApproval.ApprovalActionType.REJECT,
            comment,
        } as Partial<ApprovalRequestActionEntity>);

        // Update status của approver record từ runtime table
        const approvers = await this.approvalRequestStepApproverRepo.findByTenant({
            where: { stepId: currentStep.id },
        });

        const member = await this.memberRepo.findOneByTenant({
            where: { id: actorEmployeeId },
        });

        if (!member) {
            throw new NotFoundException('Không tìm thấy nhân viên');
        }

        // Tìm approver record khớp với actor (real-time check từ runtime table)
        let matchedApprover = approvers.find(approver => {
            if (approver.status !== NSApproval.ApprovalStepStatus.PENDING) {
                return false;
            }

            if (approver.approverType === NSApproval.ApproverType.MEMBER) {
                if (approver.memberRefId === actorEmployeeId) {
                    return true;
                }
                // Backward compatibility: check approverRefId nếu không có memberRefId
                if (!approver.memberRefId && approver.approverRefId === actorEmployeeId) {
                    return true;
                }
            } else if (approver.approverType === NSApproval.ApproverType.POSITION) {
                if (member.positionId === approver.positionRefId) {
                    if (approver.departmentRefId) {
                        return member.departmentId === approver.departmentRefId;
                    }
                    return true;
                }
            } else if (approver.approverType === NSApproval.ApproverType.DEPARTMENT) {
                return member.departmentId === approver.departmentRefId;
            } else if (approver.approverType === NSApproval.ApproverType.ROLE) {
                return member.roleIds?.includes(approver.roleRefId) || false;
            }

            return false;
        });

        if (matchedApprover) {
            // FIX: Dùng save thay vì update để có entity mới nhất trong transaction
            matchedApprover.status = NSApproval.ApprovalStepStatus.REJECTED;
            matchedApprover.actedAt = new Date();
            matchedApprover.comment = comment;
            await this.approvalRequestStepApproverRepo.save(matchedApprover);
        } else {
            throw new ForbiddenException(
                'Không tìm thấy approver record phù hợp. Có thể do thay đổi cấu hình sau khi tạo request.',
            );
        }

        await this.approvalRequestStepRepo.update(currentStep.id, {
            status: NSApproval.ApprovalStepStatus.REJECTED,
            finishedAt: new Date(),
        });

        // Reject luôn set status = "REJECTED"
        await this.approvalRequestRepo.update(request.id, {
            status: 'REJECTED',
        });

        // Fix: Thêm numberStep vào hook
        const allSteps = await this.approvalRequestStepRepo.findByTenant({
            where: { requestId: request.id },
            order: { order: 'ASC' },
        });

        // Update business document
        await this.approvalHookService.updateDocumentAfterApproval({
            businessType: request.businessType,
            documentId: request.documentId,
            requestStatus: 'REJECTED',
            orderStep: currentStep.order,
            numberStep: allSteps.length,
            comment,
        });

        return this.approvalRequestRepo.findOneByTenant({
            where: { id: request.id },
            relations: ['steps'],
            order: { steps: { order: 'ASC' } as any },
        });
    }

    /**
     * Expand approvers từ config sang danh sách member IDs cụ thể
     * - MEMBER: trả về chính memberId
     * - POSITION: tìm tất cả members có positionId này (và departmentId nếu có)
     * - DEPARTMENT: tìm tất cả members có departmentId này
     * - ROLE: tìm tất cả members có role này
     */
    private async expandApprovers(
        approverConfig: ApprovalConfigStepApproverEntity,
    ): Promise<string[]> {
        const { approverType, memberRefId, positionRefId, roleRefId, departmentRefId } =
            approverConfig;

        // MEMBER: trả về chính memberId
        if (approverType === NSApproval.ApproverType.MEMBER && memberRefId) {
            return [memberRefId];
        }

        // POSITION: tìm members có positionId (và departmentId nếu có)
        if (approverType === NSApproval.ApproverType.POSITION && positionRefId) {
            const where: any = {
                positionId: positionRefId,
                status: NSMember.EStatus.ACTIVE,
            };

            // Nếu có departmentRefId: filter theo position VÀ department
            // → "Giám đốc của phòng IT"
            if (departmentRefId) {
                where.departmentId = departmentRefId;
            }

            const members = await this.memberRepo.findByTenant({
                where,
                select: ['id'],
            });
            return members.map(m => m.id);
        }

        // DEPARTMENT: tìm tất cả members có departmentId
        if (approverType === NSApproval.ApproverType.DEPARTMENT && departmentRefId) {
            const members = await this.memberRepo.findByTenant({
                where: {
                    departmentId: departmentRefId,
                    status: NSMember.EStatus.ACTIVE,
                },
                select: ['id'],
            });
            return members.map(m => m.id);
        }

        // ROLE: tìm tất cả members có role này
        if (approverType === NSApproval.ApproverType.ROLE && roleRefId) {
            const members = await this.memberRepo
                .createQueryBuilder('member')
                .innerJoin('member.roles', 'role')
                .where('role.id = :roleId', { roleId: roleRefId })
                .andWhere('member.status = :status', { status: NSMember.EStatus.ACTIVE })
                .select('member.id')
                .getMany();
            return members.map(m => m.id);
        }

        // Backward compatibility: nếu vẫn dùng approverRefId (old format)
        if (approverConfig.approverRefId) {
            if (approverType === NSApproval.ApproverType.MEMBER) {
                return [approverConfig.approverRefId];
            }
            // Các trường hợp khác cần expand, nhưng không có đủ thông tin từ approverRefId
            // Nên trả về empty array
        }

        return [];
    }

    /**
     * CHECK: actor có thuộc danh sách approver của step không
     * - Tạm thời handle MEMBER + POSITION; nếu bạn có ROLE thì bổ sung thêm.
     */
    private async checkActorCanApproveStep(params: CheckActorCanApproveStepDto): Promise<boolean> {
        const { actorMemberId, requestStepId } = params;

        const [member, approvers] = await Promise.all([
            this.memberRepo.findOneByTenant({
                where: { id: actorMemberId },
                select: ['id', 'positionId'],
            }),
            this.approvalRequestStepApproverRepo.findByTenant({
                where: { stepId: requestStepId },
            }),
        ]);

        if (!member) return false;

        // MEMBER: approverRefId = member.id
        const matchMemberEntries = approvers.filter(
            a => a.approverType === 'MEMBER' && a.approverRefId === member.id,
        );

        // POSITION: approverRefId = employee.positionId
        // Fix async issue in some: pre-calculate or check synchronously if data is loaded
        // Here we already loaded employee.position, so we can check directly
        const position = member.positionId
            ? await this.positionRepo.findOneByTenant({
                  where: { id: member.positionId },
                  select: ['id'],
              })
            : null;
        const matchPositionEntries: any[] = [];
        if (position) {
            for (const a of approvers) {
                if (a.approverType === 'POSITION' && a.approverRefId === position.id) {
                    matchPositionEntries.push(a);
                }
            }
        }

        // TODO: nếu có ROLE thì check thêm ở đây

        const candidateEntries = [...matchMemberEntries, ...matchPositionEntries];
        if (!candidateEntries.length) return false;
        const alreadyActed = candidateEntries.some(
            a =>
                a.status === NSApproval.ApprovalStepStatus.APPROVED ||
                a.status === NSApproval.ApprovalStepStatus.REJECTED,
        );
        if (alreadyActed) return false;
        return true;
    }

    /**
     * Kiểm tra step đã đủ điều kiện hoàn thành sau khi 1 actor APPROVE hay chưa
     * - SINGLE: 1 approve là xong
     * - ANY: >= minApprovers (hoặc >=1 nếu chưa set)
     * - ALL: tất cả approver phải APPROVE
     * - SKIPPED: Không tính vào tổng số (coi như đã approve)
     */
    private async checkStepCompletedAfterApprove(
        step: ApprovalRequestStepEntity,
    ): Promise<boolean> {
        // Lấy danh sách approver snapshot
        const approvers = await this.approvalRequestStepApproverRepo.findByTenant({
            where: { stepId: step.id },
        });

        // Fix: Xử lý SKIPPED - coi như đã approve
        const approvedCount = approvers.filter(
            a =>
                a.status === NSApproval.ApprovalStepStatus.APPROVED ||
                a.status === NSApproval.ApprovalStepStatus.SKIPPED,
        ).length;
        const totalApprovers = approvers.length;

        // SINGLE: chỉ cần 1 người duyệt là xong
        if (step.approvalLevelType === NSApproval.ApprovalLevelType.SINGLE) {
            return approvedCount >= 1;
        }

        // ANY: số lượng approve >= minApprovers (mặc định 1)
        if (step.approvalLevelType === NSApproval.ApprovalLevelType.ANY) {
            const min = step.minApprovers && step.minApprovers > 0 ? step.minApprovers : 1;
            return approvedCount >= min;
        }

        // ALL: tất cả approvers phải approve (hoặc skipped)
        if (step.approvalLevelType === NSApproval.ApprovalLevelType.ALL) {
            if (totalApprovers === 0) return false;
            return approvedCount >= totalApprovers;
        }

        return false;
    }

    /**
     * Lấy tiêu đề cho request duyệt
     * @param businessType
     * @param documentCode
     * @returns
     */
    private getApprovalTitle(
        businessType: NSApproval.ApprovalBusinessType,
        documentCode?: string,
    ): string {
        return `${NSApproval.ApprovalBusinessTypeTitle[businessType]} - ${documentCode}`;
    }

    /**
     * Kiểm tra user hiện tại có quyền duyệt request không
     * Để làm ẩn hiện Button Approve/Reject trên UI
     * @param requestId
     * @returns true/false
     */
    async checkApprove(body: ApproveRequestDto): Promise<boolean> {
        const { memberId } = clientSessionContext;
        const { requestId } = body;
        const request = await this.approvalRequestRepo.findOneByTenant({
            where: { id: requestId },
            relations: ['steps', 'steps.configStep', 'steps.approvers', 'actions'],
        });
        if (!request) {
            throw new NotFoundException('Không tìm thấy yêu cầu duyệt');
        }

        // Kiểm tra request đã kết thúc chưa (dựa vào finalStatuses đã lưu)
        if (request.finalStatuses?.includes(request.status)) {
            return false;
        }

        // Fix: Tìm step hiện tại theo order thay vì configStepId
        const currentStep = request.steps.find(s => s.order === request.currentStepOrder);
        if (!currentStep) {
            return false;
        }

        // Kiểm tra memberId có trong danh sách approvers của step hiện tại không
        return this.checkActorCanApproveStep({
            actorMemberId: memberId,
            requestStepId: currentStep.id,
        });
    }

    /**
     * Lấy thông tin approval request theo entityId
     * @param entityId ID của entity nghiệp vụ (maintenance, incident, etc.)
     * @param businessType Loại nghiệp vụ
     * @returns ApprovalRequestEntity với quan hệ approvalConfig, steps và thông tin người duyệt
     */
    async getApprovalByEntity(entityId: string, businessType: NSApproval.ApprovalBusinessType) {
        return this.approvalRequestRepo.findOneByTenant({
            where: { documentId: entityId, businessType },
            relations: ['approvalConfig', 'steps', 'steps.configStep', 'steps.approvers'],
        });
    }

    /**
     * Lấy thông tin approval và quyền duyệt cho một entity
     * Hàm này dùng để enrich entity với thông tin approval, dùng cho list/detail
     *
     * @param entityId ID của entity nghiệp vụ
     * @param businessType Loại nghiệp vụ
     * @returns Object chứa thông tin approval và quyền của user hiện tại
     */
    async getApprovalInfoForEntity(
        entityId: string,
        businessType: NSApproval.ApprovalBusinessType,
    ): Promise<{
        approvalRequest: ApprovalRequestEntity | null;
        canApprove: boolean;
        canReject: boolean;
        approvalStatus: string | null;
        currentStepOrder: number | null;
        isFinished: boolean;
    }> {
        const { memberId } = clientSessionContext;

        // Lấy approval request
        const approvalRequest = await this.approvalRequestRepo.findOneByTenant({
            where: { documentId: entityId, businessType },
            relations: ['steps', 'steps.approvers'],
        });

        // Nếu không có approval request
        if (!approvalRequest) {
            return {
                approvalRequest: null,
                canApprove: false,
                canReject: false,
                approvalStatus: null,
                currentStepOrder: null,
                isFinished: true,
            };
        }

        // Check request đã kết thúc chưa
        const isFinished = approvalRequest.finalStatuses?.includes(approvalRequest.status) ?? false;

        // Nếu đã kết thúc thì không thể duyệt/reject
        if (isFinished) {
            return {
                approvalRequest,
                canApprove: false,
                canReject: false,
                approvalStatus: approvalRequest.status,
                currentStepOrder: approvalRequest.currentStepOrder,
                isFinished: true,
            };
        }

        // Tìm step hiện tại
        const currentStep = (approvalRequest.steps || []).find(
            s => s.order === approvalRequest.currentStepOrder,
        );

        if (!currentStep) {
            return {
                approvalRequest,
                canApprove: false,
                canReject: false,
                approvalStatus: approvalRequest.status,
                currentStepOrder: approvalRequest.currentStepOrder,
                isFinished: false,
            };
        }

        // Check quyền duyệt
        const canApprove = await this.checkActorCanApproveStep({
            actorMemberId: memberId,
            requestStepId: currentStep.id,
        });

        // Có thể reject nếu có quyền duyệt (thường là cùng quyền)
        const canReject = canApprove;

        return {
            approvalRequest,
            canApprove,
            canReject,
            approvalStatus: approvalRequest.status,
            currentStepOrder: approvalRequest.currentStepOrder,
            isFinished: false,
        };
    }

    /**
     * Kiểm tra trạng thái cảnh báo của step dựa trên leadtime
     * @param step ApprovalRequestStepEntity
     * @returns Object chứa thông tin cảnh báo
     */
    async getStepWarningStatus(step: ApprovalRequestStepEntity): Promise<{
        isWarning: boolean;
        isOverdue: boolean;
        remainingHours?: number;
        overdueHours?: number;
        message?: string;
    }> {
        // Chỉ check khi step đang IN_PROGRESS và có expectedFinishedAt
        if (step.status !== NSApproval.ApprovalStepStatus.IN_PROGRESS || !step.expectedFinishedAt) {
            return {
                isWarning: false,
                isOverdue: false,
            };
        }

        const now = new Date();
        const expectedDate = new Date(step.expectedFinishedAt);
        const diffMs = expectedDate.getTime() - now.getTime();
        const diffHours = diffMs / (1000 * 60 * 60);

        // Đã quá hạn
        if (diffHours < 0) {
            return {
                isWarning: true,
                isOverdue: true,
                overdueHours: Math.abs(diffHours),
                message: `Bước duyệt đã quá hạn ${Math.ceil(Math.abs(diffHours))} giờ`,
            };
        }

        // Lấy warning threshold từ setting string (follow theo module cảnh báo hệ thống)
        // Sử dụng WARNING_LEVEL_CRITICAL (đơn vị ngày) và đổi sang giờ
        const warningThresholdSetting = await this.settingStringService.getDefaultValueByKey(
            'string',
            NSSettingString.EDefaultConfigKey.WARNING_LEVEL_CRITICAL,
        );
        const warningDays = warningThresholdSetting
            ? parseInt(String(warningThresholdSetting), 10)
            : 0;

        // Đổi từ ngày sang giờ
        const warningThresholdHours = warningDays * 24;

        // Sắp hết hạn (còn < WARNING_THRESHOLD_HOURS giờ)
        if (diffHours < warningThresholdHours) {
            return {
                isWarning: true,
                isOverdue: false,
                remainingHours: diffHours,
                message: `Bước duyệt sắp hết hạn, còn ${Math.ceil(diffHours)} giờ`,
            };
        }

        return {
            isWarning: false,
            isOverdue: false,
            remainingHours: diffHours,
        };
    }
}
