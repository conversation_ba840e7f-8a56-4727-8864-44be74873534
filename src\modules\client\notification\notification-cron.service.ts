import { Injectable, Logger } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { NSNotification } from '~/common/enums/notification.enum';
import { NotificationRepo } from '~/domains/primary/notification/notification.repo';
import { NotificationService } from './notification.service';

@Injectable()
export class NotificationCronService {
    private readonly logger = new Logger(NotificationCronService.name);

    constructor(
        @InjectRepo(NotificationRepo) private readonly notificationRepo: NotificationRepo,
        private readonly notificationService: NotificationService,
    ) {}

    // @Cron(CronExpression.EVERY_HOUR)
    async processQueuedNotifications() {
        try {
            const rows = await this.notificationRepo
                .createQueryBuilder('n')
                .select('n.tenantId', 'tenantId')
                .where('n.status = :status', { status: NSNotification.EStatus.QUEUED })
                .distinct(true)
                .getRawMany<{ tenantId: string }>();

            for (const row of rows) {
                if (!row?.tenantId) continue;
                await this.notificationService.processDueNotificationsForTenant(row.tenantId);
            }
        } catch (error: any) {
            this.logger.error(`Notification cron error: ${error?.message || 'ERROR'}`);
        }
    }
}
