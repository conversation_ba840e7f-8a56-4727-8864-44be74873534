import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class AnalyticsFilterDto {
    @ApiProperty({ description: '<PERSON><PERSON><PERSON> b<PERSON>t đầu (YYYY-MM-DD)', required: false })
    @IsString()
    @IsOptional()
    dateFrom?: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)', required: false })
    @IsString()
    @IsOptional()
    dateTo?: string;

    @ApiProperty({ description: 'ID member', required: false })
    @IsString()
    @IsOptional()
    memberId?: string;

    @ApiProperty({ description: 'ID khách hàng', required: false })
    @IsString()
    @IsOptional()
    customerId?: string;

    @ApiProperty({ description: 'Trạng thái/loại khách hàng', required: false })
    @IsString()
    @IsOptional()
    customerType?: string;

    @ApiProperty({
        description: '<PERSON> kỳ',
        enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
        required: false,
    })
    @IsString()
    @IsOptional()
    period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

export class CustomerTypeAnalytics {
    @ApiProperty({ description: 'Loại khách hàng' })
    customerType: string;

    @ApiProperty({ description: 'Số lượng' })
    count: number;

    @ApiProperty({ description: 'Tỷ lệ (%)' })
    percentage: number;
}

export class EmployeeCustomerAnalytics {
    @ApiProperty({ description: 'ID member' })
    employeeId: string;

    @ApiProperty({ description: 'Tên member' })
    employeeName: string;

    @ApiProperty({ description: 'Tổng số khách hàng' })
    customerCount: number;

    @ApiProperty({ description: 'Khách đã ký hợp đồng' })
    signedCustomers: number;
}

export class RevenueAnalytics {
    @ApiProperty({ description: 'Chu kỳ (ví dụ: 2025-01)' })
    period: string;

    @ApiProperty({ description: 'Doanh thu (VNĐ)' })
    revenue: number;

    @ApiProperty({ description: 'Số hợp đồng' })
    contracts: number;

    @ApiProperty({ description: 'Tăng trưởng (%)' })
    growth: number;
}

export class ComplaintTypeItem {
    @ApiProperty({ description: 'Loại khiếu nại' })
    type: string;

    @ApiProperty({ description: 'Số lượng' })
    count: number;

    @ApiProperty({ description: 'Tỷ lệ (%)' })
    percentage: number;
}

export class ComplaintAnalytics {
    @ApiProperty({ description: 'Tổng khiếu nại' })
    totalComplaints: number;

    @ApiProperty({ description: 'Mới' })
    newComplaints: number;

    @ApiProperty({ description: 'Chờ xử lý' })
    pendingComplaints: number;

    @ApiProperty({ description: 'Đang xử lý' })
    inProgressComplaints: number;

    @ApiProperty({ description: 'Đã giải quyết' })
    resolvedComplaints: number;

    @ApiProperty({ description: 'Tạm dừng' })
    onHoldComplaints: number;

    @ApiProperty({ description: 'Đã đóng' })
    closedComplaints: number;

    @ApiProperty({ description: 'Đã hủy' })
    cancelledComplaints: number;

    @ApiProperty({ type: [ComplaintTypeItem], description: 'Top loại khiếu nại' })
    topComplaintTypes: ComplaintTypeItem[];
}

export class ComplaintByMemberAnalytics {
    @ApiProperty({ description: 'ID member' })
    memberId: string;

    @ApiProperty({ description: 'Tên member' })
    memberName: string;

    @ApiProperty({ description: 'Tổng khiếu nại' })
    totalComplaints: number;

    @ApiProperty({ description: 'Đã giải quyết' })
    resolvedComplaints: number;

    @ApiProperty({ description: 'Tỷ lệ giải quyết (%)' })
    resolutionRate: number;
}
