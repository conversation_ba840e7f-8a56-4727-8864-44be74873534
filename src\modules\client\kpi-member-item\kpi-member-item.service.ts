import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { BusinessException } from '~/@systems/exceptions';
import { KpiMemberItemRepo } from '~/domains/primary/kpi-member-item/kpi-member-item.repo';
import { KpiMemberRepo } from '~/domains/primary/kpi-member/kpi-member.repo';
import { clientSessionContext } from '../client-session.context';

@Injectable()
export class KpiMemberItemService {
    constructor(
        @InjectRepo(KpiMemberRepo) private readonly memberRepo: KpiMemberRepo,
        @InjectRepo(KpiMemberItemRepo) private readonly itemRepo: KpiMemberItemRepo,
    ) {}

    async listByTarget(targetId: string) {
        const { tenantId } = clientSessionContext;
        const header = await this.memberRepo.findOne({ where: { id: targetId, tenantId } });
        if (!header) throw new BusinessException('Không tìm thấy bộ KPI');
        return this.itemRepo.find({ where: { tenantId, targetId } });
    }

    @DefTransaction()
    async update(
        targetId: string,
        itemId: string,
        payload: { targetValue?: number; weight?: number; note?: string },
    ) {
        const { tenantId } = clientSessionContext;
        const header = await this.memberRepo.findOne({ where: { id: targetId, tenantId } });
        if (!header) throw new BusinessException('Không tìm thấy bộ KPI');
        const item = await this.itemRepo.findOne({ where: { id: itemId, tenantId, targetId } });
        if (!item) throw new BusinessException('Không tìm thấy mục KPI');
        await this.itemRepo.update(itemId, { ...item, ...payload, id: itemId });
        return { success: true };
    }
}
