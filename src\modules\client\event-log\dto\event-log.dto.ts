import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    IsUUID,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSEventLogs } from '~/common/enums/event-logs.enum';

/**
 * DTO để ghi lại một sự kiện vào nhật ký hệ thống
 */
export class LogEventDto {
    @ApiProperty({
        description: '<PERSON>ại thực thể liên quan',
        enum: NSEventLogs.EntityTypes,
        example: NSEventLogs.EntityTypes.QUOTE,
    })
    @IsEnum(NSEventLogs.EntityTypes)
    @IsNotEmpty()
    relatedEntityType: NSEventLogs.EntityTypes;

    @ApiProperty({
        description: 'ID của thực thể liên quan',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsUUID()
    @IsNotEmpty()
    relatedEntityId: string;

    @ApiProperty({
        description: '<PERSON><PERSON>i sự kiện',
        enum: NSEventLogs.EventTypes,
        example: NSEventLogs.EventTypes.CREATED,
    })
    @IsEnum(NSEventLogs.EventTypes)
    @IsNotEmpty()
    eventType: NSEventLogs.EventTypes;

    @ApiPropertyOptional({
        description: 'Chi tiết bổ sung của sự kiện',
        example: { oldStatus: 'PENDING', newStatus: 'IN_PROGRESS' },
    })
    @IsOptional()
    @IsObject()
    details?: Record<string, any>;
}

/**
 * DTO để lấy lịch sử sự kiện theo ID của một thực thể
 */
export class GetHistoryByEntityDto extends PageRequest {
    @ApiProperty({
        description: 'Loại thực thể',
        enum: NSEventLogs.EntityTypes,
        example: NSEventLogs.EntityTypes.QUOTE,
    })
    @IsEnum(NSEventLogs.EntityTypes)
    @IsNotEmpty()
    entityType: NSEventLogs.EntityTypes;

    @ApiProperty({
        description: 'ID của thực thể',
        example: '550e8400-e29b-41d4-a716-************',
    })
    @IsUUID()
    @IsNotEmpty()
    entityId: string;
}

/**
 * DTO để lấy lịch sử theo nhiều IDs của một loại thực thể
 */
export class GetHistoryByEntityIdsDto {
    @ApiProperty({
        description: 'Loại thực thể',
        enum: NSEventLogs.EntityTypes,
        example: NSEventLogs.EntityTypes.QUOTE,
    })
    @IsEnum(NSEventLogs.EntityTypes)
    @IsNotEmpty()
    entityType: NSEventLogs.EntityTypes;

    @ApiProperty({
        description: 'Mảng IDs của các thực thể',
        example: ['550e8400-e29b-41d4-a716-************'],
        type: [String],
    })
    @IsArray()
    @IsUUID('4', { each: true })
    @IsNotEmpty()
    entityIds: string[];
}

/**
 * DTO để lấy danh sách event logs với filter và phân trang
 */
export class ListEventLogDto extends PageRequest {
    @ApiPropertyOptional({
        description: 'Loại thực thể',
        enum: NSEventLogs.EntityTypes,
        example: NSEventLogs.EntityTypes.QUOTE,
    })
    @IsOptional()
    @IsEnum(NSEventLogs.EntityTypes)
    relatedEntityType?: NSEventLogs.EntityTypes;

    @ApiPropertyOptional({
        description: 'ID của thực thể liên quan',
        example: 'uuid-cua-entity',
    })
    @IsOptional()
    @IsUUID()
    relatedEntityId?: string;

    @ApiPropertyOptional({
        description:
            'Loại sự kiện (có thể là một phần của eventType, ví dụ: CREATED, UPDATED, STATUS_CHANGE)',
        example: 'CREATED',
    })
    @IsOptional()
    @IsString()
    eventType?: string;

    @ApiPropertyOptional({
        description: 'Người tạo event (userId/createdBy)',
        example: '<EMAIL>',
    })
    @IsOptional()
    @IsString()
    userId?: string;

    @ApiPropertyOptional({
        description: 'Ngày bắt đầu filter (ISO string)',
        example: '2024-01-01T00:00:00.000Z',
    })
    @IsOptional()
    @IsString()
    startDate?: string;

    @ApiPropertyOptional({
        description: 'Ngày kết thúc filter (ISO string)',
        example: '2024-12-31T23:59:59.999Z',
    })
    @IsOptional()
    @IsString()
    endDate?: string;
}
