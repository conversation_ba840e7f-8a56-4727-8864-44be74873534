import { Injectable, NotFoundException } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'ape-nestjs-typeorm3-kit';
import { DeepPartial, In, Like } from 'typeorm';

import { BusinessException } from '~/@systems/exceptions';
import { NSApproval } from '~/common/enums/approval.enum';
import { MemberRepo } from '~/domains/primary';
import { ApprovalConfigEntity } from '~/domains/primary/approval-config/approval-config.entity';
import { ApprovalConfigRepository } from '~/domains/primary/approval-config/approval-config.repo';

import { ApprovalConfigMappingRepository } from '~/domains/primary/approval-config/approval-config-mapping.repo';
import { ApprovalConfigStepApproverEntity } from '~/domains/primary/approval-config/approval-config-step-approver.entity';
import { ApprovalConfigStepApproverRepository } from '~/domains/primary/approval-config/approval-config-step-approver.repo';
import { ApprovalConfigStepRepository } from '~/domains/primary/approval-config/approval-config-step.repo';
import { ApprovalRequestStepRepository } from '~/domains/primary/approval-config/approval-request-step.repo';
import { ApprovalRequestRepository } from '~/domains/primary/approval-config/approval-request.repo';
import { DepartmentRepo } from '~/domains/primary/department/department.repo';
import { PositionRepo } from '~/domains/primary/position/position.repo';
import {
    CreateApprovalConfigDto,
    CreateApprovalConfigMappingDto,
    GetApprovalConfigDto,
    GetApprovalConfigMappingDto,
    UpdateApprovalConfigDto,
    UpdateApprovalConfigMappingDto,
} from './dto/approval.dto';

@Injectable()
export class ApprovalService {
    constructor(
        @InjectRepo(ApprovalConfigRepository)
        private readonly approvalConfigRepo: ApprovalConfigRepository,
        @InjectRepo(ApprovalConfigStepRepository)
        private readonly approvalConfigStepRepo: ApprovalConfigStepRepository,
        @InjectRepo(ApprovalConfigStepApproverRepository)
        private readonly approvalConfigStepApproverRepo: ApprovalConfigStepApproverRepository,
        @InjectRepo(ApprovalConfigMappingRepository)
        private readonly approvalConfigMappingRepo: ApprovalConfigMappingRepository,
        @InjectRepo(ApprovalRequestRepository)
        private readonly approvalRequestRepo: ApprovalRequestRepository,
        @InjectRepo(ApprovalRequestStepRepository)
        private readonly approvalRequestStepRepo: ApprovalRequestStepRepository,
        // Đổi lại thành memberRepo ở các source khác
        @InjectRepo(MemberRepo)
        private readonly memberRepo: MemberRepo,
        @InjectRepo(DepartmentRepo)
        private readonly departmentRepo: DepartmentRepo,
        @InjectRepo(PositionRepo)
        private readonly positionRepo: PositionRepo,
    ) {}

    /**
     * Lấy danh sách cấu hình duyệt
     */
    async getConfigs(dto: GetApprovalConfigDto) {
        const { pageSize, pageIndex, isActive, name, code, businessType } = dto;
        const wheres: any = {};
        if (name) {
            wheres.name = Like(`%${name}%`);
        }
        if (code) {
            wheres.code = Like(`%${code}%`);
        }
        if (businessType) {
            wheres.businessType = businessType;
        }
        if (isActive) {
            wheres.isActive = isActive;
        }
        return this.approvalConfigRepo.findPaginationByTenant(
            {
                where: wheres,
                order: {
                    isActive: 'DESC',
                    createdDate: 'DESC',
                },
            },
            { pageSize, pageIndex },
        );
    }

    /**
     * Chi tiết cấu hình duyệt
     */
    async getConfigDetail(id: string) {
        const data = await this.approvalConfigRepo.findOneByTenant({
            where: { id },
            relations: ['steps', 'steps.approvers'],
        });

        if (!data) {
            throw new NotFoundException('Không tìm thấy cấu hình duyệt');
        }

        // Lấy danh sách approverRefId cho kiểu MEMBER
        const memberRefIds = Array.from(
            new Set(
                data.steps
                    .flatMap(s => s.approvers)
                    .filter(a => a.approverType === NSApproval.ApproverType.MEMBER)
                    .map(a => a.approverRefId),
            ),
        );

        if (!memberRefIds.length) {
            return data;
        }

        /**
         * Đổi lại thành memberRepo
         * Viết lại logic lấy thông tin nhân viên + tên phòng ban, vị trí
         */
        const members = await this.memberRepo.find({
            where: { id: In(memberRefIds) },
            select: ['id', 'fullName', 'avatar', 'departmentId', 'positionId'],
        });

        const memberMap = new Map(members.map(m => [m.id, m]));

        // Fix: Dùng Promise.all để resolve async trong map
        const deptNameCache = new Map<string, string | null>();
        const posNameCache = new Map<string, string | null>();
        const stepsWithApprovers = await Promise.all(
            data.steps.map(async step => {
                const approvers = await Promise.all(
                    step.approvers.map(async approver => {
                        if (approver.approverType !== NSApproval.ApproverType.MEMBER) {
                            return {
                                ...approver,
                                employee: null,
                            };
                        }

                        const member = memberMap.get(approver.approverRefId);

                        if (!member) {
                            return {
                                ...approver,
                                employee: null,
                            };
                        }

                        let departmentName: string | null = null;
                        let positionName: string | null = null;
                        if (member.departmentId) {
                            if (deptNameCache.has(member.departmentId)) {
                                departmentName = deptNameCache.get(member.departmentId)!;
                            } else {
                                const dept = await this.departmentRepo.findOneByTenant({
                                    where: { id: member.departmentId },
                                    select: ['id', 'name'],
                                });
                                departmentName = dept?.name ?? null;
                                deptNameCache.set(member.departmentId, departmentName);
                            }
                        }
                        if (member.positionId) {
                            if (posNameCache.has(member.positionId)) {
                                positionName = posNameCache.get(member.positionId)!;
                            } else {
                                const pos = await this.positionRepo.findOneByTenant({
                                    where: { id: member.positionId },
                                    select: ['id', 'name'],
                                });
                                positionName = pos?.name ?? null;
                                posNameCache.set(member.positionId, positionName);
                            }
                        }

                        return {
                            ...approver,
                            employee: {
                                id: member.id,
                                name: member.fullName,
                                avatar: member.avatar,
                                departmentName,
                                positionName,
                            },
                        };
                    }),
                );

                return {
                    ...step,
                    approvers,
                };
            }),
        );

        const result = {
            ...data,
            steps: stepsWithApprovers,
        };

        return result;
    }

    /**
     * Tạo mới cấu hình duyệt kèm danh sách step + approver
     */
    @DefTransaction()
    async createConfig(dto: CreateApprovalConfigDto): Promise<ApprovalConfigEntity> {
        const { isDefault } = dto;

        const existingConfig = await this.approvalConfigRepo.findOneByTenant({
            where: { code: dto.code.trim() },
        });
        if (existingConfig) {
            throw new BusinessException('Mã cấu hình duyệt đã tồn tại');
        }

        // 0.1. Kiểm tra businessType đã tồn tại chưa (trong cùng tenant)
        const existingConfigByBusinessType = await this.approvalConfigRepo.findOne({
            where: { businessType: dto.businessType },
        });
        if (existingConfigByBusinessType) {
            throw new BusinessException(
                `Nghiệp vụ "${dto.businessType}" đã có cấu hình duyệt. Mỗi nghiệp vụ chỉ được có một cấu hình duyệt.`,
            );
        }
        // 1. Tạo config chính
        const config = await this.approvalConfigRepo.save({
            code: dto.code.trim(),
            name: dto.name.trim(),
            businessType: dto.businessType,
            description: dto.description,
            isActive: dto.isActive ?? true,
            groupKey: dto.businessType,
        });

        // Mặc định tạo 1 mapping nếu chưa có
        const mapping = await this.approvalConfigMappingRepo.findOneByTenant({
            where: { businessType: dto.businessType },
        });
        if (!mapping) {
            await this.approvalConfigMappingRepo.save({
                approvalConfigId: config.id,
                businessType: dto.businessType,
                isDefault: true,
            });
        } else {
            if (isDefault) {
                // Nếu là default thì update lại isDefault = false cho tất cả
                await this.approvalConfigMappingRepo.update(
                    {
                        businessType: dto.businessType,
                    },
                    {
                        isDefault: false,
                    },
                );
                // Tạo mapping mới
                await this.approvalConfigMappingRepo.save({
                    approvalConfigId: config.id,
                    businessType: dto.businessType,
                    isDefault: true,
                });
            }
        }

        // 2. Tạo các step và approver tương ứng
        if (dto.steps && dto.steps.length > 0) {
            // Fix: Validate approverRefId khi tạo config
            const memberRefIds: string[] = dto.steps.flatMap(s =>
                s.approvers
                    .filter(a => a.approverType === NSApproval.ApproverType.MEMBER)
                    .map(a => a.memberRefId || a.approverRefId) // Support cả old và new format
                    .filter(Boolean),
            );

            if (memberRefIds.length > 0) {
                const members = await this.memberRepo.find({
                    where: { id: In(memberRefIds) },
                    select: ['id'],
                });
                const memberIds = new Set(members.map(m => m.id));
                const invalidMemberIds = memberRefIds.filter(id => !memberIds.has(id));

                if (invalidMemberIds.length > 0) {
                    throw new BusinessException(
                        `Không tìm thấy member với ID: ${invalidMemberIds.join(', ')}`,
                    );
                }
            }

            for (const stepDto of dto.steps) {
                // 2.1 Tạo step
                const step = await this.approvalConfigStepRepo.save({
                    configId: config.id,
                    order: stepDto.order,
                    title: stepDto.title.trim(),
                    approvalLevelType: stepDto.approvalLevelType,
                    minApprovers: stepDto.minApprovers,
                    note: stepDto.note,
                    leadtime: stepDto.leadtime,
                });

                // 2.2 Tạo approver cho step
                if (stepDto.approvers && stepDto.approvers.length > 0) {
                    const approverEntities: DeepPartial<ApprovalConfigStepApproverEntity>[] =
                        stepDto.approvers.map(ap => {
                            // Map từ DTO sang Entity (hỗ trợ cả old và new format)
                            const entity: DeepPartial<ApprovalConfigStepApproverEntity> = {
                                stepId: step.id,
                                approverType: ap.approverType,
                            };

                            // Map từ approverRefId (old format) hoặc từ các RefId riêng (new format)
                            if (ap.approverRefId) {
                                // Backward compatibility: map từ approverRefId
                                if (ap.approverType === NSApproval.ApproverType.MEMBER) {
                                    entity.memberRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.POSITION) {
                                    entity.positionRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.ROLE) {
                                    entity.roleRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.DEPARTMENT) {
                                    entity.departmentRefId = ap.approverRefId;
                                }
                            } else {
                                // New format: dùng các RefId riêng
                                if (ap.memberRefId) entity.memberRefId = ap.memberRefId;
                                if (ap.positionRefId) entity.positionRefId = ap.positionRefId;
                                if (ap.roleRefId) entity.roleRefId = ap.roleRefId;
                                if (ap.departmentRefId) entity.departmentRefId = ap.departmentRefId;
                            }

                            // Set useCreatorDepartment flag
                            entity.useCreatorDepartment = ap.useCreatorDepartment ?? false;

                            // Validate: chỉ 1 trong các RefId có giá trị (trừ khi useCreatorDepartment = true, lúc đó departmentRefId có thể null)
                            const refIds = [
                                entity.memberRefId,
                                entity.positionRefId,
                                entity.roleRefId,
                                entity.departmentRefId,
                            ].filter(Boolean);
                            if (refIds.length !== 1 && !entity.useCreatorDepartment) {
                                throw new BusinessException(
                                    `Approver phải có đúng 1 RefId được set cho type ${ap.approverType}`,
                                );
                            }

                            // Validate: useCreatorDepartment chỉ hợp lệ với POSITION
                            if (
                                entity.useCreatorDepartment &&
                                ap.approverType !== NSApproval.ApproverType.POSITION
                            ) {
                                throw new BusinessException(
                                    `useCreatorDepartment chỉ hợp lệ với approverType = POSITION`,
                                );
                            }

                            // Validate: useCreatorDepartment yêu cầu positionRefId
                            if (entity.useCreatorDepartment && !entity.positionRefId) {
                                throw new BusinessException(
                                    `useCreatorDepartment yêu cầu positionRefId phải được set`,
                                );
                            }

                            return entity;
                        });
                    await Promise.all(
                        approverEntities.map(a => this.approvalConfigStepApproverRepo.save(a)),
                    );
                }
            }
        }

        // 3. Trả về config đã tạo (có thể load kèm steps nếu cần)
        return this.approvalConfigRepo.findOneByTenant({
            where: { id: config.id },
            relations: ['steps', 'steps.approvers'],
            order: {
                steps: {
                    order: 'ASC',
                },
            },
        });
    }

    /**
     * Cập nhật cấu hình duyệt.
     * Logic: update config, xóa hết step + approver cũ theo config, sau đó insert lại theo dto.steps
     */
    @DefTransaction()
    async updateConfig(dto: UpdateApprovalConfigDto): Promise<ApprovalConfigEntity> {
        // 0. Lấy config hiện tại kèm steps + approvers
        const currentConfig = await this.approvalConfigRepo.findOne({
            where: { id: dto.id },
            relations: ['steps', 'steps.approvers'],
        });

        if (!currentConfig) {
            throw new NotFoundException('Không tìm thấy cấu hình duyệt');
        }

        // // Fix: Check config đang được sử dụng bởi request nào không
        // const activeRequests = await this.approvalRequestRepo.count({
        //   where: { approvalConfigId: currentConfig.id },
        // })

        // if (activeRequests > 0) {
        //   throw new BusinessException(
        //     `Cấu hình đang được sử dụng bởi ${activeRequests} yêu cầu duyệt. Vui lòng vô hiệu hóa (isActive = false) thay vì cập nhật.`,
        //   )
        // }

        // 0. Kiểm tra code đã tồn tại chưa (trong cùng tenant, loại trừ chính nó)
        if (dto.code) {
            const existingConfigByCode = await this.approvalConfigRepo.findOne({
                where: { code: dto.code.trim() },
            });
            if (existingConfigByCode && existingConfigByCode.id !== currentConfig.id) {
                throw new BusinessException(`Mã cấu hình duyệt "${dto.code.trim()}" đã tồn tại`);
            }
        }

        // 0.1. Kiểm tra businessType đã tồn tại chưa (trong cùng tenant, loại trừ chính nó)
        if (dto.businessType) {
            const existingConfigByBusinessType = await this.approvalConfigRepo.findOne({
                where: { businessType: dto.businessType },
            });
            if (
                existingConfigByBusinessType &&
                existingConfigByBusinessType.id !== currentConfig.id
            ) {
                throw new BusinessException(
                    `Nghiệp vụ "${dto.businessType}" đã có cấu hình duyệt. Mỗi nghiệp vụ chỉ được có một cấu hình duyệt.`,
                );
            }
        }

        // Đảm bảo groupKey có giá trị (data cũ có thể chưa có)
        if (!currentConfig.groupKey) {
            currentConfig.groupKey = currentConfig.businessType as any;
        }

        // 1. Cập nhật thông tin chung của config (update in-place)
        currentConfig.name = dto.name?.trim() ?? currentConfig.name;
        currentConfig.businessType = dto.businessType ?? currentConfig.businessType;
        currentConfig.description = dto.description ?? currentConfig.description;
        // Cập nhật code nếu có
        if (dto.code) {
            currentConfig.code = dto.code.trim();
        }

        // Optional: nếu vẫn muốn tăng version mỗi lần sửa config
        currentConfig.version = (currentConfig.version ?? 1) + 1;

        await this.approvalConfigRepo.save(currentConfig);

        // 2. Nếu có gửi steps → clear steps cũ + approvers cũ, rồi tạo mới theo DTO
        // 2. Cập nhật steps: Tận dụng steps cũ để update data thay vì xóa đi tạo lại (tránh lỗi FK)
        if (dto.steps) {
            const oldSteps = currentConfig.steps?.sort((a, b) => a.order - b.order) ?? [];
            // Sắp xếp steps mới theo order (nếu có) hoặc giữ nguyên thứ tự mảng
            const newSteps = [...dto.steps].sort((a, b) => a.order - b.order);

            const loops = Math.max(oldSteps.length, newSteps.length);

            for (let i = 0; i < loops; i++) {
                const oldStep = oldSteps[i];
                const newStepDto = newSteps[i];

                if (oldStep && newStepDto) {
                    // UPDATE step hiện có
                    await this.approvalConfigStepRepo.update(oldStep.id, {
                        order: newStepDto.order,
                        title: newStepDto.title.trim(),
                        approvalLevelType: newStepDto.approvalLevelType,
                        minApprovers: newStepDto.minApprovers,
                        note: newStepDto.note,
                        leadtime: newStepDto.leadtime,
                    });

                    // Cập nhật approvers: Xóa cũ -> Thêm mới
                    await this.approvalConfigStepApproverRepo.delete({ stepId: oldStep.id });

                    if (newStepDto.approvers && newStepDto.approvers.length > 0) {
                        const approverEntities = newStepDto.approvers.map(ap => {
                            const entity: DeepPartial<ApprovalConfigStepApproverEntity> = {
                                stepId: oldStep.id,
                                approverType: ap.approverType,
                            };

                            // Map từ approverRefId (old) hoặc từ các RefId riêng (new)
                            if (ap.approverRefId) {
                                if (ap.approverType === NSApproval.ApproverType.MEMBER) {
                                    entity.memberRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.POSITION) {
                                    entity.positionRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.ROLE) {
                                    entity.roleRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.DEPARTMENT) {
                                    entity.departmentRefId = ap.approverRefId;
                                }
                            } else {
                                if (ap.memberRefId) entity.memberRefId = ap.memberRefId;
                                if (ap.positionRefId) entity.positionRefId = ap.positionRefId;
                                if (ap.roleRefId) entity.roleRefId = ap.roleRefId;
                                if (ap.departmentRefId) entity.departmentRefId = ap.departmentRefId;
                            }

                            // Set useCreatorDepartment flag
                            entity.useCreatorDepartment = ap.useCreatorDepartment ?? false;

                            // Validate: chỉ 1 trong các RefId có giá trị (trừ khi useCreatorDepartment = true)
                            const refIds = [
                                entity.memberRefId,
                                entity.positionRefId,
                                entity.roleRefId,
                                entity.departmentRefId,
                            ].filter(Boolean);
                            if (refIds.length !== 1 && !entity.useCreatorDepartment) {
                                throw new BusinessException(
                                    `Approver phải có đúng 1 RefId được set cho type ${ap.approverType}`,
                                );
                            }

                            // Validate: useCreatorDepartment chỉ hợp lệ với POSITION
                            if (
                                entity.useCreatorDepartment &&
                                ap.approverType !== NSApproval.ApproverType.POSITION
                            ) {
                                throw new BusinessException(
                                    `useCreatorDepartment chỉ hợp lệ với approverType = POSITION`,
                                );
                            }

                            // Validate: useCreatorDepartment yêu cầu positionRefId
                            if (entity.useCreatorDepartment && !entity.positionRefId) {
                                throw new BusinessException(
                                    `useCreatorDepartment yêu cầu positionRefId phải được set`,
                                );
                            }

                            return entity;
                        });
                        await Promise.all(
                            approverEntities.map(a => this.approvalConfigStepApproverRepo.save(a)),
                        );
                    }
                } else if (!oldStep && newStepDto) {
                    // CREATE step mới
                    const newStep = await this.approvalConfigStepRepo.save({
                        configId: currentConfig.id,
                        order: newStepDto.order,
                        title: newStepDto.title.trim(),
                        approvalLevelType: newStepDto.approvalLevelType,
                        minApprovers: newStepDto.minApprovers,
                        note: newStepDto.note,
                        leadtime: newStepDto.leadtime,
                    });

                    if (newStepDto.approvers && newStepDto.approvers.length > 0) {
                        const approverEntities = newStepDto.approvers.map(ap => {
                            const entity: DeepPartial<ApprovalConfigStepApproverEntity> = {
                                stepId: newStep.id,
                                approverType: ap.approverType,
                            };

                            // Map từ approverRefId (old) hoặc từ các RefId riêng (new)
                            if (ap.approverRefId) {
                                if (ap.approverType === NSApproval.ApproverType.MEMBER) {
                                    entity.memberRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.POSITION) {
                                    entity.positionRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.ROLE) {
                                    entity.roleRefId = ap.approverRefId;
                                } else if (ap.approverType === NSApproval.ApproverType.DEPARTMENT) {
                                    entity.departmentRefId = ap.approverRefId;
                                }
                            } else {
                                if (ap.memberRefId) entity.memberRefId = ap.memberRefId;
                                if (ap.positionRefId) entity.positionRefId = ap.positionRefId;
                                if (ap.roleRefId) entity.roleRefId = ap.roleRefId;
                                if (ap.departmentRefId) entity.departmentRefId = ap.departmentRefId;
                            }

                            // Set useCreatorDepartment flag
                            entity.useCreatorDepartment = ap.useCreatorDepartment ?? false;

                            // Validate: chỉ 1 trong các RefId có giá trị (trừ khi useCreatorDepartment = true)
                            const refIds = [
                                entity.memberRefId,
                                entity.positionRefId,
                                entity.roleRefId,
                                entity.departmentRefId,
                            ].filter(Boolean);
                            if (refIds.length !== 1 && !entity.useCreatorDepartment) {
                                throw new BusinessException(
                                    `Approver phải có đúng 1 RefId được set cho type ${ap.approverType}`,
                                );
                            }

                            // Validate: useCreatorDepartment chỉ hợp lệ với POSITION
                            if (
                                entity.useCreatorDepartment &&
                                ap.approverType !== NSApproval.ApproverType.POSITION
                            ) {
                                throw new BusinessException(
                                    `useCreatorDepartment chỉ hợp lệ với approverType = POSITION`,
                                );
                            }

                            // Validate: useCreatorDepartment yêu cầu positionRefId
                            if (entity.useCreatorDepartment && !entity.positionRefId) {
                                throw new BusinessException(
                                    `useCreatorDepartment yêu cầu positionRefId phải được set`,
                                );
                            }

                            return entity;
                        });
                        await Promise.all(
                            approverEntities.map(a => this.approvalConfigStepApproverRepo.save(a)),
                        );
                    }
                } else if (oldStep && !newStepDto) {
                    // DELETE step thừa
                    // Fix: Check step đang được dùng bởi request nào không
                    const stepsUsingConfig = await this.approvalRequestStepRepo.count({
                        where: { configStepId: oldStep.id },
                    });

                    if (stepsUsingConfig > 0) {
                        throw new BusinessException(
                            `Bước duyệt đang được sử dụng bởi ${stepsUsingConfig} yêu cầu duyệt. Không thể xóa.`,
                        );
                    }

                    try {
                        await this.approvalConfigStepRepo.delete(oldStep.id);
                    } catch (e) {
                        throw new BusinessException(`Không thể xóa bước duyệt: ${e.message}`);
                    }
                }
            }
        }

        // 3. Cập nhật mapping default cho businessType
        // Đảm bảo chỉ một config default cho mỗi businessType
        await this.approvalConfigMappingRepo.update(
            {
                businessType: currentConfig.businessType,
            },
            {
                isDefault: false,
            },
        );

        // Upsert mapping mới cho config hiện tại
        // Tùy entity mà bạn có thể dùng save thẳng, nếu có unique index (businessType, approvalConfigId)
        await this.approvalConfigMappingRepo.save({
            businessType: currentConfig.businessType,
            approvalConfigId: currentConfig.id,
            isDefault: true,
        });

        // 4. Trả về config sau khi update
        return this.approvalConfigRepo.findOne({
            where: { id: currentConfig.id },
            relations: ['steps', 'steps.approvers'],
            order: {
                steps: {
                    order: 'ASC',
                },
            },
        });
    }

    /**
     * Xóa cấu hình duyệt.
     * Quan hệ đã set onDelete: 'CASCADE' nên sẽ xóa luôn step + approver.
     * Cần cân nhắc: nếu đã có ApprovalRequest dùng config này thì có cho xóa không?
     */
    @DefTransaction()
    async deleteConfig(id: string): Promise<void> {
        const config = await this.approvalConfigRepo.findOne({ where: { id } });
        if (!config) {
            throw new NotFoundException('Không tìm thấy cấu hình duyệt');
        }

        // Fix: Check config đang được sử dụng bởi request nào không
        const activeRequests = await this.approvalRequestRepo.count({
            where: { approvalConfigId: config.id },
        });

        if (activeRequests > 0) {
            throw new BusinessException(
                `Cấu hình đang được sử dụng bởi ${activeRequests} yêu cầu duyệt. Vui lòng vô hiệu hóa (isActive = false) thay vì xóa.`,
            );
        }

        await this.approvalConfigRepo.delete(id);
    }

    /**
     * Danh sách mapping cấu hình với nghiệp vụ
     */
    async getMapping(dto: GetApprovalConfigMappingDto) {
        const { businessType, approvalConfigId, pageIndex, pageSize } = dto;
        const wheres: any = {};
        if (businessType) {
            wheres.businessType = businessType;
        }
        if (approvalConfigId) {
            wheres.approvalConfigId = approvalConfigId;
        }
        const { data, total } = await this.approvalConfigMappingRepo.findPaginationByTenant(
            {
                where: wheres,
            },
            {
                pageIndex,
                pageSize,
            },
        );

        // Mapping lấy ra tên của cấu hình
        const configs = await this.approvalConfigRepo.findByTenant({});
        const mappingData = data.map(m => {
            const config = configs.find(c => c.id === m.approvalConfigId);
            return {
                ...m,
                businessTypeName: NSApproval.ApprovalBusinessTypeTitle[m.businessType],
                configName: config?.name,
            };
        });

        return {
            data: mappingData,
            total,
        };
    }

    /**
     * Tạo mapping cấu hình với nghiệp vụ
     */
    @DefTransaction()
    async createMapping(dto: CreateApprovalConfigMappingDto) {
        return this.approvalConfigMappingRepo.save(dto);
    }

    /**
     * Cập nhật mapping cấu hình với nghiệp vụ
     */
    @DefTransaction()
    async updateMapping(dto: UpdateApprovalConfigMappingDto) {
        return this.approvalConfigMappingRepo.save(dto);
    }
}
