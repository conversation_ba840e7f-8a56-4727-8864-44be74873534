import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSCustomer } from '~/common/enums';
import { CustomerDocumentRepo, MemberRepo, MediaRepo } from '~/domains/primary';
import { clientSessionContext } from '../client-session.context';
import { UploadService } from '../upload/upload.service';
import {
    CreateCustomerDocumentDto,
    ListCustomerDocumentDto,
    SetActiveCustomerDocumentDto,
    UpdateCustomerDocumentDto,
} from './dto/customer-document.dto';

@Injectable()
export class CustomerDocumentService {
    constructor(
        @InjectRepo(CustomerDocumentRepo) private customerDocumentRepo: CustomerDocumentRepo,
        @InjectRepo(MediaRepo) private mediaRepo: MediaRepo,
        private uploadService: UploadService,
        private readonly memberRepo: MemberRepo,
    ) { }

    async listCustomerDocument(params: ListCustomerDocumentDto) {
        const { tenantId } = clientSessionContext;
        const { customerId, name, pageSize, pageIndex } = params;
        const wheres: any = {};
        if (name) {
            wheres.name = ILike(`%${name}%`);
        }
        const member = await this.memberRepo.find({
            where: {
                tenantId,
            },
        });

        const res = await this.customerDocumentRepo.findPagination(
            {
                where: {
                    tenantId,
                    customerId,
                    ...wheres,
                },
                order: {
                    createdDate: 'DESC',
                },
            },
            { pageSize, pageIndex },
        );
        return {
            total: res.total,
            data: res.data.map(doc => ({
                ...doc,
                createdByName: member.find(m => m.id === doc.createdBy)?.fullName,
            })),
        };
    }

    // Create
    @DefTransaction()
    async createCustomerDocument(body: CreateCustomerDocumentDto, file: Express.Multer.File) {
        const { tenantId, memberId } = clientSessionContext;
        const { name, description, customerId, mediaId } = body.payload;

        let mediaItem: any = null;
        let uploadFile: any = null;

        // Nếu có mediaId, validate và lấy thông tin từ media table
        if (mediaId) {
            mediaItem = await this.mediaRepo.findOneByTenant({
                where: { id: mediaId, tenantId },
            });
            if (!mediaItem) {
                throw new BusinessException('Media không tồn tại hoặc không thuộc tenant này');
            }
            uploadFile = {
                fileUrl: mediaItem.url,
                fileName: mediaItem.fileName,
                contentType: mediaItem.contentType,
                mediaType: mediaItem.mediaType,
            };
        } else if (file) {
            // Backward compatibility: vẫn hỗ trợ upload file trực tiếp
            uploadFile = await this.uploadService.uploadSingle(file);
        } else {
            throw new BusinessException('Vui lòng chọn media hoặc tệp đính kèm');
        }

        // Lưu vào customer_document
        const customerDocument = this.customerDocumentRepo.create({
            tenantId,
            name,
            description,
            customerId,
            url: uploadFile.fileUrl,
            fileName: uploadFile.fileName,
            mediaId: mediaId || undefined,
            createdBy: memberId,
            status: NSCustomer.EActiveStatus.ACTIVE,
            createdDate: new Date(),
        });
        const savedDocument = await this.customerDocumentRepo.save(customerDocument);

        // Lưu vào media table nếu upload file mới và lấy mediaId
        if (!mediaId && file) {
            const mediaEntity = this.mediaRepo.create({
                tenantId,
                url: uploadFile.fileUrl,
                fileName: uploadFile.fileName,
                contentType: uploadFile.contentType,
                mediaType: uploadFile.mediaType,
                size: file.size,
                entity: 'customer_document',
                entityId: savedDocument.id,
                createdBy: memberId,
                createdDate: new Date(),
            });
            const savedMedia = await this.mediaRepo.save(mediaEntity);
            savedDocument.mediaId = savedMedia.id;
            await this.customerDocumentRepo.save(savedDocument);
        }
        return savedDocument;
    }
    // Update
    @DefTransaction()
    async updateCustomerDocument(body: UpdateCustomerDocumentDto, file: Express.Multer.File) {
        const { tenantId, memberId } = clientSessionContext;
        const { name, description, customerId, id, mediaId } = body.payload;

        let mediaItem: any = null;
        let uploadFile: any = null;

        // Nếu có mediaId, validate và lấy thông tin từ media table
        if (mediaId) {
            mediaItem = await this.mediaRepo.findOneByTenant({
                where: { id: mediaId, tenantId },
            });
            if (!mediaItem) {
                throw new BusinessException('Media không tồn tại hoặc không thuộc tenant này');
            }
            uploadFile = {
                fileUrl: mediaItem.url,
                fileName: mediaItem.fileName,
                contentType: mediaItem.contentType,
                mediaType: mediaItem.mediaType,
            };
        } else if (file) {
            // Backward compatibility: vẫn hỗ trợ upload file trực tiếp
            uploadFile = await this.uploadService.uploadSingle(file);
        } else {
            // Không có mediaId và không có file mới, giữ nguyên file cũ
            const customerDocument = await this.customerDocumentRepo.findOne({
                where: {
                    tenantId,
                    customerId,
                    id,
                },
            });
            if (!customerDocument) {
                throw new BusinessException('Customer document not found');
            }
            customerDocument.name = name;
            customerDocument.description = description;
            customerDocument.updatedBy = memberId;
            customerDocument.updatedDate = new Date();
            return await this.customerDocumentRepo.save(customerDocument);
        }

        const customerDocument = await this.customerDocumentRepo.findOne({
            where: {
                tenantId,
                customerId,
                id,
            },
        });
        if (!customerDocument) {
            throw new BusinessException('Customer document not found');
        }
        customerDocument.name = name;
        customerDocument.description = description;
        customerDocument.url = uploadFile.fileUrl;
        customerDocument.fileName = uploadFile.fileName;
        if (mediaId) {
            customerDocument.mediaId = mediaId;
        }
        customerDocument.updatedBy = memberId;
        customerDocument.updatedDate = new Date();
        const savedDocument = await this.customerDocumentRepo.save(customerDocument);

        // Cập nhật hoặc tạo mới trong media table nếu upload file mới
        if (!mediaId && file) {
            const existingMedia = await this.mediaRepo.findOne({
                where: { tenantId, entityId: savedDocument.id, entity: 'customer_document' },
            });
            if (existingMedia) {
                existingMedia.url = uploadFile.fileUrl;
                existingMedia.fileName = uploadFile.fileName;
                existingMedia.contentType = uploadFile.contentType;
                existingMedia.mediaType = uploadFile.mediaType;
                existingMedia.size = file.size;
                existingMedia.updatedBy = memberId;
                existingMedia.updatedDate = new Date();
                const savedMedia = await this.mediaRepo.save(existingMedia);
                savedDocument.mediaId = savedMedia.id;
                await this.customerDocumentRepo.save(savedDocument);
            } else {
                const mediaEntity = this.mediaRepo.create({
                    tenantId,
                    url: uploadFile.fileUrl,
                    fileName: uploadFile.fileName,
                    contentType: uploadFile.contentType,
                    mediaType: uploadFile.mediaType,
                    size: file.size,
                    entity: 'customer_document',
                    entityId: savedDocument.id,
                    createdBy: memberId,
                    createdDate: new Date(),
                });
                const savedMedia = await this.mediaRepo.save(mediaEntity);
                savedDocument.mediaId = savedMedia.id;
                await this.customerDocumentRepo.save(savedDocument);
            }
        }
        return savedDocument;
        return await this.customerDocumentRepo.update(id, {
            ...customerDocument,
            id,
        });
    }

    async setActiveCustomerDocument(body: SetActiveCustomerDocumentDto) {
        const { tenantId, memberId } = clientSessionContext;
        const { id, status, customerId } = body;
        const customerDocument = await this.customerDocumentRepo.findOne({
            where: {
                tenantId,
                customerId,
                id,
            },
        });
        if (!customerDocument) {
            throw new BusinessException('Customer document not found');
        }
        customerDocument.status = status;
        customerDocument.updatedBy = memberId;
        customerDocument.updatedDate = new Date();
        return await this.customerDocumentRepo.update(id, {
            ...customerDocument,
            id,
        });
    }
}
