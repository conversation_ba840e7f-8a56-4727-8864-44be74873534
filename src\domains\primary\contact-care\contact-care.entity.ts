import { ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSContactCare } from '~/common/enums/contact-care.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('contact_care')
export class ContactCareEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional({ example: 'TH00001' })
    @Column({ type: 'varchar', length: 32, nullable: true })
    @Index()
    code?: string | null;

    @ApiPropertyOptional({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    customerId?: string | null;

    @ApiPropertyOptional({ example: 'Thăm hỏi khách hàng định kỳ' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    title?: string | null;

    @ApiPropertyOptional({ example: '<PERSON><PERSON> tả chi tiết chuyến thăm…' })
    @Column({ type: 'text', nullable: true })
    description?: string | null;

    @ApiPropertyOptional({ description: 'Phân loại chuyến thăm' })
    @Column({ type: 'varchar', nullable: true })
    @Index()
    type?: string | null;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @Column({
        type: 'varchar',
        nullable: true,
        enum: NSContactCare.EStatus,
        default: NSContactCare.EStatus.NEW,
    })
    @Index()
    status?: NSContactCare.EStatus | null;

    @ApiPropertyOptional({ description: 'Địa điểm thăm hỏi' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    visitLocation?: string | null;

    @ApiPropertyOptional({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    addressId?: string | null;

    @ApiPropertyOptional({ example: '123 Đường ABC, Quận 1, TP.HCM' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    address?: string | null;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 6, nullable: true })
    provinceCode?: string | null;

    @ApiPropertyOptional({ example: '020000' })
    @Column({ type: 'varchar', length: 6, nullable: true })
    communeCode?: string | null;

    @ApiPropertyOptional({ description: 'Nhân viên giám sát' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    supervisorMemberId?: string | null;

    @ApiPropertyOptional({
        description: 'Nhân viên được phân công (có thể là nhiều UUID cách nhau bởi dấu phẩy)',
    })
    @Column({ type: 'varchar', array: true, length: 1000, nullable: true })
    @Index()
    assignedMemberIds?: string[] | null;

    @ApiPropertyOptional({ description: 'Số điện thoại' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    phone?: string | null;

    @ApiPropertyOptional({ description: 'Email' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    email?: string | null;

    @ApiPropertyOptional({ description: 'Liên hệ khách hàng' })
    @Column({ type: 'uuid', nullable: true })
    @Index()
    customerContactId?: string | null;

    @ApiPropertyOptional({ description: 'Ngày dự kiến' })
    @Column({ type: 'timestamptz', nullable: true })
    dueDate?: Date | null;

    @ApiPropertyOptional({ description: 'Ngày check-in' })
    @Column({ type: 'timestamptz', nullable: true })
    checkInDate?: Date | null;

    @ApiPropertyOptional({ description: 'Thời gian check-out' })
    @Column({ type: 'timestamptz', nullable: true })
    checkOutTime?: Date | null;

    @ApiPropertyOptional({ description: 'Yêu cầu check-in' })
    @Column({ type: 'boolean', default: false, nullable: true })
    requireCheckin?: boolean | null;
}
