import { Is<PERSON>rray, <PERSON>NotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class EmailConfigItemDto {
    @ApiProperty({ description: 'Email người gửi' })
    @IsString()
    @IsNotEmpty()
    email: string;

    @ApiProperty({ description: 'SMTP host' })
    @IsString()
    @IsNotEmpty()
    host: string;

    @ApiProperty({ description: 'SMTP port' })
    @IsString()
    @IsNotEmpty()
    port: string;

    @ApiProperty({ description: 'SMTP username', required: false })
    @IsString()
    @IsOptional()
    username?: string;

    @ApiProperty({ description: 'SMTP password', required: false })
    @IsString()
    @IsOptional()
    password?: string;
}

export class UpdateEmailConfigsDto {
    @ApiProperty({ description: '<PERSON>h sách cấu hình email', type: [EmailConfigItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => EmailConfigItemDto)
    configs: EmailConfigItemDto[];
}
