import { ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('campaign_customer')
export class CampaignCustomerEntity extends PrimaryBaseEntity {
    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    campaignId?: string;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    customerId?: string;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    email?: string;

    @ApiPropertyOptional({ example: '100000' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    status?: string;

    @ApiPropertyOptional({ example: '2023-01-01 00:00:00' })
    @Column({ type: 'timestamptz', nullable: true })
    sendDate?: Date;
}
