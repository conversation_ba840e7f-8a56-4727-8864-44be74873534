import { Body, Controller, UseGuards } from '@nestjs/common';
import { DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { CreateQuoteItemDto, QuoteItemDto, UpdateQuoteItemDto } from './dto';
import { QuoteItemService } from './quote-item.service';

@Controller('quote-items')
@UseGuards(PermissionGuard)
@UseGuards(ClientAuthGuard)
export class QuoteItemController {
    constructor(private readonly quoteItemService: QuoteItemService) {}

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.CREATE])
    async create(@Body() dto: CreateQuoteItemDto) {
        return this.quoteItemService.create(dto);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.UPDATE])
    async update(@Body() dto: UpdateQuoteItemDto) {
        return this.quoteItemService.update(dto);
    }

    @DefPost('delete')
    @RequirePermissions([PERMISSION_CODES.SETTING_QUOTATION.UPDATE])
    async delete(@Body() dto: QuoteItemDto) {
        return this.quoteItemService.delete(dto);
    }
}
