import { RequestContext } from '~/@core/context'
import { KEY_SESSION_CONTEXT } from '~/common/constants'
import { ClientSessionDto } from './member-auth/dto'

export class ClientSessionContext {
    get sessionData() {
        return RequestContext.getAttribute<ClientSessionDto>(KEY_SESSION_CONTEXT.CLIENT_SESSION)
    }

    get accessToken() {
        return this.sessionData?.accessToken
    }
    get memberId() {
        return this?.sessionData?.member?.id
    }

    get ssoAccountId() {
        return this?.sessionData?.member?.ssoAccountId
    }

    get tenantId() {
        return this?.sessionData?.member?.tenantId
    }

    get userName() {
        return this?.sessionData?.member?.username
    }

    get fullName() {
        return this?.sessionData?.member?.fullName
    }
}

export const clientSessionContext = new ClientSessionContext()
