# <PERSON>ệ thống Duyệt Động (Dynamic Approval System)

## Tổng quan

Hệ thống duyệt động cho phép cấu hình và thực thi các quy trình duyệt linh hoạt cho nhiều loại nghiệp vụ khác nhau (<PERSON><PERSON>, <PERSON><PERSON><PERSON> đề nghị, Đ<PERSON>n xin nghỉ, v.v.). <PERSON>ệ thống hỗ trợ cây duyệt nhiều cấp với các kiểu duyệt khác nhau (1 người, bấ<PERSON> kỳ, tất cả) và nhiều loại người duyệt (user cụ thể, role, position).

## Kiến trúc

Hệ thống được chia thành 2 phần chính:

### 1. **<PERSON><PERSON><PERSON> hình (Configuration)** - Template

Định nghĩa quy trình duyệt, có thể tái sử dụng cho nhiều request.

### 2. **Runtime (Request)** - Th<PERSON><PERSON> thi

Snapshot của cấu hình tại thời điểm tạo request, l<PERSON><PERSON> lại lịch sử và trạng thái duyệt.

---

## Cấu trúc Entity

### 📋 ApprovalConfigEntity

**Mục đích**: Cấu hình duyệt (template) cho một loại nghiệp vụ

**Các trường quan trọng**:

- `name`: Tên cấu hình duyệt
- `businessType`: Loại nghiệp vụ (PO, PURCHASE_REQUEST, ...)
- `description`: Mô tả chi tiết
- `isActive`: Bật/tắt cấu hình
- `groupKey`: Key để nhóm các config (tùy chọn)

**Quan hệ**:

- `steps`: Danh sách các bước duyệt (`ApprovalConfigStepEntity[]`)
- `requests`: Các request đang sử dụng config này (`ApprovalRequestEntity[]`)

**Ví dụ**:

```
Config: "Duyệt PO theo phòng ban"
├── Step 1 (order=1): "Trưởng phòng duyệt" (SINGLE)
├── Step 2 (order=2): "Giám đốc duyệt" (SINGLE)
└── Step 3 (order=3): "Kế toán duyệt" (ALL)
```

---

### 🔢 ApprovalConfigStepEntity

**Mục đích**: Một bước trong cấu hình duyệt

**Các trường quan trọng**:

- `order`: Thứ tự duyệt (1, 2, 3, ...)
- `title`: Tiêu đề hiển thị ("Trưởng phòng duyệt", "Giám đốc duyệt", ...)
- `approvalType`: Kiểu duyệt
  - `SINGLE`: Duyệt 1 người cố định
  - `ANY`: Có nhiều người, chỉ cần 1 người bất kỳ duyệt
  - `ALL`: Có nhiều người, tất cả đều phải duyệt
- `minApprovers`: Số lượng người tối thiểu cần duyệt (dùng cho ANY nâng cao)
- `note`: Ghi chú cho cấp duyệt

**Quan hệ**:

- `config`: Cấu hình cha (`ApprovalConfigEntity`)
- `approvers`: Danh sách người duyệt (`ApprovalConfigStepApproverEntity[]`)

**Ví dụ**:

```
Step 1: "Trưởng phòng duyệt"
├── approvalType: SINGLE
└── approvers:
    └── [MEMBER: user-123]

Step 2: "Giám đốc duyệt"
├── approvalType: ALL
└── approvers:
    ├── [MEMBER: user-456]
    ├── [MEMBER: user-789]
    └── [ROLE: director]
```

---

### 👤 ApprovalConfigStepApproverEntity

**Mục đích**: Người duyệt trong một bước của cấu hình

**Các trường quan trọng**:

- `approverType`: Kiểu đối tượng duyệt
  - `MEMBER`: User cụ thể (memberId)
  - `ROLE`: Duyệt theo role
  - `POSITION`: Duyệt theo chức vụ
  - `DEPARTMENT`: Duyệt theo phòng ban
- `memberRefId`: ID của member (khi approverType = MEMBER)
- `positionRefId`: ID của position (khi approverType = POSITION)
- `roleRefId`: ID của role (khi approverType = ROLE)
- `departmentRefId`: ID của department (khi approverType = DEPARTMENT hoặc để filter Position trong Department)
- `useCreatorDepartment`: (Boolean, default: false) Nếu `true`, `departmentRefId` sẽ được resolve từ department của người tạo request khi tạo request (chỉ hợp lệ với `approverType = POSITION`)
- `approverRefId`: (Deprecated) Giữ lại để backward compatibility

**Lưu ý**:

- Chỉ 1 trong các RefId có giá trị (tùy theo approverType), **trừ khi** `useCreatorDepartment = true` (lúc đó `departmentRefId` có thể null trong config, sẽ resolve khi tạo request)
- `departmentRefId` có thể dùng kèm với `positionRefId` để filter "Position trong Department" (ví dụ: "Giám đốc phòng IT")
- `useCreatorDepartment = true` cho phép dynamic resolve department từ người tạo request:
  - **Use case**: "Trưởng phòng của phòng ban người tạo duyệt"
  - **Yêu cầu**: `approverType = POSITION` và `positionRefId` phải được set
  - **Logic**: Khi tạo request, hệ thống sẽ lấy `memberId` từ `adminSessionContext` (người đang login), load member để lấy `departmentId` và set vào runtime approver

**Quan hệ**:

- `step`: Bước duyệt cha (`ApprovalConfigStepEntity`)
- `member`: Member entity (khi approverType = MEMBER)
- `position`: Position entity (khi approverType = POSITION)
- `department`: Department entity (khi approverType = DEPARTMENT)

**Ví dụ**:

```
Approver 1:
├── approverType: MEMBER
└── memberRefId: "550e8400-e29b-41d4-a716-************"

Approver 2:
├── approverType: ROLE
└── roleRefId: "director-role-id"

Approver 3:
├── approverType: POSITION
├── positionRefId: "director-position-id"
└── departmentRefId: null (tất cả giám đốc ở mọi phòng ban)

Approver 4:
├── approverType: POSITION
├── positionRefId: "director-position-id"
└── departmentRefId: "it-department-id" (chỉ giám đốc phòng IT)

Approver 5:
├── approverType: DEPARTMENT
└── departmentRefId: "it-department-id" (tất cả nhân viên phòng IT)

Approver 6 (Dynamic):
├── approverType: POSITION
├── positionRefId: "trưởng-phòng-position-id"
├── useCreatorDepartment: true
└── departmentRefId: null (sẽ resolve từ creator's department khi tạo request)
→ Ví dụ: PR của phòng IT tạo → trưởng phòng IT duyệt
→ Ví dụ: PR của phòng nhân sự tạo → trưởng phòng nhân sự duyệt
```

---

### 🔗 ApprovalConfigMappingEntity

**Mục đích**: Mapping giữa loại nghiệp vụ và cấu hình duyệt

**Các trường quan trọng**:

- `businessType`: Loại nghiệp vụ
- `approvalConfigId`: ID của cấu hình duyệt
- `isDefault`: Đánh dấu cấu hình mặc định cho loại nghiệp vụ này

**Quan hệ**:

- `approvalConfig`: Cấu hình duyệt (`ApprovalConfigEntity`)

**Ví dụ**:

```
Mapping:
├── businessType: PURCHASE_REQUEST
├── approvalConfigId: "config-123"
└── isDefault: true
```

---

### 📝 ApprovalRequestEntity

**Mục đích**: Request duyệt thực tế (runtime) cho một chứng từ cụ thể

**Các trường quan trọng**:

- `businessType`: Loại nghiệp vụ
- `documentId`: ID của chứng từ (PO.id, RequestForm.id, ...)
- `documentCode`: Mã chứng từ (PO.code, RequestForm.code, ...)
- `approvalConfigId`: Cấu hình duyệt đang áp dụng
- `status`: Trạng thái tổng (PENDING, IN_PROGRESS, APPROVED, REJECTED, CANCELLED)
- `finalStatuses`: Danh sách trạng thái kết thúc (không thể approve/reject nếu status nằm trong danh sách này)
- `approvedStatus`: Trạng thái khi approve hoàn toàn (ví dụ: IN_PROGRESS, PROCESSING)
- `currentStepOrder`: Đang ở step thứ mấy (order trong config)
- `currentStepId`: ConfigStep hiện tại (dùng join nhanh)

**Quan hệ**:

- `approvalConfig`: Cấu hình duyệt (`ApprovalConfigEntity`)
- `currentStep`: Step hiện tại (`ApprovalConfigStepEntity`)
- `steps`: Danh sách các bước trong request (`ApprovalRequestStepEntity[]`)
- `actions`: Lịch sử hành động (`ApprovalRequestActionEntity[]`)

**Ví dụ**:

```
Request:
├── documentId: "po-123"
├── documentCode: "PO-2024-001"
├── status: "IN_PROGRESS"
├── currentStepOrder: 2
└── steps: [Step 1 (APPROVED), Step 2 (IN_PROGRESS), Step 3 (PENDING)]
```

---

### 🔄 ApprovalRequestStepEntity

**Mục đích**: Bước duyệt trong request (snapshot từ config step)

**Các trường quan trọng**:

- `configStepId`: Tham chiếu config step gốc (để trace, không phụ thuộc để chạy)
- `order`: Thứ tự cấp duyệt (snapshot từ configStep.order)
- `approvalType`: Kiểu duyệt (ALL / ANY / SINGLE)
- `minApprovers`: Số người cần duyệt tối thiểu
- `status`: Trạng thái step
  - `PENDING`: Chưa đến lượt / chưa kích hoạt
  - `IN_PROGRESS`: Đang chờ duyệt
  - `APPROVED`: Đã duyệt
  - `REJECTED`: Bị từ chối
  - `SKIPPED`: Bỏ qua (ví dụ do điều kiện)
- `startedAt`: Thời điểm bắt đầu step
- `finishedAt`: Thời điểm kết thúc step

**Quan hệ**:

- `request`: Request cha (`ApprovalRequestEntity`)
- `configStep`: Config step gốc (`ApprovalConfigStepEntity`) - để trace
- `approvers`: Danh sách người duyệt (`ApprovalRequestStepApproverEntity[]`)

**Lưu ý**: Entity này là snapshot, nên nếu config step bị xóa/sửa, request vẫn giữ nguyên dữ liệu.

---

### 👥 ApprovalRequestStepApproverEntity

**Mục đích**: Người duyệt trong request step (runtime) - Lưu nguyên config, không expand

**Các trường quan trọng**:

- `configStepApproverId`: Tham chiếu config approver gốc (để trace)
- `approverType`: Kiểu đối tượng duyệt (MEMBER / ROLE / POSITION / DEPARTMENT) - **Lưu nguyên từ config**
- `memberRefId`: ID của member (khi approverType = MEMBER) - **Lưu nguyên từ config**
- `positionRefId`: ID của position (khi approverType = POSITION) - **Lưu nguyên từ config**
- `roleRefId`: ID của role (khi approverType = ROLE) - **Lưu nguyên từ config**
- `departmentRefId`: ID của department (khi approverType = DEPARTMENT hoặc để filter Position) - **Lưu nguyên từ config**
- `approverRefId`: (Deprecated) Giữ lại để backward compatibility
- `status`: Trạng thái duyệt của từng người
  - `PENDING`: Chưa duyệt
  - `IN_PROGRESS`: Đang xử lý
  - `APPROVED`: Đã duyệt
  - `REJECTED`: Đã từ chối
  - `SKIPPED`: Bỏ qua
- `actedAt`: Thời điểm thực hiện hành động
- `comment`: Ghi chú của người duyệt

**Quan hệ**:

- `step`: Request step cha (`ApprovalRequestStepEntity`)
- `configStepApprover`: Config approver gốc (`ApprovalConfigStepApproverEntity`) - để trace

**Lưu ý quan trọng**:

- **Runtime table lưu nguyên config** (không expand thành danh sách members cụ thể)
- **Check real-time**: Khi check quyền duyệt, hệ thống sẽ check real-time từ runtime table dựa trên vị trí/phòng ban/role hiện tại của member
- **Snapshot**: Config được snapshot tại thời điểm tạo request, nên nếu config thay đổi sau đó, request vẫn giữ nguyên cấu hình ban đầu
- **Linh hoạt**: Nếu member thay đổi vị trí/phòng ban sau khi tạo request, vẫn có thể duyệt nếu đáp ứng điều kiện real-time

**Ví dụ**:

```
Approver 1 (MEMBER):
├── approverType: MEMBER
├── memberRefId: "user-123"
├── status: APPROVED
├── actedAt: "2024-01-15 10:30:00"
└── comment: "Đồng ý"

Approver 2 (POSITION):
├── approverType: POSITION
├── positionRefId: "director-position-id"
├── departmentRefId: "it-department-id" (chỉ giám đốc phòng IT)
├── status: PENDING
└── actedAt: null
→ Check real-time: Member nào có position = director và department = IT sẽ có quyền duyệt

Approver 3 (DEPARTMENT):
├── approverType: DEPARTMENT
├── departmentRefId: "it-department-id"
├── status: PENDING
└── actedAt: null
→ Check real-time: Tất cả members trong phòng IT sẽ có quyền duyệt
```

---

### 📊 ApprovalRequestActionEntity

**Mục đích**: Lịch sử hành động trên request

**Các trường quan trọng**:

- `requestId`: ID của request
- `stepId`: ID của request step
- `actorId`: Người thực hiện (memberId)
- `action`: Hành động (APPROVE, REJECT, ...)
- `comment`: Ghi chú

**Quan hệ**:

- `request`: Request (`ApprovalRequestEntity`)
- `step`: Request step (`ApprovalRequestStepEntity`)

**Ví dụ**:

```
Action:
├── actorId: "user-123"
├── action: APPROVE
├── stepId: "step-456"
└── comment: "Đồng ý duyệt"
```

---

### 📸 ApprovalRequestStepApproverSnapshotEntity

**Mục đích**: Snapshot danh sách approvers cụ thể khi tạo request (dùng cho audit trail và history tracking)

**Các trường quan trọng**:

- `requestId`: ID của request
- `stepId`: ID của request step
- `configStepApproverId`: Reference đến config approver gốc
- `memberId`: Member ID cụ thể lúc snapshot (đã được expand từ config)
- `snapshotDate`: Thời điểm tạo snapshot
- `memberFullName`: Tên đầy đủ của member lúc snapshot
- `memberPositionId`: Position ID của member lúc snapshot
- `memberPositionName`: Tên position của member lúc snapshot
- `memberDepartmentId`: Department ID của member lúc snapshot
- `memberDepartmentName`: Tên department của member lúc snapshot
- `approverType`: Config gốc (MEMBER / ROLE / POSITION / DEPARTMENT)
- `positionRefId`: Position ID từ config (nếu có)
- `departmentRefId`: Department ID từ config (nếu có)
- `roleRefId`: Role ID từ config (nếu có)

**Quan hệ**:

- `request`: Request (`ApprovalRequestEntity`)
- `step`: Request step (`ApprovalRequestStepEntity`)

**Lưu ý**:

- Snapshot được tạo khi tạo request, lưu lại danh sách members cụ thể lúc đó
- Dùng cho audit trail: biết được ai là approver lúc tạo request
- Không dùng cho check quyền duyệt (check quyền dùng runtime table với real-time check)

**Ví dụ**:

```
Snapshot 1:
├── memberId: "user-123"
├── memberFullName: "Nguyễn Văn A"
├── memberPositionName: "Giám đốc"
├── memberDepartmentName: "Phòng IT"
├── snapshotDate: "2024-01-15 09:00:00"
├── approverType: POSITION
├── positionRefId: "director-position-id"
└── departmentRefId: "it-department-id"
→ Lưu lại: Lúc tạo request, user-123 là giám đốc phòng IT
```

---

## Luồng hoạt động

### 1. **Tạo cấu hình duyệt**

```
1. Tạo ApprovalConfigEntity
   └── businessType: PURCHASE_REQUEST
   └── name: "Duyệt PO theo phòng ban"

2. Tạo ApprovalConfigStepEntity (nhiều step)
   ├── Step 1: order=1, approvalType=SINGLE
   ├── Step 2: order=2, approvalType=ALL
   └── Step 3: order=3, approvalType=ANY

3. Tạo ApprovalConfigStepApproverEntity cho mỗi step
   └── Step 1: [MEMBER: user-123]
   └── Step 2: [MEMBER: user-456, MEMBER: user-789]
   └── Step 3: [ROLE: director]
   └── Step 4: [POSITION: trưởng-phòng, useCreatorDepartment: true] (dynamic)
   └── Step 4: [POSITION: trưởng-phòng, useCreatorDepartment: true] (dynamic)

4. Tạo ApprovalConfigMappingEntity
   └── businessType: PURCHASE_REQUEST
   └── approvalConfigId: config-id
   └── isDefault: true
```

### 2. **Tạo request duyệt**

```
1. Tạo ApprovalRequestEntity
   └── documentId: "po-123"
   └── approvalConfigId: config-id
   └── status: PENDING
   └── currentStepOrder: 1

2. Tạo ApprovalRequestStepEntity cho mỗi step trong config (snapshot)
   ├── Step 1: order=1, status=PENDING
   ├── Step 2: order=2, status=PENDING
   └── Step 3: order=3, status=PENDING

3. Tạo ApprovalRequestStepApproverEntity cho mỗi approver trong mỗi step (lưu nguyên config, resolve department nếu useCreatorDepartment = true)
   └── Step 1: [MEMBER: user-123, status: PENDING]
   └── Step 2: [MEMBER: user-456, status: PENDING], [MEMBER: user-789, status: PENDING]
   └── Step 3: [ROLE: director, status: PENDING] → Check real-time khi duyệt
   └── Step 4: [POSITION: trưởng-phòng, departmentRefId: "it-department-id" (resolved từ creator), status: PENDING] → Check real-time khi duyệt

4. Tạo ApprovalRequestStepApproverSnapshotEntity (snapshot danh sách members cụ thể)
   └── Step 1: [user-123 snapshot với thông tin lúc tạo request]
   └── Step 2: [user-456 snapshot, user-789 snapshot]
   └── Step 3: [Tất cả members có role director lúc tạo request]
```

### 3. **Xử lý duyệt**

```
1. Check quyền duyệt (real-time check từ runtime table)
   └── Load member với position/department/roles hiện tại
   └── Check từ ApprovalRequestStepApproverEntity (runtime table):
       ├── MEMBER: check memberRefId === actorId
       ├── POSITION: check position hiện tại === positionRefId (và departmentRefId nếu có)
       ├── DEPARTMENT: check department hiện tại === departmentRefId
       └── ROLE: check roles hiện tại có chứa roleRefId

2. User duyệt (approve/reject)
   └── Tìm ApprovalRequestStepApproverEntity khớp với actor (real-time check)
   └── Cập nhật ApprovalRequestStepApproverEntity
       ├── status: APPROVED/REJECTED
       ├── actedAt: now()
       └── comment: "..."

3. Tạo ApprovalRequestActionEntity (lịch sử)
   └── action: APPROVE/REJECT
   └── actorId: user-id

4. Kiểm tra điều kiện chuyển step
   ├── Nếu step hiện tại đã đủ điều kiện (theo approvalType)
   │   └── Cập nhật ApprovalRequestStepEntity
   │       ├── status: APPROVED
   │       └── finishedAt: now()
   │
   └── Nếu step bị reject
       └── Cập nhật ApprovalRequestEntity
           └── status: REJECTED

5. Chuyển sang step tiếp theo (nếu có)
   └── Cập nhật ApprovalRequestEntity
       ├── currentStepOrder: next
       └── currentStepId: next-step-id
   └── Cập nhật ApprovalRequestStepEntity
       ├── status: IN_PROGRESS
       └── startedAt: now()
```

### 4. **Kết thúc quy trình**

```
Khi step cuối cùng được approve:
└── Cập nhật ApprovalRequestEntity
    ├── status: APPROVED
    └── currentStepOrder: null (hoặc step cuối)
```

---

## Các kiểu duyệt (ApprovalLevelType)

### SINGLE

- **Mô tả**: Duyệt 1 người cố định
- **Điều kiện hoàn thành**: Người duyệt duy nhất approve
- **Ví dụ**: "Trưởng phòng duyệt" → chỉ có 1 trưởng phòng

### ANY

- **Mô tả**: Có nhiều người, chỉ cần 1 người bất kỳ duyệt
- **Điều kiện hoàn thành**: Ít nhất 1 người approve
- **Ví dụ**: "Giám đốc duyệt" → có 2 giám đốc, chỉ cần 1 người duyệt

### ALL

- **Mô tả**: Có nhiều người, tất cả đều phải duyệt
- **Điều kiện hoàn thành**: Tất cả người duyệt đều approve
- **Ví dụ**: "Kế toán duyệt" → có 3 kế toán, tất cả đều phải duyệt

---

## Các kiểu người duyệt (ApproverType)

### MEMBER

- **Mô tả**: User cụ thể (memberId)
- **memberRefId**: UUID của member
- **Check quyền**: Trực tiếp so sánh `memberRefId === actorId`
- **Ví dụ**: `memberRefId: "550e8400-e29b-41d4-a716-************"` → chỉ member này có thể duyệt

### ROLE

- **Mô tả**: Duyệt theo role
- **roleRefId**: ID của role
- **Check quyền**: Check real-time xem actor có role này không
- **Ví dụ**: `roleRefId: "director-role-id"` → tất cả user có role "director" đều có thể duyệt (check real-time)

### POSITION

- **Mô tả**: Duyệt theo chức vụ
- **positionRefId**: ID của position
- **departmentRefId**: (Optional) ID của department để filter "Position trong Department"
- **Check quyền**: Check real-time xem actor có position này không (và department nếu có departmentRefId)
- **Ví dụ 1**: `positionRefId: "director-position-id"`, `departmentRefId: null` → tất cả giám đốc ở mọi phòng ban
- **Ví dụ 2**: `positionRefId: "director-position-id"`, `departmentRefId: "it-department-id"` → chỉ giám đốc phòng IT

### DEPARTMENT

- **Mô tả**: Duyệt theo phòng ban
- **departmentRefId**: ID của department
- **Check quyền**: Check real-time xem actor có thuộc department này không
- **Ví dụ**: `departmentRefId: "it-department-id"` → tất cả nhân viên phòng IT đều có thể duyệt (check real-time)

---

## Trạng thái (Status)

### ApprovalRequestEntity.status

- `PENDING`: Mới tạo, chưa ai xử lý
- `IN_PROGRESS`: Đang duyệt
- `APPROVED`: Đã duyệt hoàn toàn
- `REJECTED`: Bị từ chối
- `CANCELLED`: Hủy bởi người tạo / hệ thống

### ApprovalRequestStepEntity.status

- `PENDING`: Chưa đến lượt / chưa kích hoạt
- `IN_PROGRESS`: Đang chờ duyệt
- `APPROVED`: Đã duyệt
- `REJECTED`: Bị từ chối
- `SKIPPED`: Bỏ qua (ví dụ do điều kiện)

### ApprovalRequestStepApproverEntity.status

- `PENDING`: Chưa duyệt
- `IN_PROGRESS`: Đang xử lý
- `APPROVED`: Đã duyệt
- `REJECTED`: Đã từ chối
- `SKIPPED`: Bỏ qua

---

## Lưu ý quan trọng

### 1. **Snapshot Pattern**

- `ApprovalRequestStepEntity` và `ApprovalRequestStepApproverEntity` là snapshot của config
- Nếu config bị xóa/sửa, request vẫn giữ nguyên dữ liệu
- Đảm bảo tính toàn vẹn lịch sử duyệt

### 1.1. **Runtime Table lưu nguyên Config**

- `ApprovalRequestStepApproverEntity` **lưu nguyên config** (không expand thành danh sách members)
- Khi tạo request: Copy nguyên `approverType`, `memberRefId`, `positionRefId`, `roleRefId`, `departmentRefId` từ config
- **Check quyền real-time**: Khi check quyền duyệt, hệ thống check real-time dựa trên vị trí/phòng ban/role hiện tại của member
- **Lợi ích**:
  - Nếu member thay đổi vị trí/phòng ban sau khi tạo request, vẫn có thể duyệt nếu đáp ứng điều kiện
  - Không cần update runtime table khi có thay đổi nhân sự
  - Logic đơn giản hơn (không cần expand)

### 1.2. **Snapshot Table cho History**

- `ApprovalRequestStepApproverSnapshotEntity` lưu snapshot danh sách members cụ thể khi tạo request
- Dùng cho audit trail: biết được ai là approver lúc tạo request
- Không dùng cho check quyền duyệt (check quyền dùng runtime table với real-time check)

### 2. **Cascade Delete**

- Xóa `ApprovalConfigEntity` → xóa tất cả `ApprovalConfigStepEntity` và `ApprovalRequestEntity`
- Xóa `ApprovalRequestEntity` → xóa tất cả `ApprovalRequestStepEntity` và `ApprovalRequestStepApproverEntity`
- Xóa `ApprovalRequestStepEntity` → xóa tất cả `ApprovalRequestStepApproverEntity`

### 3. **Index và Performance**

- `ApprovalConfigStepEntity`: Index unique trên `(configId, order)`
- `ApprovalRequestStepEntity`: Index unique trên `(requestId, order)`
- `ApprovalRequestEntity`: Index trên `(businessType, documentId)`
- `ApprovalRequestStepApproverEntity`:
  - Index trên `(stepId, approverType, status)` - cho query list requests
  - Index trên `(stepId, memberRefId, status)` - cho check MEMBER
  - Index trên `(stepId, positionRefId, departmentRefId, status)` - cho check POSITION + DEPARTMENT
  - Index trên `(stepId, departmentRefId, status)` - cho check DEPARTMENT
  - Index trên `(stepId, roleRefId, status)` - cho check ROLE
- `ApprovalRequestStepApproverSnapshotEntity`:
  - Index trên `(requestId, stepId)` - cho query history theo request
  - Index trên `(requestId, snapshotDate)` - cho query history theo thời gian
  - Index trên `(memberId)` - cho query history theo member

### 4. **Final Statuses**

- `finalStatuses` trong `ApprovalRequestEntity` định nghĩa các trạng thái kết thúc
- Khi request ở trạng thái kết thúc, không thể approve/reject nữa
- Mặc định: `["APPROVED", "REJECTED", "CANCELLED"]`

### 5. **Group Key**

- `groupKey` trong `ApprovalConfigEntity` dùng để nhóm các config
- Có thể dùng để áp dụng config khác nhau cho các nhóm khác nhau

---

## Ví dụ thực tế

### Scenario: Duyệt PO

**Cấu hình**:

```
Config: "Duyệt PO theo phòng ban"
├── Step 1: "Trưởng phòng duyệt" (SINGLE)
│   └── Approver: [MEMBER: user-123]
├── Step 2: "Giám đốc duyệt" (ALL)
│   └── Approvers: [MEMBER: user-456, MEMBER: user-789]
└── Step 3: "Kế toán duyệt" (ANY)
    └── Approvers: [ROLE: accountant]
```

**Request**:

```
Request: PO-2024-001
├── Step 1: APPROVED (user-123 đã duyệt)
│   └── Runtime Approver: [MEMBER: user-123, status: APPROVED]
├── Step 2: IN_PROGRESS (user-456 đã duyệt, user-789 chưa)
│   └── Runtime Approvers:
│       ├── [MEMBER: user-456, status: APPROVED]
│       └── [MEMBER: user-789, status: PENDING]
└── Step 3: PENDING (chưa đến lượt)
    └── Runtime Approver: [ROLE: accountant, status: PENDING]
        → Check real-time: Members nào có role accountant sẽ có quyền duyệt
```

**Khi user-789 duyệt Step 2**:

```
Request: PO-2024-001
├── Step 1: APPROVED
├── Step 2: APPROVED (cả 2 đã duyệt)
│   └── Runtime Approvers:
│       ├── [MEMBER: user-456, status: APPROVED]
│       └── [MEMBER: user-789, status: APPROVED]
└── Step 3: IN_PROGRESS (chuyển sang step 3)
    └── Runtime Approver: [ROLE: accountant, status: PENDING]
        → Bất kỳ member nào có role accountant (check real-time) có thể duyệt
```

**Ví dụ với POSITION + DEPARTMENT**:

```
Config Step: "Giám đốc phòng IT duyệt"
└── Runtime Approver:
    ├── approverType: POSITION
    ├── positionRefId: "director-position-id"
    └── departmentRefId: "it-department-id"

Khi tạo request:
└── Snapshot: [user-A (giám đốc IT lúc đó), user-B (giám đốc IT lúc đó)]

Sau đó user-A chuyển sang phòng khác, user-C lên làm giám đốc IT:
└── Check real-time: user-C có thể duyệt (vì có position = director và department = IT)
└── user-A không thể duyệt nữa (vì đã chuyển phòng)
```

---

## Các Case Có Thể Xảy Ra

Dưới đây là danh sách các case có thể xảy ra trong hệ thống duyệt, được phân loại theo các khía cạnh khác nhau:

### 1. Case theo ApprovalLevelType (Kiểu duyệt)

#### 1.1. SINGLE - Duyệt 1 người cố định

**Case 1.1.1: SINGLE với MEMBER**

- **Cấu hình**: `approvalType: SINGLE`, `approverType: MEMBER`, `memberRefId: "user-123"`
- **Hành vi**: Chỉ user-123 có thể duyệt
- **Kết quả**: Step hoàn thành khi user-123 approve
- **Edge case**: Nếu user-123 bị xóa/deactivate → Request bị stuck (cần xử lý riêng)

**Case 1.1.2: SINGLE với POSITION (không có departmentRefId)**

- **Cấu hình**: `approvalType: SINGLE`, `approverType: POSITION`, `positionRefId: "director-id"`
- **Hành vi**: Bất kỳ ai có position = director đều có thể duyệt (check real-time)
- **Kết quả**: Step hoàn thành khi 1 người có position = director approve
- **Edge case**: Nếu có nhiều người cùng position, ai approve trước sẽ hoàn thành step

**Case 1.1.3: SINGLE với POSITION + DEPARTMENT**

- **Cấu hình**: `approvalType: SINGLE`, `approverType: POSITION`, `positionRefId: "director-id"`, `departmentRefId: "it-department-id"`
- **Hành vi**: Chỉ giám đốc phòng IT có thể duyệt (check real-time)
- **Kết quả**: Step hoàn thành khi 1 giám đốc IT approve
- **Edge case**: Nếu giám đốc IT chuyển phòng sau khi tạo request, người mới lên làm giám đốc IT vẫn có thể duyệt

**Case 1.1.4: SINGLE với ROLE**

- **Cấu hình**: `approvalType: SINGLE`, `approverType: ROLE`, `roleRefId: "director-role-id"`
- **Hành vi**: Bất kỳ ai có role = director đều có thể duyệt (check real-time)
- **Kết quả**: Step hoàn thành khi 1 người có role = director approve
- **Edge case**: Nếu member có nhiều roles, chỉ cần có role này là đủ

**Case 1.1.5: SINGLE với DEPARTMENT**

- **Cấu hình**: `approvalType: SINGLE`, `approverType: DEPARTMENT`, `departmentRefId: "it-department-id"`
- **Hành vi**: Bất kỳ ai trong phòng IT đều có thể duyệt (check real-time)
- **Kết quả**: Step hoàn thành khi 1 người trong phòng IT approve
- **Edge case**: Nếu member chuyển phòng, không thể duyệt nữa; member mới vào phòng có thể duyệt

#### 1.2. ANY - Có nhiều người, chỉ cần 1 người bất kỳ duyệt

**Case 1.2.1: ANY với nhiều MEMBER**

- **Cấu hình**: `approvalType: ANY`, `approvers: [MEMBER: user-123, MEMBER: user-456, MEMBER: user-789]`
- **Hành vi**: Chỉ cần 1 trong 3 người approve
- **Kết quả**: Step hoàn thành khi bất kỳ 1 người approve
- **Edge case**: Nếu tất cả đều reject → Step bị reject

**Case 1.2.2: ANY với nhiều POSITION**

- **Cấu hình**: `approvalType: ANY`, `approvers: [POSITION: director, POSITION: manager]`
- **Hành vi**: Bất kỳ ai có position = director HOẶC manager đều có thể duyệt
- **Kết quả**: Step hoàn thành khi 1 người có 1 trong 2 positions approve
- **Edge case**: Nếu member có cả 2 positions, vẫn chỉ cần approve 1 lần

**Case 1.2.3: ANY với minApprovers = 2**

- **Cấu hình**: `approvalType: ANY`, `minApprovers: 2`, `approvers: [MEMBER: user-123, MEMBER: user-456, MEMBER: user-789]`
- **Hành vi**: Cần ít nhất 2 người approve
- **Kết quả**: Step hoàn thành khi có ít nhất 2 người approve
- **Edge case**: Nếu chỉ có 1 người approve → Step vẫn PENDING

**Case 1.2.4: ANY với ROLE**

- **Cấu hình**: `approvalType: ANY`, `approverType: ROLE`, `roleRefId: "director-role-id"`
- **Hành vi**: Bất kỳ ai có role = director đều có thể duyệt
- **Kết quả**: Step hoàn thành khi 1 người có role = director approve
- **Edge case**: Nếu có 10 người có role này, chỉ cần 1 người approve là đủ

**Case 1.2.5: ANY với DEPARTMENT**

- **Cấu hình**: `approvalType: ANY`, `approverType: DEPARTMENT`, `departmentRefId: "it-department-id"`
- **Hành vi**: Bất kỳ ai trong phòng IT đều có thể duyệt
- **Kết quả**: Step hoàn thành khi 1 người trong phòng IT approve
- **Edge case**: Nếu phòng IT có 50 người, chỉ cần 1 người approve

**Case 1.2.6: ANY với hỗn hợp (MEMBER + ROLE + POSITION)**

- **Cấu hình**: `approvalType: ANY`, `approvers: [MEMBER: user-123, ROLE: director, POSITION: manager]`
- **Hành vi**: user-123 HOẶC ai có role = director HOẶC ai có position = manager đều có thể duyệt
- **Kết quả**: Step hoàn thành khi 1 trong các điều kiện trên approve
- **Edge case**: Nếu user-123 cũng có role = director, vẫn chỉ cần approve 1 lần

#### 1.3. ALL - Có nhiều người, tất cả đều phải duyệt

**Case 1.3.1: ALL với nhiều MEMBER**

- **Cấu hình**: `approvalType: ALL`, `approvers: [MEMBER: user-123, MEMBER: user-456, MEMBER: user-789]`
- **Hành vi**: Cả 3 người đều phải approve
- **Kết quả**: Step hoàn thành khi tất cả 3 người approve
- **Edge case**: Nếu 1 người reject → Step bị reject ngay lập tức

**Case 1.3.2: ALL với ROLE**

- **Cấu hình**: `approvalType: ALL`, `approverType: ROLE`, `roleRefId: "director-role-id"`
- **Hành vi**: Tất cả người có role = director đều phải approve
- **Kết quả**: Step hoàn thành khi tất cả người có role này approve
- **Edge case**: Nếu có người mới được gán role này sau khi tạo request → Người đó cũng phải approve (check real-time)

**Case 1.3.3: ALL với POSITION + DEPARTMENT**

- **Cấu hình**: `approvalType: ALL`, `approverType: POSITION`, `positionRefId: "director-id"`, `departmentRefId: "it-department-id"`
- **Hành vi**: Tất cả giám đốc phòng IT đều phải approve
- **Kết quả**: Step hoàn thành khi tất cả giám đốc IT approve
- **Edge case**: Nếu có giám đốc mới lên sau khi tạo request → Người đó cũng phải approve

**Case 1.3.4: ALL với DEPARTMENT**

- **Cấu hình**: `approvalType: ALL`, `approverType: DEPARTMENT`, `departmentRefId: "it-department-id"`
- **Hành vi**: Tất cả người trong phòng IT đều phải approve
- **Kết quả**: Step hoàn thành khi tất cả người trong phòng IT approve
- **Edge case**: Nếu có người mới vào phòng sau khi tạo request → Người đó cũng phải approve (có thể không hợp lý nếu phòng lớn)

**Case 1.3.5: ALL với hỗn hợp**

- **Cấu hình**: `approvalType: ALL`, `approvers: [MEMBER: user-123, ROLE: director, POSITION: manager]`
- **Hành vi**: user-123 VÀ tất cả người có role = director VÀ tất cả người có position = manager đều phải approve
- **Kết quả**: Step hoàn thành khi tất cả điều kiện trên approve
- **Edge case**: Logic phức tạp, có thể có overlap (user-123 cũng có role = director)

### 2. Case theo ApproverType (Kiểu người duyệt)

#### 2.1. MEMBER - User cụ thể

**Case 2.1.1: MEMBER đơn giản**

- **Cấu hình**: `approverType: MEMBER`, `memberRefId: "user-123"`
- **Check quyền**: `memberRefId === actorId`
- **Hành vi**: Chỉ user-123 có thể duyệt
- **Edge case**:
  - User bị xóa → Request stuck
  - User bị deactivate → Vẫn có thể duyệt (nếu không check status)

**Case 2.1.2: MEMBER với SINGLE**

- Xem Case 1.1.1

**Case 2.1.3: MEMBER với ANY**

- Xem Case 1.2.1

**Case 2.1.4: MEMBER với ALL**

- Xem Case 1.3.1

#### 2.2. ROLE - Duyệt theo role

**Case 2.2.1: ROLE đơn giản**

- **Cấu hình**: `approverType: ROLE`, `roleRefId: "director-role-id"`
- **Check quyền**: `actor.roles.some(role => role.id === roleRefId)` (check real-time)
- **Hành vi**: Tất cả người có role này đều có thể duyệt
- **Edge case**:
  - Member có nhiều roles → Chỉ cần có role này là đủ
  - Member được gán role sau khi tạo request → Có thể duyệt
  - Member bị gỡ role sau khi tạo request → Không thể duyệt nữa

**Case 2.2.2: ROLE với SINGLE**

- Xem Case 1.1.4

**Case 2.2.3: ROLE với ANY**

- Xem Case 1.2.4

**Case 2.2.4: ROLE với ALL**

- Xem Case 1.3.2

#### 2.3. POSITION - Duyệt theo chức vụ

**Case 2.3.1: POSITION không có departmentRefId**

- **Cấu hình**: `approverType: POSITION`, `positionRefId: "director-id"`, `departmentRefId: null`
- **Check quyền**: `actor.position.id === positionRefId` (check real-time)
- **Hành vi**: Tất cả người có position = director ở mọi phòng ban đều có thể duyệt
- **Edge case**:
  - Member thay đổi position → Không thể duyệt nữa
  - Member mới lên position → Có thể duyệt

**Case 2.3.2: POSITION có departmentRefId (Position trong Department)**

- **Cấu hình**: `approverType: POSITION`, `positionRefId: "director-id"`, `departmentRefId: "it-department-id"`, `useCreatorDepartment: false`
- **Check quyền**: `actor.position.id === positionRefId && actor.department.id === departmentRefId` (check real-time)
- **Hành vi**: Chỉ giám đốc phòng IT có thể duyệt
- **Edge case**:
  - Giám đốc IT chuyển phòng → Không thể duyệt nữa
  - Người mới lên làm giám đốc IT → Có thể duyệt
  - Giám đốc IT chuyển sang position khác → Không thể duyệt nữa

**Case 2.3.3: POSITION với useCreatorDepartment = true (Dynamic)**

- **Cấu hình**: `approverType: POSITION`, `positionRefId: "trưởng-phòng-id"`, `departmentRefId: null`, `useCreatorDepartment: true`
- **Logic khi tạo request**:
  1. Lấy `memberId` từ `adminSessionContext` (người đang tạo request)
  2. Load member để lấy `departmentId`
  3. Set `departmentRefId = creator.departmentId` vào runtime approver
- **Check quyền**: `actor.position.id === positionRefId && actor.department.id === resolvedDepartmentId` (check real-time)
- **Hành vi**: Trưởng phòng của phòng ban người tạo có thể duyệt
- **Ví dụ thực tế**:
  - PR của phòng IT tạo → Trưởng phòng IT duyệt
  - PR của phòng nhân sự tạo → Trưởng phòng nhân sự duyệt
- **Edge case**:
  - Creator không có department → `departmentRefId` = null, không ai có thể duyệt (log warning)
  - Creator chuyển phòng sau khi tạo request → Không ảnh hưởng (đã resolve khi tạo request)
  - Trưởng phòng chuyển phòng sau khi tạo request → Không thể duyệt nữa (check real-time)

**Case 2.3.4: POSITION với SINGLE**

- Xem Case 1.1.2, 1.1.3

**Case 2.3.4: POSITION với ANY**

- Xem Case 1.2.2

**Case 2.3.5: POSITION với ALL**

- Xem Case 1.3.3

#### 2.4. DEPARTMENT - Duyệt theo phòng ban

**Case 2.4.1: DEPARTMENT đơn giản**

- **Cấu hình**: `approverType: DEPARTMENT`, `departmentRefId: "it-department-id"`
- **Check quyền**: `actor.department.id === departmentRefId` (check real-time)
- **Hành vi**: Tất cả người trong phòng IT đều có thể duyệt
- **Edge case**:
  - Member chuyển phòng → Không thể duyệt nữa
  - Member mới vào phòng → Có thể duyệt
  - Phòng ban bị xóa → Request stuck (cần xử lý riêng)

**Case 2.4.2: DEPARTMENT với SINGLE**

- Xem Case 1.1.5

**Case 2.4.3: DEPARTMENT với ANY**

- Xem Case 1.2.5

**Case 2.4.4: DEPARTMENT với ALL**

- Xem Case 1.3.4

### 3. Case theo Real-time Check (Check quyền động)

#### 3.1. Member thay đổi position/department sau khi tạo request

**Case 3.1.1: Member được thăng chức**

- **Tình huống**: Request tạo với `POSITION: manager`, sau đó member được thăng lên `director`
- **Hành vi**: Member không thể duyệt nữa (vì không còn position = manager)
- **Kết quả**: Request vẫn chờ người khác có position = manager duyệt

**Case 3.1.2: Member được gán position mới**

- **Tình huống**: Request tạo với `POSITION: director`, sau đó member được gán position = director
- **Hành vi**: Member có thể duyệt (check real-time)
- **Kết quả**: Member mới có thể duyệt request cũ

**Case 3.1.3: Member chuyển phòng**

- **Tình huống**: Request tạo với `POSITION: director + DEPARTMENT: it`, sau đó member chuyển sang phòng khác
- **Hành vi**: Member không thể duyệt nữa (vì không còn department = IT)
- **Kết quả**: Request vẫn chờ giám đốc IT khác duyệt

**Case 3.1.4: Member được gán role mới**

- **Tình huống**: Request tạo với `ROLE: director`, sau đó member được gán role = director
- **Hành vi**: Member có thể duyệt (check real-time)
- **Kết quả**: Member mới có thể duyệt request cũ

**Case 3.1.5: Member bị gỡ role**

- **Tình huống**: Request tạo với `ROLE: director`, member đã approve step 1, sau đó bị gỡ role
- **Hành vi**: Member vẫn có thể duyệt step tiếp theo (nếu step tiếp theo không yêu cầu role này)
- **Kết quả**: Approval đã thực hiện vẫn hợp lệ (snapshot), nhưng không thể duyệt step mới yêu cầu role này

#### 3.2. Member có nhiều roles/positions

**Case 3.2.1: Member có nhiều roles**

- **Tình huống**: Member có roles = [director, manager, accountant]
- **Cấu hình**: `ROLE: director` hoặc `ROLE: manager` hoặc `ROLE: accountant`
- **Hành vi**: Member có thể duyệt (vì có ít nhất 1 role match)
- **Kết quả**: Member có thể duyệt nhiều loại request khác nhau

**Case 3.2.2: Member có position nhưng không có department**

- **Tình huống**: Member có position = director nhưng department = null
- **Cấu hình**: `POSITION: director` (không có departmentRefId)
- **Hành vi**: Member có thể duyệt
- **Kết quả**: Member có thể duyệt request yêu cầu "giám đốc" (không phân biệt phòng ban)

**Case 3.2.3: Member có position và department**

- **Tình huống**: Member có position = director, department = IT
- **Cấu hình**: `POSITION: director + DEPARTMENT: it`
- **Hành vi**: Member có thể duyệt
- **Kết quả**: Member có thể duyệt request yêu cầu "giám đốc phòng IT"

### 4. Case theo Trạng thái (Status)

#### 4.1. Step Status

**Case 4.1.1: Step PENDING → IN_PROGRESS**

- **Tình huống**: Step trước đó đã APPROVED, step hiện tại chuyển sang IN_PROGRESS
- **Hành vi**: Step bắt đầu chờ duyệt
- **Kết quả**: Approvers có thể thấy và duyệt step này

**Case 4.1.2: Step IN_PROGRESS → APPROVED**

- **Tình huống**: Step đã đủ điều kiện (theo approvalType)
- **Hành vi**: Step hoàn thành, chuyển sang step tiếp theo
- **Kết quả**: Request chuyển sang step tiếp theo (nếu có)

**Case 4.1.3: Step IN_PROGRESS → REJECTED**

- **Tình huống**: Có approver reject
- **Hành vi**: Step bị reject, request bị reject
- **Kết quả**: Request.status = REJECTED, không thể tiếp tục

**Case 4.1.4: Step SKIPPED**

- **Tình huống**: Step bị bỏ qua (ví dụ do điều kiện)
- **Hành vi**: Step không cần duyệt
- **Kết quả**: Step tự động APPROVED, chuyển sang step tiếp theo

#### 4.2. Approver Status

**Case 4.2.1: Approver PENDING → APPROVED**

- **Tình huống**: Approver approve step
- **Hành vi**: Approver status = APPROVED, actedAt = now()
- **Kết quả**: Step có thể hoàn thành nếu đủ điều kiện

**Case 4.2.2: Approver PENDING → REJECTED**

- **Tình huống**: Approver reject step
- **Hành vi**: Approver status = REJECTED, actedAt = now()
- **Kết quả**: Step bị reject, request bị reject

**Case 4.2.3: Approver đã APPROVED, không thể approve lại**

- **Tình huống**: Approver đã approve, cố gắng approve lại
- **Hành vi**: Hệ thống từ chối (đã approve rồi)
- **Kết quả**: Không có thay đổi

**Case 4.2.4: Approver đã REJECTED, không thể approve**

- **Tình huống**: Approver đã reject, cố gắng approve
- **Hành vi**: Hệ thống từ chối (đã reject rồi)
- **Kết quả**: Không có thay đổi

#### 4.3. Request Status

**Case 4.3.1: Request PENDING → IN_PROGRESS**

- **Tình huống**: Request mới tạo, step đầu tiên bắt đầu
- **Hành vi**: Request.status = IN_PROGRESS, step đầu tiên = IN_PROGRESS
- **Kết quả**: Approvers có thể thấy và duyệt

**Case 4.3.2: Request IN_PROGRESS → APPROVED**

- **Tình huống**: Step cuối cùng đã APPROVED
- **Hành vi**: Request.status = APPROVED
- **Kết quả**: Request hoàn thành, có thể xử lý tiếp (ví dụ: tạo PO)

**Case 4.3.3: Request IN_PROGRESS → REJECTED**

- **Tình huống**: Có step bị reject
- **Hành vi**: Request.status = REJECTED
- **Kết quả**: Request bị từ chối, không thể tiếp tục

**Case 4.3.4: Request CANCELLED**

- **Tình huống**: Người tạo hoặc hệ thống hủy request
- **Hành vi**: Request.status = CANCELLED
- **Kết quả**: Request bị hủy, không thể approve/reject

### 5. Case Edge Cases (Trường hợp đặc biệt)

#### 5.1. Không có approver nào match

**Case 5.1.1: ROLE không có member nào**

- **Tình huống**: `ROLE: director` nhưng không có member nào có role này
- **Hành vi**: Không ai có thể duyệt
- **Kết quả**: Request stuck ở step này (cần xử lý riêng: skip hoặc alert)

**Case 5.1.2: POSITION + DEPARTMENT không có member nào**

- **Tình huống**: `POSITION: director + DEPARTMENT: it` nhưng không có giám đốc IT
- **Hành vi**: Không ai có thể duyệt
- **Kết quả**: Request stuck ở step này

**Case 5.1.3: MEMBER bị xóa**

- **Tình huống**: `MEMBER: user-123` nhưng user-123 bị xóa
- **Hành vi**: Không ai có thể duyệt
- **Kết quả**: Request stuck ở step này

#### 5.2. Approver bị xóa/deactivate

**Case 5.2.1: MEMBER bị deactivate**

- **Tình huống**: `MEMBER: user-123` nhưng user-123 bị deactivate
- **Hành vi**: User-123 vẫn có thể duyệt (nếu không check status)
- **Kết quả**: Có thể cần thêm logic check status

**Case 5.2.2: Position bị xóa**

- **Tình huống**: `POSITION: director-id` nhưng position bị xóa
- **Hành vi**: Không ai có thể duyệt (vì không có member nào có position này)
- **Kết quả**: Request stuck

**Case 5.2.3: Department bị xóa**

- **Tình huống**: `DEPARTMENT: it-id` nhưng department bị xóa
- **Hành vi**: Không ai có thể duyệt (vì không có member nào trong department này)
- **Kết quả**: Request stuck

#### 5.3. Config thay đổi sau khi tạo request

**Case 5.3.1: Config bị xóa**

- **Tình huống**: Config bị xóa sau khi đã tạo request
- **Hành vi**: Request vẫn chạy bình thường (vì đã snapshot)
- **Kết quả**: Request không bị ảnh hưởng

**Case 5.3.2: Config bị sửa**

- **Tình huống**: Config bị sửa (thay đổi approvers) sau khi đã tạo request
- **Hành vi**: Request vẫn dùng config cũ (snapshot)
- **Kết quả**: Request không bị ảnh hưởng

**Case 5.3.3: Config bị deactivate**

- **Tình huống**: Config bị deactivate sau khi đã tạo request
- **Hành vi**: Request vẫn chạy bình thường
- **Kết quả**: Request không bị ảnh hưởng, nhưng không thể tạo request mới với config này

#### 5.4. Concurrent Approval (Duyệt đồng thời)

**Case 5.4.1: Nhiều người cùng approve ANY**

- **Tình huống**: `approvalType: ANY`, 2 người cùng approve
- **Hành vi**: Cả 2 đều có thể approve, step hoàn thành khi người đầu tiên approve
- **Kết quả**: Người thứ 2 có thể bị reject (đã hoàn thành rồi) hoặc vẫn approve được (tùy logic)

**Case 5.4.2: Nhiều người cùng approve ALL**

- **Tình huống**: `approvalType: ALL`, nhiều người cùng approve
- **Hành vi**: Tất cả đều có thể approve, step hoàn thành khi người cuối cùng approve
- **Kết quả**: Tất cả approvals đều hợp lệ

**Case 5.4.3: Người approve và reject cùng lúc**

- **Tình huống**: 1 người approve, 1 người reject cùng lúc
- **Hành vi**: Tùy logic xử lý (thường reject có priority cao hơn)
- **Kết quả**: Request bị reject

#### 5.5. minApprovers với ANY

**Case 5.5.1: minApprovers = 1 (mặc định)**

- **Tình huống**: `approvalType: ANY`, `minApprovers: 1` (hoặc không set)
- **Hành vi**: Chỉ cần 1 người approve
- **Kết quả**: Step hoàn thành khi 1 người approve

**Case 5.5.2: minApprovers = 2**

- **Tình huống**: `approvalType: ANY`, `minApprovers: 2`, có 5 approvers
- **Hành vi**: Cần ít nhất 2 người approve
- **Kết quả**: Step hoàn thành khi có ít nhất 2 người approve

**Case 5.5.3: minApprovers > số lượng approvers**

- **Tình huống**: `approvalType: ANY`, `minApprovers: 5`, nhưng chỉ có 3 approvers
- **Hành vi**: Không thể hoàn thành step (vì không đủ số lượng)
- **Kết quả**: Request stuck (cần validation khi tạo config)

**Case 5.5.4: minApprovers với ROLE/POSITION/DEPARTMENT**

- **Tình huống**: `approvalType: ANY`, `minApprovers: 2`, `approverType: ROLE`
- **Hành vi**: Cần ít nhất 2 người có role này approve
- **Kết quả**: Step hoàn thành khi có ít nhất 2 người approve

### 6. Case theo Kết hợp (Combination)

#### 6.1. Nhiều ApproverType trong 1 Step

**Case 6.1.1: MEMBER + ROLE**

- **Cấu hình**: `approvers: [MEMBER: user-123, ROLE: director]`
- **Với SINGLE**: Chỉ cần 1 trong 2 approve
- **Với ANY**: Chỉ cần 1 trong 2 approve
- **Với ALL**: Cả 2 đều phải approve (user-123 VÀ tất cả người có role = director)

**Case 6.1.2: POSITION + DEPARTMENT (riêng biệt)**

- **Cấu hình**: `approvers: [POSITION: director, DEPARTMENT: it]`
- **Với SINGLE**: Chỉ cần 1 trong 2 approve (giám đốc HOẶC người trong phòng IT)
- **Với ANY**: Chỉ cần 1 trong 2 approve
- **Với ALL**: Cả 2 đều phải approve (tất cả giám đốc VÀ tất cả người trong phòng IT)

**Case 6.1.3: POSITION + DEPARTMENT (kết hợp)**

- **Cấu hình**: `approvers: [POSITION: director + DEPARTMENT: it]` (1 approver với cả 2 điều kiện)
- **Với SINGLE**: Chỉ giám đốc IT có thể duyệt
- **Với ANY**: Chỉ giám đốc IT có thể duyệt
- **Với ALL**: Tất cả giám đốc IT đều phải duyệt

#### 6.2. Nhiều Step với các ApprovalLevelType khác nhau

**Case 6.2.1: Step 1 (SINGLE) → Step 2 (ANY) → Step 3 (ALL)**

- **Cấu hình**:
  - Step 1: `SINGLE`, `MEMBER: user-123`
  - Step 2: `ANY`, `ROLE: director`
  - Step 3: `ALL`, `MEMBER: user-456, MEMBER: user-789`
- **Hành vi**:
  - Step 1: Chỉ user-123 duyệt
  - Step 2: Bất kỳ ai có role = director duyệt
  - Step 3: Cả user-456 và user-789 đều phải duyệt
- **Kết quả**: Request hoàn thành khi tất cả 3 steps đều APPROVED

**Case 6.2.2: Step 1 (ALL) → Step 2 (SINGLE)**

- **Cấu hình**:
  - Step 1: `ALL`, `MEMBER: user-123, MEMBER: user-456`
  - Step 2: `SINGLE`, `ROLE: director`
- **Hành vi**:
  - Step 1: Cả user-123 và user-456 đều phải duyệt
  - Step 2: Bất kỳ ai có role = director duyệt
- **Kết quả**: Request hoàn thành khi step 2 APPROVED

### 7. Case theo Snapshot và History

#### 7.1. Snapshot được tạo khi tạo request

**Case 7.1.1: Snapshot cho MEMBER**

- **Tình huống**: `MEMBER: user-123`
- **Snapshot**: Lưu user-123 với thông tin lúc tạo request
- **Kết quả**: Biết được user-123 là approver lúc tạo request

**Case 7.1.2: Snapshot cho ROLE**

- **Tình huống**: `ROLE: director`
- **Snapshot**: Lưu tất cả members có role = director lúc tạo request
- **Kết quả**: Biết được ai là approver lúc tạo request (có thể thay đổi sau đó)

**Case 7.1.3: Snapshot cho POSITION + DEPARTMENT**

- **Tình huống**: `POSITION: director + DEPARTMENT: it`
- **Snapshot**: Lưu tất cả giám đốc IT lúc tạo request
- **Kết quả**: Biết được ai là giám đốc IT lúc tạo request

#### 7.2. History tracking

**Case 7.2.1: Xem lịch sử approvers lúc tạo request**

- **Tình huống**: Query snapshot để xem ai là approver lúc tạo request
- **Hành vi**: Trả về danh sách members từ snapshot
- **Kết quả**: Biết được ai là approver ban đầu (có thể khác với hiện tại)

**Case 7.2.2: Xem lịch sử thay đổi**

- **Tình huống**: Member thay đổi position/department sau khi approve
- **Hành vi**: Snapshot vẫn giữ nguyên thông tin lúc tạo request
- **Kết quả**: Có thể audit được ai là approver lúc đó

---

## Tài liệu tham khảo

- Enum: `src/common/enums/approval.enum.ts`
- Entities: `src/domains/primary/approval-*`
- Repositories: `src/domains/primary/approval-*/*.repo.ts`
