// src/domains/crm/customer.entity.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSCustomer } from '~/common/enums/customer.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('customer')
export class CustomerEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'CUST-0001' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    code: string;

    @ApiProperty({ example: 'Công ty ABC' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiPropertyOptional({ example: '<EMAIL>' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    email?: string | null;

    @ApiPropertyOptional({ example: '+84 ***********' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    phone?: string | null;

    @ApiPropertyOptional({ example: '0312345678' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    taxNumber?: string | null;

    @ApiPropertyOptional({ example: 'https://abc.com' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    website?: string | null;

    @ApiPropertyOptional({ example: 'VN' })
    @Column({ type: 'varchar', length: 10, nullable: true })
    countryCode?: string | null;

    @ApiPropertyOptional({ example: '79' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    provinceCode?: string | null;

    @ApiPropertyOptional({ example: '27134' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    communeCode?: string | null;

    @ApiPropertyOptional({ example: 'Hồ Chí Minh' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    address?: string | null;

    @ApiPropertyOptional({ example: 'Miền Bắc' })
    @Column({ type: 'varchar', length: 255, nullable: true })
    region?: string | null;

    @ApiPropertyOptional({ example: '700000', description: 'Mã bưu điện' })
    @Column({ type: 'varchar', length: 20, nullable: true })
    postalCode?: string | null;

    @ApiPropertyOptional({ example: 'Khách doanh nghiệp lớn' })
    @Column({ type: 'text', nullable: true })
    note?: string | null;

    @ApiPropertyOptional({ example: '["user1", "user2"]' })
    @Column({ type: 'jsonb', nullable: true, default: () => `('[]'::jsonb)` })
    salesRep?: string[];

    @ApiProperty({ example: true })
    @Column({
        type: 'enum',
        enum: NSCustomer.EActiveStatus,
        default: NSCustomer.EActiveStatus.ACTIVE,
    })
    status: NSCustomer.EActiveStatus;

    @ApiPropertyOptional({ example: 'BUSINESS' })
    @Column({ type: 'varchar', enum: NSCustomer.EType, nullable: true })
    type?: NSCustomer.EType;

    @Column({ type: 'varchar', nullable: true })
    customerType?: string;

    @Column({
        type: 'varchar',
        length: 200,
        nullable: true,
        default: NSCustomer.ECustomerKind.UNKNOWN,
    })
    customerKind?: NSCustomer.ECustomerKind;

    @Column({ type: 'varchar', length: 200, nullable: true })
    source?: NSCustomer.ESource;

    @Column({ type: 'varchar', length: 200, nullable: true })
    market?: NSCustomer.EMarket;

    @Column({ type: 'varchar', length: 50, nullable: true })
    fax?: string | null;

    @ApiPropertyOptional({ description: 'Ngày sinh', example: '1990-01-01' })
    @Column({ type: 'timestamptz', nullable: true })
    birthday?: Date | null;
}
