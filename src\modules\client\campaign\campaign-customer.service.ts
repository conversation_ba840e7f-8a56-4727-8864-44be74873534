import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MemberRepo } from '~/domains/primary';
import { CampaignCustomerRepo } from '~/domains/primary/campaign-customer/campaign-customer.repo';
import { clientSessionContext } from '../client-session.context';
import { ListCampaignCustomerDto } from './dto/campaign-customer.dto';

@Injectable()
export class CampaignCustomerService {
    constructor(
        @InjectRepository(CampaignCustomerRepo)
        private readonly campaignCustomerRepo: CampaignCustomerRepo,
        @InjectRepository(MemberRepo)
        private readonly memberRepo: MemberRepo,
    ) {}

    async list(params: ListCampaignCustomerDto) {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize } = params;
        let where: any = {};

        if (params.status) {
            where.status = params.status;
        }
        if (params.campaignId) {
            where.campaignId = params.campaignId;
        }
        if (params.customerId) {
            where.customerId = params.customerId;
        }
        if (params.sendDate) {
            where.sendDate = params.sendDate;
        }

        let res: any = [];
        if (Number(pageSize) === -1) {
            res = await this.campaignCustomerRepo.find({
                where: { ...where, tenantId },
                order: {
                    createdDate: 'DESC',
                },
            });
            return {
                total: res.length,
                data: res,
            };
        } else {
            res = await this.campaignCustomerRepo.findPagination(
                {
                    where: { ...where, tenantId },
                    order: {
                        createdDate: 'DESC',
                    },
                },
                { pageIndex, pageSize },
            );

            const members = await this.memberRepo.find({
                where: { tenantId },
            });

            const reMapRes = res.data.map(item => ({
                ...item,
                senderName: members.find(m => m.id === item.createdBy)?.fullName,
            }));

            return {
                total: res.total,
                data: reMapRes,
            };
        }
    }
}
