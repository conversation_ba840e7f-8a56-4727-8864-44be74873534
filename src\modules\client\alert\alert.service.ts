import { Injectable } from '@nestjs/common';

import { InjectRepo } from 'nestjs-typeorm3-kit';

import { Between } from 'typeorm';
import { NSAlert } from '~/common/enums/alert.enum';
import { AlertRepo } from '~/domains/primary/alert/alert.repo';
import { clientSessionContext } from '../client-session.context';
import {
    IAlert,
    IAlertSummary,
    IListAlertsResponse,
    IMarkAsReadResponse,
    ListAlertsRequestDto,
    MarkAsReadRequestDto,
} from './dto/alert.dto';

@Injectable()
export class AlertService {
    constructor(@InjectRepo(AlertRepo) private alertRepo: AlertRepo) {}

    /**
     * <PERSON><PERSON>y danh sách cảnh báo với phân trang và filter
     */
    async list(params: ListAlertsRequestDto): Promise<IListAlertsResponse> {
        const { tenantId } = clientSessionContext;
        const { pageIndex, pageSize, type, severity, isRead, startDate, endDate, complaintId } =
            params;

        let where: any = {};
        // Áp dụng các filter
        if (type) {
            where.type = type;
        }

        if (severity) {
            where.severity = severity;
        }

        if (typeof isRead === 'boolean') {
            where.isRead = isRead;
        }

        if (startDate && endDate) {
            where.createdDate = Between(new Date(startDate), new Date(endDate));
        }

        if (complaintId) {
            where.complaintId = complaintId;
        }

        let res: any = {};
        if (pageSize === -1) {
            const [data, total] = await this.alertRepo.findAndCount({
                where: {
                    ...where,
                    tenantId,
                },
                order: {
                    createdDate: 'DESC',
                },
            });
            res = {
                data,
                total,
            };
        } else {
            res = await this.alertRepo.findPaginationByTenant(
                {
                    where,
                    order: {
                        createdDate: 'DESC',
                    },
                },
                {
                    pageIndex,
                    pageSize,
                },
            );
        }
        // Map entities to DTOs
        const items: IAlert[] = res.data.map(alert => ({
            id: alert.id!,
            type: alert.type,
            severity: alert.severity,
            title: alert.title,
            description: alert.description,
            complaintId: alert.complaintId,
            customerId: alert.customerId,
            source: alert.source,
            dueDate: alert.dueDate,
            createdDate: alert.createdDate ? alert.createdDate.toISOString() : '',
            isRead: alert.isRead,
            metadata: alert.metadata,
        }));
        const summary: any = {};
        return {
            items,
            summary,
            total: res.total,
            page: pageIndex,
            pageSize,
        };
    }

    /**
     * Đánh dấu một hoặc nhiều cảnh báo là đã đọc
     */
    async markAsRead(body: MarkAsReadRequestDto) {
        const { tenantId } = clientSessionContext;
        const { alertIds } = body;
        // Update alerts
        for (const item of alertIds) {
            await this.alertRepo.update({ id: item }, { isRead: true, tenantId, id: item });
        }

        return {
            success: true,
        };
    }

    /**
     * Đánh dấu tất cả cảnh báo là đã đọc
     */
    async markAllAsRead(): Promise<IMarkAsReadResponse> {
        const { tenantId } = clientSessionContext;

        const alerts = await this.alertRepo.find({
            where: {
                tenantId,
                isRead: false,
            },
        });

        for (const alert of alerts) {
            alert.isRead = true;
            await this.alertRepo.save(alert);
        }

        const result = { affected: alerts.length };

        return {
            success: true,
            count: result.affected || 0,
        };
    }

    /**
     * Lấy tổng hợp thống kê cảnh báo
     */
    async getSummary(): Promise<IAlertSummary> {
        const { tenantId } = clientSessionContext;
        return this.getSummaryInternal(tenantId);
    }

    /**
     * Internal method để tính summary
     */
    private async getSummaryInternal(tenantId: string): Promise<IAlertSummary> {
        const queryBuilder = this.alertRepo
            .createQueryBuilder('alert')
            .where('alert.tenantId = :tenantId', { tenantId });

        // Đếm theo severity
        const [critical, high, medium, low] = await Promise.all([
            queryBuilder
                .clone()
                .andWhere('alert.severity = :severity', { severity: NSAlert.ESeverity.CRITICAL })
                .getCount(),
            queryBuilder
                .clone()
                .andWhere('alert.severity = :severity', { severity: NSAlert.ESeverity.HIGH })
                .getCount(),
            queryBuilder
                .clone()
                .andWhere('alert.severity = :severity', { severity: NSAlert.ESeverity.MEDIUM })
                .getCount(),
            queryBuilder
                .clone()
                .andWhere('alert.severity = :severity', { severity: NSAlert.ESeverity.LOW })
                .getCount(),
        ]);

        const total = critical + high + medium + low;

        // Đếm theo type
        const byType: Record<NSAlert.EType, number> = {
            [NSAlert.EType.COMPLAINT_NEW]: 0,
            [NSAlert.EType.COMPLAINT_OVERDUE]: 0,
            [NSAlert.EType.COMPLAINT_FOLLOWUP]: 0,
            [NSAlert.EType.COMPLAINT_NEAR_DEADLINE]: 0,
            [NSAlert.EType.CONTRACT_NEW]: 0,
            [NSAlert.EType.CONTRACT_FOLLOWUP]: 0,
            [NSAlert.EType.CONTRACT_NEAR_DEADLINE]: 0,
            [NSAlert.EType.CONTRACT_OVERDUE]: 0,
            [NSAlert.EType.QUOTE_NEW]: 0,
            [NSAlert.EType.QUOTE_OVERDUE]: 0,
            [NSAlert.EType.QUOTE_FOLLOWUP]: 0,
            [NSAlert.EType.QUOTE_NEAR_DEADLINE]: 0,
            [NSAlert.EType.CONTACT_CARE_NEW]: 0,
            [NSAlert.EType.CONTACT_CARE_OVERDUE]: 0,
            [NSAlert.EType.CONTACT_CARE_FOLLOWUP]: 0,
            [NSAlert.EType.CONTACT_CARE_NEAR_DEADLINE]: 0,
            [NSAlert.EType.CAMPAIGN_NEW]: 0,
            [NSAlert.EType.CAMPAIGN_FOLLOWUP]: 0,
            [NSAlert.EType.CAMPAIGN_NEAR_DEADLINE]: 0,
            [NSAlert.EType.CAMPAIGN_OVERDUE]: 0,
        };

        // Lấy count theo từng type
        const typeCounts = await Promise.all(
            Object.values(NSAlert.EType).map(async type => {
                const count = await queryBuilder
                    .clone()
                    .andWhere('alert.type = :type', { type })
                    .getCount();
                return { type, count };
            }),
        );

        typeCounts.forEach(({ type, count }) => {
            byType[type] = count;
        });

        return {
            total,
            critical,
            high,
            medium,
            low,
            byType,
        };
    }
}
