import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { CustomerAddressService } from './customer-address.service';
import {
    CreateCustomerAddressDto,
    ListCreateCustomerAddressDto,
} from './dto/create-customer-address.dto';
import { DeleteListCustomerAddressDto } from './dto/delete-customer-address.dto';
import { ListCustomerAddressDto } from './dto/list-customer-address.dto';
import {
    ListUpdateCustomerAddressDto,
    UpdateCustomerAddressDto,
} from './dto/update-customer-address.dto';

@UseGuards(PermissionGuard)
@DefController('customer-address')
export class CustomerAddressController {
    constructor(private readonly customerAddressService: CustomerAddressService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.VIEW])
    async list(@Query() params: ListCustomerAddressDto) {
        return this.customerAddressService.list(params);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.CREATE])
    create(@Body() createCustomerAddressDto: CreateCustomerAddressDto) {
        return this.customerAddressService.create(createCustomerAddressDto);
    }

    @DefPost('create-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.CREATE])
    createList(@Body() createCustomerAddressDto: ListCreateCustomerAddressDto) {
        return this.customerAddressService.createList(createCustomerAddressDto);
    }

    @DefPost('active')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    async setActive(@Body() params: { id: string }) {
        return this.customerAddressService.setActive(params.id);
    }

    @DefPost('in-active')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    async inActive(@Body() params: { id: string }) {
        return this.customerAddressService.setInActive(params.id);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    update(@Body() updateCustomerAddressDto: UpdateCustomerAddressDto) {
        return this.customerAddressService.update(updateCustomerAddressDto);
    }

    //update list

    @DefPost('update-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    async updateList(@Body() updateCustomerAddressDto: ListUpdateCustomerAddressDto) {
        return this.customerAddressService.updateList(updateCustomerAddressDto);
    }

    @DefPost('set-default')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    async setDefault(@Body() params: { id: string }) {
        return this.customerAddressService.setDefault(params.id);
    }

    @DefPost('delete-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CUSTOMER_ADDRESS.UPDATE])
    async deleteList(@Body() params: DeleteListCustomerAddressDto) {
        return this.customerAddressService.deleteList(params);
    }

    @DefGet('get-province-by-code')
    getProvinceByCode(@Query('query') query: string) {
        return this.customerAddressService.getProvinceByCode(query);
    }

    @DefGet('get-commune-by-code')
    getCommuneByCode(@Query('query') query: string) {
        return this.customerAddressService.getCommuneByCode(query);
    }
}
