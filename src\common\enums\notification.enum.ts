export namespace NSNotification {
    export enum ESource {
        CUSTOMER = 'CUSTOMER',
        QUOTATION = 'QUOTATION',
        COMPLAINT = 'COMPLAINT',
        CONTRACT = 'CONTRACT',
        CAMPAIGN = 'CAMPAIGN',
        SYSTEM = 'SYSTEM',
        CONTACT_CARE = 'CONTACT_CARE',
    }
    export enum ESeverity {
        CRITICAL = 'CRITICAL',
        HIGH = 'HIGH',
        MEDIUM = 'MEDIUM',
        LOW = 'LOW',
    }
    export enum EType {
        SYSTEM_MESSAGE = 'SYSTEM_MESSAGE',
        CUSTOMER_NEW = 'CUSTOMER_NEW',
        CUSTOMER_UPDATED = 'CUSTOMER_UPDATED',
        QUOTATION_NEW = 'QUOTATION_NEW',
        QUOTATION_OVERDUE = 'QUOTATION_OVERDUE',
        COMPLAINT_NEW = 'COMPLAINT_NEW',
        COMPLAINT_PROCESS = 'COMPLAINT_PROCESS',
        COMPLAINT_OVERDUE = 'COMPLAINT_OVERDUE',
        CONTRACT_NEW = 'CONTRACT_NEW',
        CONTRACT_PROCESS = 'CONTRACT_PROCESS',
        CONTRACT_OVERDUE = 'CONTRACT_OVERDUE',
        CAMPAIGN_NEW = 'CAMPAIGN_NEW',
        CAMPAIGN_PROCESS = 'CAMPAIGN_PROCESS',
        CAMPAIGN_OVERDUE = 'CAMPAIGN_OVERDUE',
        CONTACT_CARE_NEW = 'CONTACT_CARE_NEW',
        CONTACT_CARE_OVERDUE = 'CONTACT_CARE_OVERDUE',
        CONTACT_CARE_PROCESS = 'CONTACT_CARE_PROCESS',
    }
    export enum ERecipientType {
        MEMBER = 'MEMBER',
        ROLE = 'ROLE',
        DEPARTMENT = 'DEPARTMENT',
        TENANT_ALL = 'TENANT_ALL',
    }
    export enum EStatus {
        DRAFT = 'DRAFT',
        QUEUED = 'QUEUED',
        SENDING = 'SENDING',
        SENT = 'SENT',
        FAILED = 'FAILED',
        CANCELED = 'CANCELED',
    }
    export enum EDeliveryStatus {
        PENDING = 'PENDING',
        SENT = 'SENT',
        FAILED = 'FAILED',
    }
}
