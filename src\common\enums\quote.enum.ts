export namespace NSQuote {
    //#region Trạng thái
    export enum EStatus {
        NEW = 'NEW', // Mới tạo
        SENT = 'SENT', // Đã gửi duyệt
        APPROVED = 'APPROVED', // Đã duyệt
        REJECTED = 'REJECTED', // Từ chối
        SENT_TO_CUSTOMER = 'SENT_TO_CUSTOMER', // Đã gửi khách hàng
        CONFIRMED = 'CONFIRMED', // Đã chốt
        CANCELLED = 'CANCELLED', // Đã huỷ
    }

    export const ESTATUS_LOCALE_LABEL = {
        [EStatus.NEW]: 'enums.NSQuote.EStatus.NEW',
        [EStatus.SENT]: 'enums.NSQuote.EStatus.SENT',
        [EStatus.CONFIRMED]: 'enums.NSQuote.EStatus.CONFIRMED',
        [EStatus.REJECTED]: 'enums.NSQuote.EStatus.REJECTED',
        [EStatus.CANCELLED]: 'enums.NSQuote.EStatus.CANCELLED',
        [EStatus.APPROVED]: 'enums.NSQuote.EStatus.APPROVED',
    };

    export enum EPaymentTermStatus {
        ACTIVE = 'ACTIVE', // Hoạt động
        INACTIVE = 'INACTIVE', // Không hoạt động
    }
    //#endregion
}
