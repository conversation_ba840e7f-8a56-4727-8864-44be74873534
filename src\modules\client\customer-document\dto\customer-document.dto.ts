import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

// List dto
export class ListCustomerDocumentDto extends PageRequest {
    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    @IsOptional()
    name?: string;

    @ApiProperty({ example: '2023-01-01', description: 'Ngày tạo' })
    @IsString()
    @IsOptional()
    createdDate?: string;

    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    @IsOptional()
    customerId?: string;
}

// Create

export class PayloadCreateCustomerDocumentDto {
    @ApiProperty({ example: '1231', description: 'Tên tài liệu' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ example: '123', description: 'Mô tả' })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    customerId: string;

    @ApiProperty({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111', description: 'Media ID', required: false })
    @IsString()
    @IsOptional()
    mediaId?: string;
}
export class CreateCustomerDocumentDto {
    @ApiProperty({ type: () => PayloadCreateCustomerDocumentDto })
    @IsNotEmpty()
    payload: PayloadCreateCustomerDocumentDto;
}
export class PayloadUpdateCustomerDocumentDto {
    @ApiProperty({ example: '123123', description: 'Mã tài liệu' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ example: '1231', description: 'Tên tài liệu' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ example: '123', description: 'Mô tả' })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    customerId: string;

    @ApiProperty({ example: 'a6c8f2fd-5c8b-4aa8-8f4c-07a5b8d1a111', description: 'Media ID', required: false })
    @IsString()
    @IsOptional()
    mediaId?: string;
}
export class UpdateCustomerDocumentDto {
    @ApiProperty({ type: () => PayloadUpdateCustomerDocumentDto })
    @IsNotEmpty()
    payload: PayloadUpdateCustomerDocumentDto;
}
export class SetActiveCustomerDocumentDto {
    @ApiProperty({ example: '123123', description: 'Mã tài liệu' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ example: 'CUST-0001', description: 'Mã khách hàng' })
    @IsString()
    @IsNotEmpty()
    customerId: string;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái' })
    @IsString()
    @IsNotEmpty()
    status: string;
}
