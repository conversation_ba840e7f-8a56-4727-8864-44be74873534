import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSContractTemplate } from '~/common/enums/contract-template.enum';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('contract_template')
export class ContractTemplateEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'CNTT-0001', description: 'Mã mẫu hợp đồng' })
    @Column({ type: 'varchar', length: 64 })
    @Index()
    code: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'Tên mẫu hợp đồng' })
    @Column({ type: 'varchar', length: 255 })
    name: string;

    @ApiProperty({ example: 'CNTT-0001', description: 'HTML hợp đồng' })
    @Column({ type: 'text' })
    html: string;

    @ApiProperty({ example: 'ACTIVE', description: 'Trạng thái mẫu hợp đồng' })
    @Column({
        type: 'enum',
        enum: NSContractTemplate.EStatus,
        default: NSContractTemplate.EStatus.ACTIVE,
    })
    status: NSContractTemplate.EStatus;
}
