// src/domains/crm/dto/catalog-service.dto.ts
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, Length } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSCatalog } from '~/common/enums';

/**
 * CREATE
 */
export class CreateCatalogItemDto {
    @ApiProperty({ example: 'Gói SEO theo tháng' })
    @IsString()
    @Length(1, 255)
    name: string;

    @ApiPropertyOptional({ example: 'Tối ưu SEO onpage + offpage' })
    @IsOptional()
    @IsString()
    description?: string | null;

    @ApiPropertyOptional({ example: 'https://example.com/image.jpg' })
    @IsOptional()
    @IsString()
    imageUrl?: string | null;

    //attachment
    @ApiPropertyOptional({ example: 'https://example.com/attachment.pdf' })
    @IsOptional()
    @IsString()
    attachments?: string | null;

    @ApiProperty({
        example: NSCatalog.EStatus.ACTIVE,
        enum: NSCatalog.EStatus,
        default: NSCatalog.EStatus.ACTIVE,
    })
    @IsEnum(NSCatalog.EStatus)
    status: NSCatalog.EStatus = NSCatalog.EStatus.ACTIVE;
}

/**
 * UPDATE
 */
export class UpdateCatalogItemDto extends PartialType(CreateCatalogItemDto) {
    @ApiProperty({ description: 'ID của Catalog' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

/**
 * LIST / FILTER + PAGINATION + SORT
 */
export class ListCatalogItemDto extends PageRequest {
    @ApiPropertyOptional({
        description: 'Tìm kiếm nhanh theo code/name',
        example: 'SEO',
    })
    @IsOptional()
    @IsString()
    q?: string;

    @ApiPropertyOptional({ example: 'Gói SEO theo tháng' })
    @IsOptional()
    @IsString()
    catalogId?: string;

    @ApiPropertyOptional({ example: 'CAT-0001' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ example: 'Gói SEO theo tháng' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({ enum: NSCatalog.EType, example: NSCatalog.EType.SERVICE })
    @IsOptional()
    @IsEnum(NSCatalog.EType)
    type?: NSCatalog.EType;

    @ApiPropertyOptional({ enum: NSCatalog.EStatus, example: NSCatalog.EStatus.ACTIVE })
    @IsOptional()
    @IsEnum(NSCatalog.EStatus)
    status?: NSCatalog.EStatus;

    @ApiPropertyOptional({ example: '2024-01-01' })
    @IsOptional()
    createdFrom?: string;

    @ApiPropertyOptional({ example: '2024-12-31' })
    @IsOptional()
    createdTo?: string;
}
