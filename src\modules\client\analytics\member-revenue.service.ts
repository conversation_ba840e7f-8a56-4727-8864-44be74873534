import { Injectable } from '@nestjs/common';
import { Between, ILike, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSContract } from '~/common/enums/contract.enum';
import { MemberRepo, QuoteRepo } from '~/domains/primary';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { clientSessionContext } from '../client-session.context';
import { MemberService } from '../member/member.service';
import { ListMemberRevenueDto } from './dto/member-revenue';

@Injectable()
export class MemberRevenueService {
    constructor(
        private readonly memberRepo: MemberRepo,
        private readonly quoteRepo: QuoteRepo,
        private readonly contractRepo: ContractRepo,
        private readonly memberService: MemberService,
    ) {}

    async listRevenueReport(query: ListMemberRevenueDto) {
        const { memberName, status, revenueFrom, revenueTo, dateFrom, dateTo } = query;
        const { tenantId } = clientSessionContext;

        // Xây dựng điều kiện lọc cho members
        let memberWhere: any = { tenantId };
        if (memberName) {
            memberWhere.fullName = ILike(`%${memberName}%`);
        }
        if (status) {
            memberWhere.status = status;
        }

        // điều kiện lấy doanh số theo khoảng thời gian dựa trên ngày ký (signingDate)
        let contractWhere: any = { tenantId, status: NSContract.EStatus.SIGNED };
        if (dateFrom && dateTo) {
            contractWhere.signingDate = Between(new Date(dateFrom), new Date(dateTo));
        } else if (dateFrom) {
            contractWhere.signingDate = MoreThanOrEqual(new Date(dateFrom));
        } else if (dateTo) {
            contractWhere.signingDate = LessThanOrEqual(new Date(dateTo));
        }

        const [members, quotes, contracts] = await Promise.all([
            this.memberRepo.find({
                where: { ...memberWhere },
                order: {
                    createdDate: 'DESC',
                },
            }),
            this.quoteRepo.find({
                where: {
                    tenantId,
                },
            }),
            this.contractRepo.find({
                where: { ...contractWhere, tenantId },
                order: {
                    signingDate: 'DESC',
                },
            }),
        ]);
        try {
            let res = members.map(member => {
                const memberContracts = contracts.filter(item => item.createdBy === member.id);
                const totalContract = memberContracts.length;
                const totalRevenue = memberContracts.reduce((acc, contract) => {
                    const quote = quotes.find(item => item.id === contract.quoteId);
                    return acc + Number(quote?.totalAmount || 0);
                }, 0);

                return {
                    memberName: member.fullName,
                    status: member.status,
                    totalRevenue,
                    totalContract,
                };
            });

            if (revenueFrom || revenueTo) {
                res = res.filter(item => {
                    if (revenueFrom && revenueTo) {
                        return (
                            item.totalRevenue >= Number(revenueFrom) &&
                            item.totalRevenue <= Number(revenueTo)
                        );
                    } else if (revenueFrom) {
                        return item.totalRevenue >= Number(revenueFrom);
                    } else if (revenueTo) {
                        return item.totalRevenue <= Number(revenueTo);
                    }
                    return true;
                });
            }

            res.sort((a, b) => b.totalRevenue - a.totalRevenue);

            // Apply pagination
            const pageIndex = Number(query.pageIndex) || 1;
            const pageSize = Number(query.pageSize) || 10;
            const startIndex = (pageIndex - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedData = res.slice(startIndex, endIndex);

            return {
                data: paginatedData,
                total: res.length,
            };
        } catch (error) {
            console.error('Error generating revenue report:', error);
            throw new BusinessException(error.message);
        }
    }
}
