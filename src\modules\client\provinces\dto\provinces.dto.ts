import { ApiProperty } from '@nestjs/swagger';

export interface IProvinces {
    id: string;
    name: string;
    code: string;
    administrativeLevel: string;
    createdDate: Date;
    updatedDate: Date;
}

export interface IPaginationProvinces {
    pageIndex?: number;
    pageSize?: number;
    name?: string;
    code?: string;
    administrativeLevel?: string;
    createdDate?: Date;
    updatedDate?: Date;
}

export class ListDistrictReq {
    @ApiProperty({ description: 'Province Code', example: 1 })
    code?: number;
}

export class ListWardReq {
    @ApiProperty({ description: 'District Code', example: 1 })
    code: number;
}

export interface IWard {
    name: string;
    code: number;
    division_type: string;
    codename: string;
    district_code: number;
}

export interface IDistrict {
    name: string;
    code: number;
    division_type: string;
    codename: string;
    province_code: number;
    wards?: IWard[];
}

export interface IProvince {
    name: string;
    code: number;
    division_type: string;
    codename: string;
    phone_code: number;
    districts?: IDistrict[];
}
