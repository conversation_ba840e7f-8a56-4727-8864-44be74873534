import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSQuote } from '~/common/enums';

export class QuoteItemsDto {}

export class QuoteItemDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class CreateQuoteItemDto {
    @ApiProperty({ description: 'ID sản phẩm' })
    @IsString()
    @IsNotEmpty()
    catalogItemId: string;

    @ApiProperty({ description: 'ID báo giá' })
    @IsString()
    @IsNotEmpty()
    quoteId: string;

    @ApiPropertyOptional({ description: 'Mã sản phẩm' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty({ description: 'Tên sản phẩm' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'Loại sản phẩm' })
    @IsString()
    @IsNotEmpty()
    type: string;

    @ApiPropertyOptional({ description: 'Mô tả' })
    @IsOptional()
    description?: any;

    @ApiProperty({ description: 'Đơn vị' })
    @IsString()
    @IsNotEmpty()
    unit: string;

    @ApiProperty({ description: 'Đơn vị tiền tệ' })
    @IsString()
    @IsNotEmpty()
    currency: string;

    @ApiProperty({ description: 'Đơn giá' })
    @IsNumber()
    @Type(() => Number)
    unitPrice: number;

    @ApiProperty({ description: 'Thuế %' })
    @IsNumber()
    @Type(() => Number)
    vat: number;

    @ApiProperty({ description: 'Số lượng' })
    @IsNumber()
    @Type(() => Number)
    quantity: number;

    @ApiProperty({ description: 'Tổng tiền trước VAT' })
    @IsNumber()
    @Type(() => Number)
    totalBeforeVat: number;

    @ApiProperty({ description: 'Tổng tiền sau VAT' })
    @IsNumber()
    @Type(() => Number)
    totalAfterVat: number;
}

export class ListQuoteItemDto extends PageRequest {
    @ApiPropertyOptional({ example: 'a1b2c3d4-...', description: 'ID báo giá' })
    @IsString()
    @IsOptional()
    quoteId?: string;

    @ApiPropertyOptional({ example: 'Tên sản phẩm' })
    @IsOptional()
    @IsString()
    name?: string;
}

export class UpdateQuoteItemDto extends PartialType(CreateQuoteItemDto) {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class UpdateStatusDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ enum: NSQuote.EStatus, example: NSQuote.EStatus.NEW })
    @IsString()
    status: NSQuote.EStatus;
}
