import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import { ContactService } from './contact.service';
import {
    Create<PERSON>ontactDto,
    DeleteListContactDto,
    DetailContactDto,
    ListContactDto,
    ListCreateContactDto,
    ListUpdateContactDto,
    UpdateContactDto,
} from './dto/contact';

@UseGuards(PermissionGuard)
@DefController('contact')
export class ContactController {
    constructor(private contactService: ContactService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.VIEW])
    async listContact(@Query() params: ListContactDto) {
        return this.contactService.listContact(params);
    }

    @DefPost('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.VIEW])
    async detailContact(@Body() contact: DetailContactDto) {
        return this.contactService.detailContact(contact);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.CREATE])
    async createContact(@Body() contact: CreateContactDto) {
        return this.contactService.createContact(contact);
    }

    @DefPost('create-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.CREATE])
    async createListContact(@Body() contact: ListCreateContactDto) {
        return this.contactService.createListContact(contact);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.UPDATE])
    async updateContact(@Body() contact: UpdateContactDto) {
        return this.contactService.updateContact(contact);
    }

    @DefPost('update-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.UPDATE])
    async updateListContact(@Body() contact: ListUpdateContactDto) {
        return this.contactService.updateListContact(contact);
    }

    @DefPost('delete')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.UPDATE])
    async deleteContact(@Body() contact: DetailContactDto) {
        return this.contactService.deleteContact(contact);
    }

    @DefPost('delete-list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CONTACT.UPDATE])
    async deleteListContact(@Body() contact: DeleteListContactDto) {
        return this.contactService.deleteListContact(contact);
    }
}
