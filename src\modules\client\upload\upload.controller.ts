import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { UploadService } from './upload.service';
import { UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';

@DefController('s3')
@UseGuards(ClientAuthGuard)
export class UploadController {
    constructor(private uploadService: UploadService) {}

    @DefPost('upload-single')
    @UseInterceptors(FileInterceptor('file'))
    async uploadSingle(@UploadedFile() file: Express.Multer.File) {
        return this.uploadService.uploadSingle(file);
    }
}
