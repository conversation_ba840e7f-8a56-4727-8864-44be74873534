import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSComplaint, NSNotification } from '~/common/enums';
import { NSAlert } from '~/common/enums/alert.enum';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { NSContactCare } from '~/common/enums/contact-care.enum';
import { NSContract } from '~/common/enums/contract.enum';
import { NSQuote } from '~/common/enums/quote.enum';
import { NSSettingString } from '~/common/enums/setting-string.enum';
import { AlertRepo } from '~/domains/primary/alert/alert.repo';
import { ComplaintRepo } from '~/domains/primary/complaint/complaint.repo';
import { ContactCareRepo } from '~/domains/primary/contact-care/contact-care.repo';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { CampaignRepo } from '~/domains/primary/marketing-campaign/campaign.repo';
import { QuoteRepo } from '~/domains/primary/quote/quote.repo';
import { NotificationService } from '../notification/notification.service';
import { SettingStringService } from '../setting-string/setting-string.service';

@Injectable()
export class AlertCronService {
    private readonly logger = new Logger(AlertCronService.name);

    constructor(
        @InjectRepo(AlertRepo) private alertRepo: AlertRepo,
        @InjectRepo(ComplaintRepo) private complaintRepo: ComplaintRepo,
        @InjectRepo(ContractRepo) private contractRepo: ContractRepo,
        @InjectRepo(QuoteRepo) private quoteRepo: QuoteRepo,
        @InjectRepo(ContactCareRepo) private contactCareRepo: ContactCareRepo,
        @InjectRepo(CampaignRepo) private campaignRepo: CampaignRepo,
        private readonly settingStringService: SettingStringService,
        private readonly notificationService: NotificationService,
    ) {}

    /**
     * Chạy mỗi ngày lúc 00:00 để tạo alerts cho khiếu nại
     */
    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async generateAlerts() {
        this.logger.log('Starting complaint alert generation...');

        try {
            this.logger.log(`Start processing all complaints`);

            await this.generateComplaintAlerts();
            await this.generateContractAlerts();
            await this.generateQuoteAlerts();
            await this.generateContactCareAlerts();
            await this.generateCampaignAlerts();

            this.logger.log('Complaint alert generation RESOLVED');
        } catch (error) {
            this.logger.error(`Error generating complaint alerts: ${error.message}`, error.stack);
            // Re-throw để caller có thể biết có lỗi
            throw error;
        }
    }
    private async criticalLevel() {
        return await this.settingStringService.getDefaultValueByKey(
            'string',
            NSSettingString.EDefaultConfigKey.WARNING_LEVEL_CRITICAL,
        );
    }
    private async highLevel() {
        return await this.settingStringService.getDefaultValueByKey(
            'string',
            NSSettingString.EDefaultConfigKey.WARNING_LEVEL_HIGH,
        );
    }
    private async mediumLevel() {
        return await this.settingStringService.getDefaultValueByKey(
            'string',
            NSSettingString.EDefaultConfigKey.WARNING_LEVEL_MEDIUM,
        );
    }

    private async getSeverityByDaysUntil(daysUntil: number): Promise<NSAlert.ESeverity> {
        if (daysUntil <= 0) return NSAlert.ESeverity.CRITICAL;
        return daysUntil <= Number(await this.criticalLevel())
            ? NSAlert.ESeverity.CRITICAL
            : daysUntil <= Number(await this.highLevel())
              ? NSAlert.ESeverity.HIGH
              : daysUntil <= Number(await this.mediumLevel())
                ? NSAlert.ESeverity.MEDIUM
                : NSAlert.ESeverity.LOW;
    }

    private calculateDaysUntil(dueDate: Date | null, today: Date): number | null {
        if (!dueDate) return null;
        const dueDateDate = new Date(dueDate);
        return Math.floor((dueDateDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    }

    @DefTransaction()
    public async generateAlertForComplaint(documentId: string) {
        try {
            this.logger.log(`Start processing complaint: ${documentId}`);

            const complaint = await this.complaintRepo.findOne({
                where: { id: documentId },
            });

            if (!complaint) {
                this.logger.warn(`Complaint ${documentId} not found`);
                return;
            }

            // Xử lý RESOLVED/CANCELLED - xóa alert nếu có
            if (
                complaint.status === NSComplaint.EComplaintStatus.RESOLVED ||
                complaint.status === NSComplaint.EComplaintStatus.CANCELLED
            ) {
                await this.alertRepo.delete({
                    documentId: complaint.id,
                });
                this.logger.log(`Deleted alert for resolved/cancelled complaint: ${documentId}`);
                return;
            }

            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const daysUntil = this.calculateDaysUntil(complaint.dueDate, today);
            const alertDetails = await this.determineComplaintAlertDetails(
                complaint.status,
                daysUntil,
                complaint,
            );

            if (!alertDetails) {
                this.logger.log(`No alert needed for complaint: ${documentId}`);
                return;
            }

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                complaintCode: complaint.code,
                complaintType: complaint.type,
                createdDate: complaint.createdDate,
                complaintPriority: complaint.priority,
                handlerEmployeeId: complaint.handlerEmployeeId,
                followerEmployeeId: complaint.followerEmployeeId,
                status: complaint.status,
                dueDate: complaint.dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/customer-care/complaint',
                    id: complaint.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: { tenantId: complaint.tenantId, documentId: complaint.id },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = complaint.dueDate;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                await this.alertRepo.save(existingAlert);
                this.logger.log(`Updated alert for complaint: ${documentId}`);
            } else {
                await this.alertRepo.save({
                    tenantId: complaint.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: complaint.id,
                    customerId: complaint.customerId,
                    source: NSAlert.ESource.COMPLAINT_ALERT,
                    dueDate: complaint.dueDate,
                    isRead: false,
                    metadata,
                });
                this.logger.log(`Created new alert for complaint: ${documentId}`);
            }

            this.logger.log(`Complaint alert processing completed for: ${documentId}`);
        } catch (error) {
            this.logger.error(
                `Error generating alert for complaint ${documentId}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    @DefTransaction()
    public async generateAlertForQuote(quoteId: string) {
        try {
            this.logger.log(`Start processing quote: ${quoteId}`);

            const quote = await this.quoteRepo.findOne({
                where: { id: quoteId },
            });

            if (!quote) {
                this.logger.warn(`Quote ${quoteId} not found`);
                return;
            }

            if (
                quote.status === NSQuote.EStatus.CONFIRMED ||
                quote.status === NSQuote.EStatus.REJECTED ||
                quote.status === NSQuote.EStatus.CANCELLED
            ) {
                await this.alertRepo.delete({
                    documentId: quote.id,
                    source: NSAlert.ESource.QUOTE_ALERT,
                } as any);
                this.logger.log(`Deleted alert for finalized quote: ${quoteId}`);
                return;
            }

            if (!quote.validityDays || !quote.createdDate) {
                this.logger.log(`Quote ${quoteId} missing validityDays/createdDate`);
                return;
            }

            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const dueDate = new Date(quote.createdDate);
            dueDate.setHours(0, 0, 0, 0);
            dueDate.setDate(dueDate.getDate() + Number(quote.validityDays));

            const daysUntil = this.calculateDaysUntil(dueDate as any, today);
            const alertDetails = await this.determineQuoteAlertDetails(daysUntil, quote);

            if (!alertDetails) {
                this.logger.log(`No alert needed for quote: ${quoteId}`);
                return;
            }

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                quotationNumber: (quote as any).quotationNumber,
                customerId: quote.customerId,
                customerName: (quote as any).customerName,
                status: quote.status,
                validityDays: quote.validityDays,
                createdDate: quote.createdDate,
                dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: quote.tenantId,
                    documentId: quote.id,
                    source: NSAlert.ESource.QUOTE_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                await this.alertRepo.save(existingAlert);
                this.logger.log(`Updated alert for quote: ${quoteId}`);
            } else {
                await this.alertRepo.save({
                    tenantId: quote.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: quote.id,
                    customerId: quote.customerId,
                    source: NSAlert.ESource.QUOTE_ALERT,
                    dueDate: dueDate as any,
                    isRead: false,
                    metadata,
                });
                this.logger.log(`Created new alert for quote: ${quoteId}`);
            }

            this.logger.log(`Quote alert processing completed for: ${quoteId}`);
        } catch (error) {
            this.logger.error(
                `Error generating alert for quote ${quoteId}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    @DefTransaction()
    public async generateAlertForContactCare(contactCareId: string) {
        try {
            this.logger.log(`Start processing contact-care: ${contactCareId}`);

            const cc = await this.contactCareRepo.findOne({ where: { id: contactCareId } });
            if (!cc) {
                this.logger.warn(`Contact-care ${contactCareId} not found`);
                return;
            }

            if (
                cc.status === NSContactCare.EStatus.REJECTED ||
                cc.status === NSContactCare.EStatus.APPROVED
            ) {
                await this.alertRepo.delete({
                    documentId: cc.id,
                    source: NSAlert.ESource.CONTACT_CARE_ALERT,
                } as any);
                this.logger.log(
                    `Deleted alert for approved/rejected contact-care: ${contactCareId}`,
                );
                return;
            }

            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const daysUntil = this.calculateDaysUntil(cc.dueDate as any, today);
            const alertDetails = await this.determineContactCareAlertDetails(daysUntil, cc);

            if (!alertDetails) {
                this.logger.log(`No alert needed for contact-care: ${contactCareId}`);
                return;
            }

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                code: cc.code,
                title: cc.title,
                type: cc.type,
                customerId: cc.customerId,
                supervisorMemberId: cc.supervisorMemberId,
                assignedMemberIds: cc.assignedMemberIds,
                status: cc.status,
                dueDate: cc.dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: cc.tenantId,
                    documentId: cc.id,
                    source: NSAlert.ESource.CONTACT_CARE_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = cc.dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                await this.alertRepo.save(existingAlert);
                this.logger.log(`Updated alert for contact-care: ${contactCareId}`);
            } else {
                await this.alertRepo.save({
                    tenantId: cc.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: cc.id,
                    customerId: cc.customerId,
                    source: NSAlert.ESource.CONTACT_CARE_ALERT,
                    dueDate: cc.dueDate as any,
                    isRead: false,
                    metadata,
                });
                this.logger.log(`Created new alert for contact-care: ${contactCareId}`);
            }

            this.logger.log(`Contact-care alert processing completed for: ${contactCareId}`);
        } catch (error) {
            this.logger.error(
                `Error generating alert for contact-care ${contactCareId}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    @DefTransaction()
    public async generateAlertForContract(contractId: string) {
        try {
            this.logger.log(`Start processing contract: ${contractId}`);

            const contract = await this.contractRepo.findOne({
                where: { id: contractId },
            });

            if (!contract) {
                this.logger.warn(`Contract ${contractId} not found`);
                return;
            }

            if (
                contract.status === NSContract.EStatus.REJECTED ||
                contract.status === NSContract.EStatus.CANCELLED
            ) {
                await this.alertRepo.delete({
                    documentId: contract.id,
                    source: NSAlert.ESource.CONTRACT_ALERT,
                } as any);
                this.logger.log(`Deleted alert for finalized contract: ${contractId}`);
                return;
            }

            if (!contract.dueDate || !contract.createdDate) {
                this.logger.log(`Contract ${contractId} missing validityDays/createdDate`);
                return;
            }

            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const dueDate = new Date(contract.createdDate);
            dueDate.setHours(0, 0, 0, 0);
            dueDate.setDate(dueDate.getDate());

            const daysUntil = this.calculateDaysUntil(dueDate as any, today);
            const alertDetails = await this.determineContractAlertDetails(daysUntil, contract);

            if (!alertDetails) {
                this.logger.log(`No alert needed for contract: ${contractId}`);
                return;
            }

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                contractNumber: (contract as any).contractNumber,
                customerId: contract.customerId,
                customerName: (contract as any).customerName,
                status: contract.status,
                createdDate: contract.createdDate,
                dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: contract.tenantId,
                    documentId: contract.id,
                    source: NSAlert.ESource.CONTRACT_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                await this.alertRepo.save(existingAlert);
                this.logger.log(`Updated alert for contract: ${contractId}`);
            } else {
                await this.alertRepo.save({
                    tenantId: contract.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: contract.id,
                    customerId: contract.customerId,
                    source: NSAlert.ESource.CONTRACT_ALERT,
                    dueDate: dueDate as any,
                    isRead: false,
                    metadata,
                });
                this.logger.log(`Created new alert for contract: ${contractId}`);
            }

            this.logger.log(`Contract alert processing completed for: ${contractId}`);
        } catch (error) {
            this.logger.error(
                `Error generating alert for contract ${contractId}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    @DefTransaction()
    private async generateComplaintAlerts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const allComplaints = await this.complaintRepo.find();

        if (allComplaints.length === 0) {
            this.logger.log('No complaints to process');
            return;
        }

        // Xóa alert cho các khiếu nại RESOLVED hoặc CANCELLED
        const resolvedOrCancelledComplaints = allComplaints.filter(
            complaint =>
                complaint.status === NSComplaint.EComplaintStatus.RESOLVED ||
                complaint.status === NSComplaint.EComplaintStatus.CANCELLED,
        );
        if (resolvedOrCancelledComplaints.length > 0) {
            await this.alertRepo.delete({
                documentId: In(resolvedOrCancelledComplaints.map(c => c.id)),
            });
            this.logger.log(
                `Deleted ${resolvedOrCancelledComplaints.length} resolved/cancelled alerts`,
            );
        }

        // Xử lý các khiếu nại còn lại
        const alertsToCreate: any[] = [];
        const alertsToUpdate: any[] = [];

        for (const complaint of allComplaints) {
            if (
                complaint.status === NSComplaint.EComplaintStatus.RESOLVED ||
                complaint.status === NSComplaint.EComplaintStatus.CANCELLED
            ) {
                continue;
            }

            const daysUntil = this.calculateDaysUntil(complaint.dueDate, today);
            const alertDetails = await this.determineComplaintAlertDetails(
                complaint.status,
                daysUntil,
                complaint,
            );

            if (!alertDetails) continue;

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                complaintCode: complaint.code,
                complaintType: complaint.type,
                createdDate: complaint.createdDate,
                complaintPriority: complaint.priority,
                handlerEmployeeId: complaint.handlerEmployeeId,
                followerEmployeeId: complaint.followerEmployeeId,
                status: complaint.status,
                dueDate: complaint.dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/customer-care/complaint',
                    id: complaint.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: { tenantId: complaint.tenantId, documentId: complaint.id },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = complaint.dueDate;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                alertsToUpdate.push(existingAlert);
            } else {
                alertsToCreate.push({
                    tenantId: complaint.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: complaint.id,
                    customerId: complaint.customerId,
                    source: NSAlert.ESource.COMPLAINT_ALERT,
                    dueDate: complaint.dueDate,
                    isRead: false,
                    metadata,
                });
            }
        }

        // Lưu bulk tạo mới và cập nhật
        if (alertsToCreate.length > 0) {
            await this.alertRepo.save(alertsToCreate as any);
            this.logger.log(`Created ${alertsToCreate.length} new alerts`);
        }

        if (alertsToUpdate.length > 0) {
            await this.alertRepo.save(alertsToUpdate as any);
            this.logger.log(`Updated ${alertsToUpdate.length} existing alerts`);
        }
    }
    @DefTransaction()
    private async generateContractAlerts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const allContracts = await this.contractRepo.find();

        if (allContracts.length === 0) {
            this.logger.log('No contracts to process');
            return;
        }

        // Xoá cảnh báo cho hợp đồng đã ký hoặc bị từ chối
        const signedOrRejected = allContracts.filter(
            c => c.status === NSContract.EStatus.SIGNED || c.status === NSContract.EStatus.REJECTED,
        );
        if (signedOrRejected.length > 0) {
            await this.alertRepo.delete({
                documentId: In(signedOrRejected.map(c => c.id)),
                source: NSAlert.ESource.CONTRACT_ALERT,
            } as any);
            this.logger.log(`Deleted ${signedOrRejected.length} signed/rejected contract alerts`);
        }

        const alertsToCreate: any[] = [];
        const alertsToUpdate: any[] = [];

        for (const contract of allContracts) {
            if (
                contract.status === NSContract.EStatus.SIGNED ||
                contract.status === NSContract.EStatus.REJECTED
            ) {
                // Không cảnh báo cho hợp đồng đã ký hoặc bị từ chối
                continue;
            }

            const daysUntil = this.calculateDaysUntil(contract.dueDate as any, today);
            const alertDetails = await this.determineContractAlertDetails(daysUntil, contract);

            if (!alertDetails) continue;

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                contractCode: contract.code,
                contractType: contract.type,
                signingDate: contract.signingDate,
                status: contract.status,
                dueDate: contract.dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/sales-management/contract',
                    id: contract.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: { tenantId: contract.tenantId, documentId: contract.id },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = contract.dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                alertsToUpdate.push(existingAlert);
            } else {
                alertsToCreate.push({
                    tenantId: contract.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: contract.id, // Tạm dùng documentId để liên kết
                    customerId: contract.customerId,
                    source: NSAlert.ESource.CONTRACT_ALERT,
                    dueDate: contract.dueDate as any,
                    isRead: false,
                    metadata,
                });
            }
        }

        if (alertsToCreate.length > 0) {
            await this.alertRepo.save(alertsToCreate as any);
            this.logger.log(`Created ${alertsToCreate.length} new contract alerts`);
        }

        if (alertsToUpdate.length > 0) {
            await this.alertRepo.save(alertsToUpdate as any);
            this.logger.log(`Updated ${alertsToUpdate.length} existing contract alerts`);
        }
    }
    @DefTransaction()
    private async generateQuoteAlerts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const allQuotes = await this.quoteRepo.find();

        if (allQuotes.length === 0) {
            this.logger.log('No quotes to process');
            return;
        }

        const finalizedQuotes = allQuotes.filter(
            q =>
                q.status === NSQuote.EStatus.REJECTED ||
                q.status === NSQuote.EStatus.CANCELLED ||
                q.status === NSQuote.EStatus.CONFIRMED,
        );
        if (finalizedQuotes.length > 0) {
            await this.alertRepo.delete({
                documentId: In(finalizedQuotes.map(q => q.id)),
                source: NSAlert.ESource.QUOTE_ALERT,
            } as any);
            this.logger.log(
                `Deleted ${finalizedQuotes.length} confirmed/rejected/cancelled quote alerts`,
            );
        }

        const alertsToCreate: any[] = [];
        const alertsToUpdate: any[] = [];

        for (const quote of allQuotes) {
            if (
                quote.status === NSQuote.EStatus.CONFIRMED ||
                quote.status === NSQuote.EStatus.REJECTED ||
                quote.status === NSQuote.EStatus.CANCELLED
            ) {
                continue;
            }

            if (!quote.validityDays || !quote.createdDate) {
                continue;
            }

            const dueDate = new Date(quote.createdDate);
            dueDate.setHours(0, 0, 0, 0);
            dueDate.setDate(dueDate.getDate() + Number(quote.validityDays));

            const daysUntil = this.calculateDaysUntil(dueDate as any, today);
            const alertDetails = await this.determineQuoteAlertDetails(daysUntil, quote);

            if (!alertDetails) continue;

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                quotationNumber: (quote as any).quotationNumber,
                customerId: quote.customerId,
                customerName: (quote as any).customerName,
                status: quote.status,
                validityDays: quote.validityDays,
                createdDate: quote.createdDate,
                dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/sales-management/quotation',
                    id: quote.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: quote.tenantId,
                    documentId: quote.id,
                    source: NSAlert.ESource.QUOTE_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                alertsToUpdate.push(existingAlert);
            } else {
                alertsToCreate.push({
                    tenantId: quote.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: quote.id,
                    customerId: quote.customerId,
                    source: NSAlert.ESource.QUOTE_ALERT,
                    dueDate: dueDate as any,
                    isRead: false,
                    metadata,
                });
            }
        }

        if (alertsToCreate.length > 0) {
            await this.alertRepo.save(alertsToCreate as any);
            this.logger.log(`Created ${alertsToCreate.length} new quote alerts`);
        }

        if (alertsToUpdate.length > 0) {
            await this.alertRepo.save(alertsToUpdate as any);
            this.logger.log(`Updated ${alertsToUpdate.length} existing quote alerts`);
        }
    }
    @DefTransaction()
    private async generateContactCareAlerts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const allContacts = await this.contactCareRepo.find();

        if (allContacts.length === 0) {
            this.logger.log('No contact-care to process');
            return;
        }

        const approvedOrRejected = allContacts.filter(
            c =>
                c.status === NSContactCare.EStatus.APPROVED ||
                c.status === NSContactCare.EStatus.REJECTED,
        );
        if (approvedOrRejected.length > 0) {
            await this.alertRepo.delete({
                documentId: In(approvedOrRejected.map(c => c.id)),
                source: NSAlert.ESource.CONTACT_CARE_ALERT,
            } as any);
            this.logger.log(
                `Deleted ${approvedOrRejected.length} approved/rejected contact-care alerts`,
            );
        }

        const alertsToCreate: any[] = [];
        const alertsToUpdate: any[] = [];

        for (const cc of allContacts) {
            if (
                cc.status === NSContactCare.EStatus.REJECTED ||
                cc.status === NSContactCare.EStatus.APPROVED
            )
                continue;

            const daysUntil = this.calculateDaysUntil(cc.dueDate as any, today);
            const alertDetails = await this.determineContactCareAlertDetails(daysUntil, cc);
            if (!alertDetails) continue;

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                code: cc.code,
                title: cc.title,
                type: cc.type,
                customerId: cc.customerId,
                supervisorMemberId: cc.supervisorMemberId,
                assignedMemberIds: cc.assignedMemberIds,
                status: cc.status,
                dueDate: cc.dueDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/customer-care/contact',
                    id: cc.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: cc.tenantId,
                    documentId: cc.id,
                    source: NSAlert.ESource.CONTACT_CARE_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = cc.dueDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                alertsToUpdate.push(existingAlert);
            } else {
                alertsToCreate.push({
                    tenantId: cc.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: cc.id,
                    customerId: cc.customerId,
                    source: NSAlert.ESource.CONTACT_CARE_ALERT,
                    dueDate: cc.dueDate as any,
                    isRead: false,
                    metadata,
                });
            }
        }

        if (alertsToCreate.length > 0) {
            await this.alertRepo.save(alertsToCreate as any);
            this.logger.log(`Created ${alertsToCreate.length} new contact-care alerts`);
        }

        if (alertsToUpdate.length > 0) {
            await this.alertRepo.save(alertsToUpdate as any);
            this.logger.log(`Updated ${alertsToUpdate.length} existing contact-care alerts`);
        }
    }
    @DefTransaction()
    private async generateCampaignAlerts() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const allCampaigns = await this.campaignRepo.find();

        if (allCampaigns.length === 0) {
            this.logger.log('No campaign to process');
            return;
        }

        const approvedOrRejected = allCampaigns.filter(
            c =>
                c.status === NSCampaign.EStatus.APPROVED ||
                c.status === NSCampaign.EStatus.REJECTED ||
                c.status === NSCampaign.EStatus.CANCELLED ||
                c.status === NSCampaign.EStatus.RESOLVED,
        );
        if (approvedOrRejected.length > 0) {
            await this.alertRepo.delete({
                documentId: In(approvedOrRejected.map(c => c.id)),
                source: NSAlert.ESource.CAMPAIGN_ALERT,
            } as any);
            this.logger.log(
                `Deleted ${approvedOrRejected.length} approved/rejected campaign alerts`,
            );
        }

        const alertsToCreate: any[] = [];
        const alertsToUpdate: any[] = [];

        for (const c of allCampaigns) {
            if (
                c.status === NSCampaign.EStatus.REJECTED ||
                c.status === NSCampaign.EStatus.APPROVED ||
                c.status === NSCampaign.EStatus.CANCELLED ||
                c.status === NSCampaign.EStatus.RESOLVED
            )
                continue;

            const daysUntil = this.calculateDaysUntil(c.sendDate as any, today);
            const alertDetails = await this.determineCampaignAlertDetails(daysUntil, c);
            if (!alertDetails) continue;

            const { alertType, title, description, severity } = alertDetails;
            const metadata = {
                code: c.code,
                title: c.name,
                customerId: c.customerIds.map(customerId => customerId),
                status: c.status,
                sendDate: c.sendDate,
                daysUntil: daysUntil !== null ? Math.abs(daysUntil) : null,
                link: {
                    path: '/marketing-campaign/list',
                    id: c.id,
                },
            };

            const existingAlert = await this.alertRepo.findOne({
                where: {
                    tenantId: c.tenantId,
                    documentId: c.id,
                    source: NSAlert.ESource.CAMPAIGN_ALERT,
                },
            });

            if (existingAlert) {
                existingAlert.type = alertType;
                existingAlert.severity = severity;
                existingAlert.title = title;
                existingAlert.description = description;
                existingAlert.dueDate = c.sendDate as any;
                existingAlert.metadata = metadata;
                existingAlert.isRead = false;
                alertsToUpdate.push(existingAlert);
            } else {
                alertsToCreate.push({
                    tenantId: c.tenantId,
                    type: alertType,
                    severity,
                    title,
                    description,
                    documentId: c.id,
                    source: NSAlert.ESource.CAMPAIGN_ALERT,
                    dueDate: c.sendDate as any,
                    isRead: false,
                    metadata,
                });
            }
        }

        if (alertsToCreate.length > 0) {
            await this.alertRepo.save(alertsToCreate as any);
            this.logger.log(`Created ${alertsToCreate.length} new contact-care alerts`);
        }

        if (alertsToUpdate.length > 0) {
            await this.alertRepo.save(alertsToUpdate as any);
            this.logger.log(`Updated ${alertsToUpdate.length} existing contact-care alerts`);
        }
    }
    @DefTransaction()
    private async determineContractAlertDetails(daysUntil: number | null, contract: any) {
        if (!contract?.dueDate) return null;

        let alertType: NSAlert.EType;
        let title = '';
        let description = '';
        let severity: NSAlert.ESeverity = NSAlert.ESeverity.LOW;

        switch (contract.status) {
            case NSContract.EStatus.SIGNED:
            case NSContract.EStatus.REJECTED:
            case NSContract.EStatus.CANCELLED:
                return null;

            case NSContract.EStatus.NEW: {
                const isOverdue = daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.CONTRACT_OVERDUE;
                    title = `Hợp đồng ${contract.name || contract.code} đã quá hạn ${daysOverdue} ngày`;
                    description = `Hợp đồng quá hạn xử lý ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTRACT,
                            type: NSNotification.EType.CONTRACT_OVERDUE,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Hợp đồng ${contract.name || contract.code} đã quá hạn ${daysOverdue} ngày`,
                            content: `Hãy kiểm tra và xử lý hợp đồng này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [contract?.handlerEmployeeId, contract?.followerEmployeeId],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: contract.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/contract',
                            linkId: contract.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.CONTRACT_NEAR_DEADLINE;
                    title = `Hợp đồng ${contract.name || contract.code} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Hợp đồng sắp hết hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTRACT,
                            type: NSNotification.EType.CONTRACT_PROCESS,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Hợp đồng ${contract.name || contract.code} sắp hết hạn trong ${daysUntil} ngày`,
                            content: `Hãy kiểm tra và xử lý hợp đồng này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [contract?.handlerEmployeeId, contract?.followerEmployeeId],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: contract.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/contract',
                            linkId: contract.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                break;
            }

            case NSContract.EStatus.SENT:
            case NSContract.EStatus.SENT_TO_CUSTOMER:
            case NSContract.EStatus.APPROVED: {
                const isOverdue = daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.CONTRACT_OVERDUE;
                    title = `Hợp đồng ${contract.name || contract.code} đã quá hạn ${daysOverdue} ngày`;
                    description = `Hợp đồng quá hạn xử lý ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTRACT,
                            type: NSNotification.EType.CONTRACT_OVERDUE,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Hợp đồng ${contract.name || contract.code} đã quá hạn ${daysOverdue} ngày`,
                            content: `Hãy kiểm tra và xử lý hợp đồng này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [contract?.handlerEmployeeId, contract?.followerEmployeeId],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: contract.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/contract',
                            linkId: contract.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.CONTRACT_NEAR_DEADLINE;
                    title = `Hợp đồng ${contract.name || contract.code} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Hợp đồng sắp hết hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTRACT,
                            type: NSNotification.EType.CONTRACT_PROCESS,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Hợp đồng ${contract.name || contract.code} sắp hết hạn trong ${daysUntil} ngày`,
                            content: `Hãy kiểm tra và xử lý hợp đồng này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [contract?.handlerEmployeeId, contract?.followerEmployeeId],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: contract.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/contract',
                            linkId: contract.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil > Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.CONTRACT_FOLLOWUP;
                    title = `Hợp đồng ${contract.name || contract.code} đang chờ theo dõi`;
                    description = `Hợp đồng đang xử lý còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    return { alertType, title, description, severity };
                }

                break;
            }

            default:
                return null;
        }
    }
    @DefTransaction()
    private async determineQuoteAlertDetails(daysUntil: number | null, quote: any) {
        if (!quote?.validityDays || !quote?.createdDate) return null;

        let alertType: NSAlert.EType;
        let title = '';
        let description = '';
        let severity: NSAlert.ESeverity = NSAlert.ESeverity.LOW;

        switch (quote.status as NSQuote.EStatus) {
            case NSQuote.EStatus.CONFIRMED:
            case NSQuote.EStatus.REJECTED:
            case NSQuote.EStatus.CANCELLED:
            case NSQuote.EStatus.SENT_TO_CUSTOMER:
                return null;

            case NSQuote.EStatus.NEW:
            case NSQuote.EStatus.APPROVED:
            case NSQuote.EStatus.SENT: {
                const isOverdue = daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.QUOTE_OVERDUE;
                    title = `Báo giá ${quote.quotationNumber || quote.id} đã quá hạn ${daysOverdue} ngày`;
                    description = `Báo giá quá hạn ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.QUOTATION,
                            type: NSNotification.EType.QUOTATION_OVERDUE,
                            severity: NSNotification.ESeverity.LOW,
                            title,
                            content: `Hãy kiểm tra và xử lý báo giá này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [quote?.createdBy],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: quote.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/quotation',
                            linkId: quote.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.QUOTE_NEAR_DEADLINE;
                    title = `Báo giá ${quote.quotationNumber || quote.id} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Báo giá sắp hết hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.QUOTATION,
                            type: NSNotification.EType.QUOTATION_NEW,
                            severity: NSNotification.ESeverity.LOW,
                            title,
                            content: `Hãy theo dõi báo giá sắp hết hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [quote?.createdBy],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: quote.tenantId,
                            createdBy: null,
                            linkPath: '/sales-management/quotation',
                            linkId: quote.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil > Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.QUOTE_FOLLOWUP;
                    title = `Báo giá ${quote.quotationNumber || quote.id} đang chờ theo dõi`;
                    description = `Báo giá đang xử lý còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    return { alertType, title, description, severity };
                }

                break;
            }

            default:
                return null;
        }
    }
    @DefTransaction()
    private async determineContactCareAlertDetails(daysUntil: number | null, cc: any) {
        let alertType: NSAlert.EType;
        let title = '';
        let description = '';
        let severity: NSAlert.ESeverity = NSAlert.ESeverity.LOW;
        const memberIds = [cc?.createdBy, cc.supervisorMemberId];
        cc.assignedMemberIds?.forEach(assignedMemberId => {
            memberIds.push(assignedMemberId);
        });
        switch (cc.status as NSContactCare.EStatus) {
            case NSContactCare.EStatus.CANCELLED:
            case NSContactCare.EStatus.REJECTED:
            case NSContactCare.EStatus.RESOLVED:
            case NSContactCare.EStatus.HOLD:
                return null;

            case NSContactCare.EStatus.NEW:
            case NSContactCare.EStatus.SENT:
            case NSContactCare.EStatus.APPROVED: {
                const hasDueDate = !!cc.dueDate;
                const isOverdue = hasDueDate && daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.CONTACT_CARE_OVERDUE;
                    title = `Thăm hỏi ${cc.title || cc.code} đã quá hạn ${daysOverdue} ngày`;
                    description = `Thăm hỏi quá hạn ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);

                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_OVERDUE,
                            severity: NSNotification.ESeverity.CRITICAL,
                            title,
                            content: `Hãy theo dõi thăm hỏi quá hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds,
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    hasDueDate &&
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    alertType = NSAlert.EType.CONTACT_CARE_NEAR_DEADLINE;
                    title = `Thăm hỏi ${cc.title || cc.code} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Thăm hỏi sắp đến hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_NEW,
                            severity: NSNotification.ESeverity.HIGH,
                            title,
                            content: `Hãy theo dõi thăm hỏi sắp hết hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds,
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    hasDueDate &&
                    daysUntil !== null &&
                    daysUntil > Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.CONTACT_CARE_FOLLOWUP;
                    title = `Thăm hỏi ${cc.title || cc.code} cần theo dõi`;
                    description = `Thăm hỏi được lên kế hoạch còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_NEW,
                            severity: NSNotification.ESeverity.MEDIUM,
                            title,
                            content: `Hãy theo dõi thăm hỏi này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds,
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }
                break;
            }

            case NSContactCare.EStatus.IN_PROGRESS: {
                const hasDueDate = !!cc.dueDate;
                const isOverdue = hasDueDate && daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.CONTACT_CARE_OVERDUE;
                    title = `Thăm hỏi ${cc.title || cc.code} đã quá hạn ${daysOverdue} ngày`;
                    description = `Thăm hỏi quá hạn ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_PROCESS,
                            severity: NSNotification.ESeverity.CRITICAL,
                            title,
                            content: `Hãy theo dõi thăm hỏi quá hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds,
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    hasDueDate &&
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.CONTACT_CARE_NEAR_DEADLINE;
                    title = `Thăm hỏi ${cc.title || cc.code} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Thăm hỏi sắp đến hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_PROCESS,
                            severity: NSNotification.ESeverity.HIGH,
                            title,
                            content: `Hãy theo dõi thăm hỏi sắp hết hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                cc?.createdBy,
                                cc.assignedEmployeeIds?.map((id: string) => id),
                                cc.supervisorMemberId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    hasDueDate &&
                    daysUntil !== null &&
                    daysUntil > Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.mediumLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.CONTACT_CARE_FOLLOWUP;
                    title = `Thăm hỏi ${cc.title || cc.code} cần theo dõi`;
                    description = `Thăm hỏi được lên kế hoạch còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CONTACT_CARE,
                            type: NSNotification.EType.CONTACT_CARE_PROCESS,
                            severity: NSNotification.ESeverity.MEDIUM,
                            title,
                            content: `Hãy theo dõi thăm hỏi này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                cc?.createdBy,
                                cc.assignedEmployeeIds?.map((id: string) => id),
                                cc.supervisorMemberId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: cc.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/contact',
                            linkId: cc.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                break;
            }
        }
    }
    @DefTransaction()
    private async determineComplaintAlertDetails(
        status: NSComplaint.EComplaintStatus,
        daysUntil: number | null,
        complaint: any,
    ) {
        let alertType: NSAlert.EType | null = null;
        let title: string = '';
        let description: string = '';
        let severity: NSAlert.ESeverity = NSAlert.ESeverity.LOW;

        switch (status) {
            case NSComplaint.EComplaintStatus.RESOLVED:
            case NSComplaint.EComplaintStatus.CANCELLED:
            case NSComplaint.EComplaintStatus.HOLD:
            case NSComplaint.EComplaintStatus.REJECTED:
                return null;

            case NSComplaint.EComplaintStatus.NEW:
            case NSComplaint.EComplaintStatus.SENT:
            case NSComplaint.EComplaintStatus.APPROVED: {
                const isOverdue = complaint.dueDate && daysUntil !== null && daysUntil < 0;
                const overdueDays = isOverdue ? Math.abs(daysUntil) : 0;

                alertType = isOverdue
                    ? NSAlert.EType.COMPLAINT_OVERDUE
                    : NSAlert.EType.COMPLAINT_NEW;
                title = isOverdue
                    ? `Khiếu nại: ${complaint.title} quá hạn ${overdueDays} ngày`
                    : `Khiếu nại mới: ${complaint.title}`;
                description = isOverdue
                    ? `Khiếu nại quá hạn ${overdueDays} ngày`
                    : `Khiếu nại mới: ${complaint.title}`;
                severity = await this.getSeverityByDaysUntil(daysUntil!);

                if (isOverdue) {
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.COMPLAINT,
                            type: NSNotification.EType.COMPLAINT_OVERDUE,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Khiếu nại mới: ${complaint.code} đã quá hạn`,
                            content: `Hãy kiểm tra và xử lý khiếu nại này. Mô tả: ${complaint.description}`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                complaint?.createdBy,
                                complaint?.handlerEmployeeId,
                                complaint?.followerEmployeeId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: complaint.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/complaint',
                            linkId: complaint.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }
                break;
            }

            case NSComplaint.EComplaintStatus.IN_PROGRESS:
                if (complaint.dueDate && daysUntil !== null && daysUntil < 0) {
                    const daysOverdue = Math.abs(daysUntil);

                    alertType = NSAlert.EType.COMPLAINT_OVERDUE;
                    title = `Khiếu nại: ${complaint.title} đã quá hạn ${daysOverdue} ngày `;
                    description = `Khiếu nại đã quá hạn xử lý ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysOverdue);

                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.COMPLAINT,
                            type: NSNotification.EType.COMPLAINT_OVERDUE,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Khiếu nại: ${complaint.code} đã quá hạn ${daysOverdue} ngày`,
                            content: `Hãy kiểm tra và xử lý khiếu nại này. Mô tả: ${complaint.description}`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                complaint?.createdBy,
                                complaint?.handlerEmployeeId,
                                complaint?.followerEmployeeId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: complaint.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/complaint',
                            linkId: complaint.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                } else if (
                    complaint.dueDate &&
                    daysUntil !== null &&
                    daysUntil >= Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.COMPLAINT_FOLLOWUP;
                    title = `Khiếu nại: ${complaint.title} đang xử lý `;
                    description = `Khiếu nại đang xử lý còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.COMPLAINT,
                            type: NSNotification.EType.COMPLAINT_PROCESS,
                            severity: NSNotification.ESeverity.LOW,
                            title: `Khiếu nại: ${complaint.code} đang xử lý còn ${daysRemain} ngày`,
                            content: `Hãy kiểm tra và xử lý khiếu nại này. Mô tả: ${complaint.description}`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                complaint?.createdBy,
                                complaint?.handlerEmployeeId,
                                complaint?.followerEmployeeId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: complaint.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/complaint',
                            linkId: complaint.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                } else if (
                    complaint.dueDate &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.COMPLAINT_NEAR_DEADLINE;
                    title = `Khiếu nại: ${complaint.title} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Khiếu nại sắp hết hạn xử lý còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.COMPLAINT,
                            type: NSNotification.EType.COMPLAINT_PROCESS,
                            severity: NSNotification.ESeverity.CRITICAL,
                            title: `Khiếu nại: ${complaint.code} sắp đến đến hạn trong ${daysUntil} ngày`,
                            content: `Hãy kiểm tra và xử lý khiếu nại này. Mô tả: ${complaint.description}`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [
                                complaint?.createdBy,
                                complaint?.handlerEmployeeId,
                                complaint?.followerEmployeeId,
                            ],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: complaint.tenantId,
                            createdBy: null,
                            linkPath: '/customer-care/complaint',
                            linkId: complaint.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                } else {
                    alertType = NSAlert.EType.COMPLAINT_FOLLOWUP;
                    title = `Khiếu nại cần theo dõi: ${complaint.title}`;
                    description = `Khiếu nại đang xử lý cần được theo dõi`;
                    severity = NSAlert.ESeverity.LOW;
                    return { alertType, title, description, severity };
                }
            default:
                return null;
        }
    }
    @DefTransaction()
    private async determineCampaignAlertDetails(daysUntil: number | null, campaign: any) {
        if (!campaign?.sendDate) return null;

        let alertType: NSAlert.EType;
        let title = '';
        let description = '';
        let severity: NSAlert.ESeverity = NSAlert.ESeverity.LOW;

        switch (campaign.status as NSCampaign.EStatus) {
            case NSCampaign.EStatus.REJECTED:
            case NSCampaign.EStatus.RESOLVED:
            case NSCampaign.EStatus.CANCELLED:
                return null;

            case NSCampaign.EStatus.NEW:
            case NSCampaign.EStatus.SENT: {
                const isOverdue = daysUntil !== null && daysUntil < 0;
                if (isOverdue) {
                    const daysOverdue = Math.abs(daysUntil!);
                    alertType = NSAlert.EType.CAMPAIGN_OVERDUE;
                    title = `Chiến dịch Marketing: ${campaign.name} đã quá hạn ${daysOverdue} ngày`;
                    description = `Chiến dịch Marketing: ${campaign.name} quá hạn ${daysOverdue} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil!);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CAMPAIGN,
                            type: NSNotification.EType.CAMPAIGN_OVERDUE,
                            severity: NSNotification.ESeverity.CRITICAL,
                            title,
                            content: `Hãy kiểm tra và xử lý Chiến dịch Marketing này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [campaign?.createdBy],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: campaign.tenantId,
                            createdBy: null,
                            linkPath: '/marketing-campaign/list',
                            linkId: campaign.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil >= 0 &&
                    daysUntil <= Number(await this.criticalLevel())
                ) {
                    alertType = NSAlert.EType.CAMPAIGN_NEAR_DEADLINE;
                    title = `Chiến dịch Marketing: ${campaign.name} sắp hết hạn trong ${daysUntil} ngày`;
                    description = `Chiến dịch Marketing: ${campaign.name} sắp hết hạn còn ${daysUntil} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysUntil);
                    try {
                        await this.notificationService.createNotification({
                            source: NSNotification.ESource.CAMPAIGN,
                            type: NSNotification.EType.CAMPAIGN_NEW,
                            severity: NSNotification.ESeverity.HIGH,
                            title,
                            content: `Hãy theo dõi Chiến dịch Marketing sắp hết hạn này`,
                            recipientType: NSNotification.ERecipientType.MEMBER,
                            memberIds: [campaign?.createdBy],
                            sendInApp: true,
                            sendEmail: false,
                            tenantId: campaign.tenantId,
                            createdBy: null,
                            linkPath: '/marketing-campaign/list',
                            linkId: campaign.id,
                        });
                    } catch (error) {
                        throw new BusinessException(error.message || 'Lỗi khi tạo thông báo');
                    }
                    return { alertType, title, description, severity };
                }

                if (
                    daysUntil !== null &&
                    daysUntil > Number(await this.criticalLevel()) &&
                    daysUntil <= Number(await this.highLevel())
                ) {
                    const daysRemain = Math.abs(daysUntil);
                    alertType = NSAlert.EType.CAMPAIGN_FOLLOWUP;
                    title = `Chiến dịch Marketing ${campaign.name} đang chờ theo dõi`;
                    description = `Chiến dịch Marketing đang xử lý còn ${daysRemain} ngày`;
                    severity = await this.getSeverityByDaysUntil(daysRemain);
                    return { alertType, title, description, severity };
                }

                break;
            }

            default:
                return null;
        }
    }
}
