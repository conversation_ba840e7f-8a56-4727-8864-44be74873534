import { Controller, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefGet } from 'nestjs-typeorm3-kit';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { AnalyticsService } from './analytics.service';
import { AnalyticsFilterDto } from './dto/analytics.dto';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(ClientAuthGuard)
export class AnalyticsController {
    constructor(private readonly analyticsService: AnalyticsService) {}

    @DefGet('customers/type')
    async getCustomerTypeAnalytics(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getCustomerTypeAnalytics(filters);
        return {
            data,
            total: data.length,
        };
    }

    @DefGet('customers/employees')
    async getCustomerByEmployeeAnalytics(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getCustomerByEmployeeAnalytics(filters);
        return {
            data,
            total: data.length,
        };
    }

    @DefGet('customers/members/top')
    async getCustomerByMemberAnalytics(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getCustomerByEmployeeAnalytics(filters);
        return { data, total: data.length };
    }

    @DefGet('revenue/trends')
    async getRevenueTrendsAnalytics(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getRevenueTrendsAnalytics(filters);
        return { data };
    }

    @DefGet('complaints/summary')
    async getComplaintAnalytics(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getComplaintAnalytics(filters);
        return { data };
    }

    @DefGet('complaints/members')
    async getComplaintByMember(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getComplaintByMemberAnalytics(filters);
        return { data };
    }

    @DefGet('overview')
    async getDashboardOverview(@Query() filters: AnalyticsFilterDto) {
        let customerType = [] as Awaited<
            ReturnType<typeof this.analyticsService.getCustomerTypeAnalytics>
        >;
        let revenueTrends = [] as Awaited<
            ReturnType<typeof this.analyticsService.getRevenueTrendsAnalytics>
        >;
        let complaintSummary = {
            totalComplaints: 0,
            inProgressComplaints: 0,
            resolvedComplaints: 0,
            topComplaintTypes: [],
        } as Awaited<ReturnType<typeof this.analyticsService.getComplaintAnalytics>>;

        try {
            [customerType, revenueTrends, complaintSummary] = await Promise.all([
                this.analyticsService.getCustomerTypeAnalytics(filters),
                this.analyticsService.getRevenueTrendsAnalytics({ ...filters, period: 'monthly' }),
                this.analyticsService.getComplaintAnalytics(filters),
            ]);
        } catch {}

        const totalCustomers = customerType.reduce((sum, item) => sum + item.count, 0);
        const totalRevenue = revenueTrends.reduce((sum, item) => sum + item.revenue, 0);
        const totalComplaints = complaintSummary.totalComplaints || 0;
        const resolvedComplaints = complaintSummary.resolvedComplaints || 0;

        const currentMonthRevenue =
            revenueTrends.length > 0 ? revenueTrends[revenueTrends.length - 1].revenue : 0;
        const previousMonthRevenue =
            revenueTrends.length > 1 ? revenueTrends[revenueTrends.length - 2].revenue : 0;
        const revenueGrowth =
            previousMonthRevenue > 0
                ? Number(
                      (
                          ((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) *
                          100
                      ).toFixed(1),
                  )
                : 0;

        const complaintResolutionRate =
            totalComplaints > 0
                ? Number(((resolvedComplaints / totalComplaints) * 100).toFixed(1))
                : 0;

        return {
            data: {
                totalCustomers,
                totalRevenue,
                totalComplaints,
                revenueGrowth,
                customerGrowth: 8.3,
                complaintResolutionRate,
            },
        };
    }

    @DefGet('customers/sources')
    async getCustomerSources(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getCustomerSourcesAnalytics(filters);
        return { data };
    }

    @DefGet('customers/top-signed')
    async getTopCustomersBySigned(@Query() filters: AnalyticsFilterDto) {
        const data = await this.analyticsService.getTopCustomersBySignedContracts(filters);
        return { data };
    }
}
