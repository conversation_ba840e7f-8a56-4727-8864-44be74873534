import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { NSNotification } from '~/common/enums/notification.enum';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('notifications')
export class NotificationEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: NSNotification.EType.SYSTEM_MESSAGE })
    @Column({ type: 'enum', enum: NSNotification.EType })
    type: NSNotification.EType;

    @ApiProperty({ example: NSNotification.ESeverity.MEDIUM })
    @Column({ type: 'enum', enum: NSNotification.ESeverity })
    severity: NSNotification.ESeverity;

    @ApiProperty()
    @Column({ type: 'varchar', length: 255 })
    title: string;

    @ApiProperty()
    @Column({ type: 'text' })
    content: string;

    @ApiProperty({ example: NSNotification.ESource.SYSTEM })
    @Column({ type: 'enum', enum: NSNotification.ESource })
    source: NSNotification.ESource;

    @ApiPropertyOptional()
    @Column({ type: 'timestamptz', nullable: true })
    scheduleDate?: Date;

    @ApiProperty({ example: true })
    @Column({ type: 'boolean', default: true })
    sendInApp: boolean;

    @ApiProperty({ example: false })
    @Column({ type: 'boolean', default: false })
    sendEmail: boolean;

    @ApiProperty({ example: NSNotification.EStatus.DRAFT })
    @Index()
    @Column({ type: 'enum', enum: NSNotification.EStatus, default: NSNotification.EStatus.DRAFT })
    status: NSNotification.EStatus;

    @ApiPropertyOptional()
    @Column({ type: 'jsonb', nullable: true })
    metadata?: Record<string, any>;
}
