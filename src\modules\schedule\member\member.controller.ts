import { Body } from '@nestjs/common';
import { DefController, DefPost } from 'ape-nestjs-typeorm3-kit';
import { CreateMemberDto, SetActiveMemberDto, UpdateMemberDto } from './dto/member.dto';
import { MemberService } from './member.service';

@DefController('member')
export class MemberController {
    constructor(private readonly memberService: MemberService) {}

    @DefPost('create')
    async create(@Body() createMemberDto: CreateMemberDto) {
        return this.memberService.create(createMemberDto);
    }

    @DefPost('update')
    async update(@Body() updateMemberDto: UpdateMemberDto) {
        return this.memberService.update(updateMemberDto);
    }

    @DefPost('set-active')
    async setActive(@Body() setActiveMemberDto: SetActiveMemberDto) {
        return this.memberService.setActive(setActiveMemberDto);
    }
}
