import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('setting_strings')
export class SettingStringEntity extends PrimaryBaseEntity {
    @ApiProperty({ example: 'string' })
    @Column({ type: 'varchar', length: 10, nullable: true })
    type: string;

    @ApiProperty({
        example: { allocationStrategy: 'FIFO', putawayStrategy: 'FIFO' },
        description: 'Giá trị cấu hình. Sử dụng JSONB cho các cấu hình tập hợp.',
    })
    @Column({ type: 'jsonb', nullable: true }) // Sử dụng jsonb nếu hỗ trợ, nếu không dùng 'text'
    value: object;

    @ApiProperty({ example: 'DEFAULT_WMS_STRATEGIES' })
    @Column({ type: 'varchar', length: 50, nullable: true })
    key: string; // Dùng để định danh loại cấu hình (Ví dụ: 'DEFAULT_WMS_STRATEGIES')
}
