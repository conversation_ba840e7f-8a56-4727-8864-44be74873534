import { Injectable } from '@nestjs/common';
import { Between, ILike, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSContract } from '~/common/enums/contract.enum';
import { CustomerRepo, QuoteRepo } from '~/domains/primary';
import { ContractRepo } from '~/domains/primary/contract/contract.repo';
import { clientSessionContext } from '../client-session.context';
import { CustomerRevenueDto, ListCustomerRevenueDto } from './dto/customer-revenue.dto';

@Injectable()
export class CustomerRevenueService {
    constructor(
        private readonly customerRepo: CustomerRepo,
        private readonly quoteRepo: QuoteRepo,
        private readonly contractRepo: ContractRepo,
    ) {}

    async listCustomerRevenue(query: ListCustomerRevenueDto) {
        const {
            customerCode,
            customerName,
            quotationNumber,
            revenueFrom,
            revenueTo,
            createdDateFrom,
            createdDateTo,
            customerId,
        } = query;
        const { tenantId } = clientSessionContext;

        // Xây dựng điều kiện lọc cho customers
        let customerWhere: any = { tenantId };
        if (customerId) {
            customerWhere.id = customerId;
        } else {
            if (customerCode) {
                customerWhere.code = ILike(`%${customerCode}%`);
            }
            if (customerName) {
                customerWhere.name = ILike(`%${customerName}%`);
            }
        }

        // Xây dựng điều kiện lọc cho quotes
        let quoteWhere: any = { tenantId };
        if (quotationNumber) {
            quoteWhere.quotationNumber = ILike(`%${quotationNumber}%`);
        }

        if (createdDateFrom && createdDateTo) {
            quoteWhere.createdDate = Between(new Date(createdDateFrom), new Date(createdDateTo));
        } else if (createdDateFrom) {
            quoteWhere.createdDate = MoreThanOrEqual(new Date(createdDateFrom));
        } else if (createdDateTo) {
            quoteWhere.createdDate = LessThanOrEqual(new Date(createdDateTo));
        }

        try {
            const [customers, quotes, contracts] = await Promise.all([
                this.customerRepo.find({
                    where: customerWhere,
                    order: {
                        createdDate: 'DESC',
                    },
                }),
                this.quoteRepo.find({
                    where: quoteWhere,
                }),
                this.contractRepo.find({
                    where: {
                        tenantId,
                        status: NSContract.EStatus.SIGNED,
                        ...(customerId && { customerId }),
                    },
                }),
            ]);

            let result: CustomerRevenueDto[] = [];

            // Lấy danh sách hợp đồng và tính toán doanh thu
            contracts.forEach(contract => {
                const customer = customers.find(c => c.id === contract.customerId);
                if (!customer) return;

                const quote = quotes.find(q => q.id === contract.quoteId);
                const revenue = Number(quote?.totalAmount || 0);

                result.push({
                    contractCode: contract.code,
                    contractName: contract.name,
                    revenue,
                    quotationNumber: quote?.quotationNumber,
                });
            });

            // Lọc theo doanh thu
            if (revenueFrom !== undefined || revenueTo !== undefined) {
                result = result.filter(item => {
                    if (revenueFrom !== undefined && revenueTo !== undefined) {
                        return item.revenue >= revenueFrom && item.revenue <= revenueTo;
                    } else if (revenueFrom !== undefined) {
                        return item.revenue >= revenueFrom;
                    } else if (revenueTo !== undefined) {
                        return item.revenue <= revenueTo;
                    }
                    return true;
                });
            }

            return {
                data: result,
                total: result.length,
            };
        } catch (error) {
            console.error('Error generating customer revenue report:', error);
            throw new BusinessException(error.message);
        }
    }
}
