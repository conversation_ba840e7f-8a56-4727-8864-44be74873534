import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { NSApproval } from '~/common/enums/approval.enum';
import { PageRequest } from '../../../../@systems/utils';

export class CreateRequestApprovalDto {
    @ApiProperty({ example: 'title' })
    @IsString()
    @IsOptional()
    title?: string;

    @ApiProperty({ example: 'documentId' })
    @IsString()
    @IsNotEmpty()
    documentId: string;

    @ApiProperty({ example: 'documentCode' })
    @IsString()
    @IsOptional()
    documentCode?: string;

    @ApiProperty({ example: 'businessType' })
    @IsEnum(NSApproval.ApprovalBusinessType)
    @IsNotEmpty()
    businessType: NSApproval.ApprovalBusinessType;

    @ApiProperty({ example: 'approvalConfigId' })
    @IsString()
    @IsOptional()
    approvalConfigId?: string;

    @ApiProperty({ example: 'createdBy' })
    @IsString()
    @IsOptional()
    createdBy?: string;

    @ApiPropertyOptional({
        example: 'PENDING_APPROVAL',
        description: 'Trạng thái ban đầu theo nghiệp vụ',
    })
    @IsString()
    initialStatus: string;

    @ApiPropertyOptional({
        example: '["COMPLETED", "REJECTED", "CANCELLED"]',
        description: 'Danh sách trạng thái kết thúc',
    })
    finalStatuses: string[];

    @ApiPropertyOptional({
        example: 'IN_PROGRESS',
        description: 'Trạng thái khi approve hoàn toàn',
    })
    @IsString()
    approvedStatus: string;
}

export class ListApprovalRequestDto extends PageRequest {
    @ApiPropertyOptional({ example: 'title' })
    @IsString()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional({ example: NSApproval.ApprovalRequestStatus.APPROVED })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalRequestStatus)
    status?: NSApproval.ApprovalRequestStatus;

    @ApiPropertyOptional({ example: '2025-01-01' })
    @IsOptional()
    @IsString()
    createdDateFrom?: string;

    @ApiPropertyOptional({ example: '2025-01-01' })
    @IsOptional()
    @IsString()
    createdDateTo?: string;

    @ApiPropertyOptional({ example: NSApproval.ApprovalBusinessType.CONTRACT })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType?: NSApproval.ApprovalBusinessType;
}

export class ListStepApprovalRequestDto extends PageRequest {
    @ApiPropertyOptional({ example: 'title' })
    @IsString()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional({ example: NSApproval.ApprovalStepStatus.APPROVED })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalStepStatus)
    status?: NSApproval.ApprovalStepStatus;

    @ApiPropertyOptional({ example: '2025-01-01' })
    @IsOptional()
    @IsString()
    createdDateFrom?: string;

    @ApiPropertyOptional({ example: '2025-01-01' })
    @IsOptional()
    @IsString()
    createdDateTo?: string;

    @ApiPropertyOptional({ example: NSApproval.ApprovalBusinessType.CONTRACT })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalBusinessType)
    businessType?: NSApproval.ApprovalBusinessType;
}

export class ApproveRequestDto {
    @ApiProperty({ example: '5519a12e-889d-44ab-a2b0-6d1dbdca8207' })
    @IsString()
    @IsNotEmpty()
    requestId: string;

    @ApiPropertyOptional({ example: 'comment' })
    @IsString()
    @IsOptional()
    comment?: string;
}

export class CheckActorCanApproveStepDto {
    @ApiProperty({ example: '5519a12e-889d-44ab-a2b0-6d1dbdca8207' })
    @IsString()
    @IsNotEmpty()
    requestStepId: string;

    @ApiProperty({ example: '5519a12e-889d-44ab-a2b0-6d1dbdca8207' })
    @IsString()
    @IsNotEmpty()
    actorMemberId: string; // id member đang duyệt
}

export class RejectRequestDto {
    @ApiProperty({ example: '5519a12e-889d-44ab-a2b0-6d1dbdca8207' })
    @IsString()
    @IsNotEmpty()
    requestId: string;

    @ApiPropertyOptional({ example: 'comment' })
    @IsString()
    @IsOptional()
    comment?: string;
}

export class HookRequestDto {
    @ApiPropertyOptional({ example: NSApproval.ApprovalBusinessType.CONTRACT })
    @IsNotEmpty()
    businessType: NSApproval.ApprovalBusinessType;

    @ApiPropertyOptional({ example: '5519a12e-889d-44ab-a2b0-6d1dbdca8207' })
    @IsNotEmpty()
    documentId: string;

    @ApiPropertyOptional({ example: NSApproval.ApprovalRequestStatus.APPROVED })
    @IsNotEmpty()
    requestStatus: any; // APPROVED / REJECTED / CANCELLED...

    @ApiPropertyOptional({ example: 1, description: 'Thứ tự bước duyệt' })
    @IsNumber()
    @IsOptional()
    orderStep?: number;

    @ApiPropertyOptional({ example: 1, description: 'Số lượng bước duyệt' })
    @IsNumber()
    @IsOptional()
    numberStep?: number;

    @ApiPropertyOptional({ example: 'comment' })
    @IsString()
    @IsOptional()
    comment?: string;
}

export class GetOverdueStepsDto extends PageRequest {
    @ApiPropertyOptional({
        enum: NSApproval.ApprovalLeadtimeWarningSetting,
        description:
            'Loại cảnh báo: OVERDUE_ONLY (chỉ quá hạn) hoặc WARNING_AND_OVERDUE (cảnh báo và quá hạn). Nếu không truyền sẽ lấy từ setting string.',
    })
    @IsOptional()
    @IsEnum(NSApproval.ApprovalLeadtimeWarningSetting)
    warningType?: NSApproval.ApprovalLeadtimeWarningSetting;
}
