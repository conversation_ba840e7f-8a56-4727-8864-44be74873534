import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { KEY_HEADER } from '~/common/constants';

@Injectable()
export class ScheduleMiddleware implements NestMiddleware {
    constructor() {}

    async use(req: Request, res: Response, next: Function) {
        //console.log("============== INTEGRATION MIDDLEWARE ==============")
        try {
            const { headers = {} } = req;
            const { SCHEDULE_CRM_KEY } = configEnv();
            if (!headers || !headers[KEY_HEADER.SCHEDULE_CRM_KEY]) {
                throw new UnauthorizedException('Unauthorized');
            }
            const accessTokenBearer = headers[KEY_HEADER.SCHEDULE_CRM_KEY] as string;
            const accessToken = accessTokenBearer.replace('ApiKey', '').trim();
            if (!accessToken) {
                throw new UnauthorizedException('Unauthorized');
            }

            if (accessToken !== SCHEDULE_CRM_KEY) {
                throw new UnauthorizedException('Unauthorized');
            }
            next();
        } catch (error) {
            console.log(error);
            next(new UnauthorizedException('Unauthorized'));
        }
    }
}
