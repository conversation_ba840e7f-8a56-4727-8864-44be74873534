import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { PrimaryBaseEntity } from '../primary-base.entity'
import { NSApproval } from '~/common/enums/approval.enum'
import { ApprovalConfigEntity } from '../approval-config/approval-config.entity'
import { ApprovalConfigStepEntity } from './approval-config-step.entity'
import { ApprovalRequestStepEntity } from './approval-request-step.entity'
import { ApprovalRequestActionEntity } from './approval-request-action.entity'

@Entity('approval_request')
@Index(['businessType', 'documentId'])
export class ApprovalRequestEntity extends PrimaryBaseEntity {
  // Loại nghiệp vụ: PO, REQUEST_FORM, ...
  @Column({ type: 'enum', enum: NSApproval.ApprovalBusinessType })
  businessType: NSApproval.ApprovalBusinessType

  // Tiê<PERSON> đề
  @Column({ type: 'varchar', nullable: true, default: '<PERSON><PERSON>u cầu duyệt' })
  title: string

  // Id của chứng từ (PO.id / RequestForm.id)
  @Column({ type: 'uuid' })
  documentId: string

  // Mã chứng từ (PO.code / RequestForm.code)
  @Column({ type: 'varchar', nullable: true })
  documentCode: string

  // Cấu hình duyệt đang áp dụng (trùng với field trên PO/RequestForm)
  @Column({ type: 'uuid' })
  approvalConfigId: string

  // Trạng thái tổng của request (theo nghiệp vụ, ví dụ: PENDING_APPROVAL, IN_TRANSIT, etc.)
  @Column({
    type: 'varchar',
    length: 50,
    default: 'PENDING',
  })
  status: string

  // Danh sách trạng thái kết thúc - không thể approve/reject nếu status nằm trong danh sách này
  @Column({
    type: 'jsonb',
    default: '["APPROVED", "REJECTED", "CANCELLED"]',
  })
  finalStatuses: string[]

  // Trạng thái khi approve hoàn toàn (ví dụ: IN_PROGRESS, PROCESSING)
  @Column({
    type: 'varchar',
    length: 50,
    default: 'APPROVED',
  })
  approvedStatus: string

  // Đang ở step thứ mấy (order trong config)
  @Column({ type: 'int', nullable: true })
  currentStepOrder?: number

  // ConfigStep hiện tại (dùng join nhanh)
  @Column({ type: 'uuid', nullable: true })
  currentStepId?: string

  @ManyToOne(() => ApprovalConfigEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'approvalConfigId' })
  approvalConfig: ApprovalConfigEntity

  @ManyToOne(() => ApprovalConfigStepEntity, { nullable: true })
  @JoinColumn({ name: 'currentStepId' })
  currentStep?: ApprovalConfigStepEntity

  @OneToMany(() => ApprovalRequestStepEntity, (step) => step.request)
  steps: ApprovalRequestStepEntity[]

  // ==== Quan hệ runtime ====

  @OneToMany(() => ApprovalRequestActionEntity, (action) => action.request)
  actions: ApprovalRequestActionEntity[]
}
