import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import * as express from 'express';
import { Strategy, VerifyCallback } from 'passport-oauth2';
import { optionalApiConnector } from '~/connectors';
import { configEnv } from '~/@config/env';
import { GUARD_CODE } from './ape-sso-auth.guard';
import { ApeSsoUserInfo } from './dto';

const { APE_SSO_URL, APE_CLIENT_ID, APE_CLIENT_SECRET, APE_CALLBACK_URL } = configEnv();
const ENPOINTS = {
    authorizationURL: APE_SSO_URL + '/api/client/auth/authorize',
    tokenURL: APE_SSO_URL + '/api/client/auth/token',
    userInfoURL: APE_SSO_URL + '/api/client/auth/userinfo',
};

@Injectable()
export class ApeSsoStrategy extends PassportStrategy(Strategy, GUARD_CODE) {
    constructor() {
        super({
            authorizationURL: ENPOINTS.authorizationURL,
            tokenURL: ENPOINTS.tokenURL,
            clientID: APE_CLIENT_ID,
            clientSecret: APE_CLIENT_SECRET,
            callbackURL: APE_CALLBACK_URL,
            scope: ['email', 'profile'],
            passReqToCallback: true,
        });
    }

    async userProfile(accessToken: string, done: (err?: unknown, profile?: any) => void) {
        try {
            const profile = await optionalApiConnector.get<ApeSsoUserInfo>(
                ENPOINTS.userInfoURL,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    },
                },
            );
            done(null, profile);
        } catch (err) {
            done(err);
        }
    }

    async validate(
        req: express.Request, // 👈 có query.state ở đây
        accessToken: string,
        refreshToken: string,
        profile: ApeSsoUserInfo,
        done: VerifyCallback,
    ) {
        //console.log('PARAMS:', { accessToken, refreshToken, profile, done });

        const { id, username, fullName, avatar, tenantId, type } = profile;

        const redirectUri = decodeURIComponent((req.query.state as string) || '');
        //console.log('redirectUri in validate:', redirectUri);

        const user = {
            id,
            username,
            fullName,
            avatar,
            tenantId,
            type,
            redirectUri, // 👈 gắn lại cho req.user
        } as ApeSsoUserInfo & { redirectUri: string };

        done(null, user);
    }
}
