import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsDateString, IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSCampaign } from '~/common/enums/campaign.enum';

export class CreateCampaignDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    code?: string;

    @ApiProperty({ example: 'Chiến dịch khuyến mãi mùa hè' })
    @MaxLength(255)
    @IsOptional()
    @IsString()
    name: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    description?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    content?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsDateString()
    sendDate?: Date;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    mailSend?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    customerIds?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    groupIds?: string[];

    @ApiPropertyOptional({
        enum: NSCampaign.EStatus,
        default: NSCampaign.EStatus.NEW,
    })
    @IsOptional()
    @IsEnum(NSCampaign.EStatus)
    status?: NSCampaign.EStatus;
}

export class ListCampaignDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Tìm kiếm theo mã' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ description: 'Tìm kiếm theo tên' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({
        enum: NSCampaign.EStatus,
        example: NSCampaign.EStatus.NEW,
    })
    @IsOptional()
    status?: NSCampaign.EStatus;

    @ApiPropertyOptional({ description: 'Ngày gửi từ' })
    @IsOptional()
    @IsDateString()
    sendDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày gửi đến' })
    @IsOptional()
    @IsDateString()
    sendDateTo?: string;

    @ApiPropertyOptional({ description: 'Ngày tạo từ' })
    @IsOptional()
    @IsDateString()
    createdDateFrom?: string;

    @ApiPropertyOptional({ description: 'Ngày tạo đến' })
    @IsOptional()
    @IsDateString()
    createdDateTo?: string;

    @ApiPropertyOptional({ description: 'Lọc theo người gửi' })
    @IsOptional()
    @IsString()
    mailSend?: string;

    @ApiPropertyOptional({ description: 'Lọc theo người tạo' })
    @IsOptional()
    @IsString()
    createdBy?: string;
}
export class CampaignDto {}

export class UpdateCampaignDto extends CreateCampaignDto {
    @ApiPropertyOptional({ description: 'ID chiến dịch' })
    @IsOptional()
    @IsString()
    id: string;
}
