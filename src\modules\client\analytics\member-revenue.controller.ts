import { Query } from '@nestjs/common';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { ListRevenueReportDto } from './dto/revenue-report.dto';
import { MemberRevenueService } from './member-revenue.service';
import { ListMemberRevenueDto } from './dto/member-revenue';

@DefController('member-revenue')
export class MemberRevenueController {
    constructor(private readonly memberRevenueService: MemberRevenueService) {}

    @DefGet('list')
    async listRevenueReport(@Query() query: ListMemberRevenueDto) {
        return this.memberRevenueService.listRevenueReport(query);
    }
}
