import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { PERMISSION_CODES, PermissionGuard, RequirePermissions } from '../@guards';
import {
    AssignRoleDto,
    CreateMemberDto,
    MemberPaginationDto,
    UpdateMemberDto,
    UpdateStatusDto,
} from './dto/member.dto';
import { MemberService } from './member.service';
import { ChangePasswordReq } from '../member-auth/dto';

@UseGuards(PermissionGuard)
@DefController('member')
export class MemberController {
    constructor(private readonly memberService: MemberService) { }

    @DefGet('pagination')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async pagination(@Query() dto: MemberPaginationDto) {
        return this.memberService.pagination(dto);
    }

    @DefPost('get-member-by-ids')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.VIEW])
    async getMemberByIds(@Body() body: string[]) {
        return this.memberService.getMemberByIds(body);
    }

    // Phân quyền Role cho nhân viên
    @DefPost('assign-role')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async assignRoleMember(@Body() body: AssignRoleDto) {
        return this.memberService.assignRoleMember(body);
    }

    // Tạo mới thành viên
    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.CREATE])
    async create(@Body() body: CreateMemberDto) {
        return this.memberService.create(body);
    }

    // Cập nhật thông tin thành viên
    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async update(@Body() body: UpdateMemberDto) {
        return this.memberService.update(body);
    }

    // Kích hoạt / Dừng kích hoạt thành viên
    @DefPost('set-active')
    @RequirePermissions([PERMISSION_CODES.SETTING_MEMBER.UPDATE])
    async setActive(@Body() body: UpdateStatusDto) {
        return this.memberService.setActive(body);
    }

    @DefPost("change-password")
    changePassword(@Body() body: ChangePasswordReq) {
        return this.memberService.changePassword(body);
    }
}
