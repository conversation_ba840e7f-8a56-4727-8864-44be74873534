import { Injectable } from "@nestjs/common";
import { DefTransaction, InjectRepo } from "nestjs-typeorm3-kit";
import { CatalogServiceRepo } from "~/domains/primary";
import { CreateCatalogDto, ListCatalogDto, UpdateCatalogDto } from "./dto/catalog.dto";
import { generateCodeHelper } from "~/common/helpers";
import { Between, ILike } from "typeorm";
import { BusinessException } from "~/@systems/exceptions";
import { NSCatalog } from "~/common/enums";

@Injectable()
export class CatalogService {
    constructor(
        @InjectRepo(CatalogServiceRepo) private catalogServiceRepo: CatalogServiceRepo,
    ) { }

    async listCatalog(params: ListCatalogDto) {
        const where: any = {};
        if (params.q) {
            where.q = ILike(`%${params.q}%`);
        }
        if (params.type) {
            where.type = params.type;
        }
        if (params.status) {
            where.status = params.status;
        }
        if (params.createdFrom && params.createdTo) {
            where.createdDate = Between(params.createdFrom, params.createdTo);
        }
        return this.catalogServiceRepo.findPagination({
            where,
            order: {
                createdDate: 'DESC',
            }
        }, {
            pageIndex: params.pageIndex,
            pageSize: params.pageSize,
        });
    }

    async detailCatalog(id: string) {
        const check = await this.catalogServiceRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        return check;
    }

    @DefTransaction()
    async createCatalog(catalog: CreateCatalogDto) {
        const code = generateCodeHelper.generateCustomerCode('CAT');
        const q = `${catalog.name}${code}`;
        return this.catalogServiceRepo.save({ ...catalog, code, q });
    }

    @DefTransaction()
    async updateCatalog(catalog: UpdateCatalogDto) {
        const { id, ...catalogData } = catalog;
        const check = await this.detailCatalog(id);
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogServiceRepo.update(id, catalogData);
        return updatedCatalog;
    }

    //Active Catalog
    @DefTransaction()
    async activeCatalog(id: string) {
        const check = await this.catalogServiceRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogServiceRepo.update(id, { status: NSCatalog.EStatus.ACTIVE });
        return updatedCatalog;
    }
    
    //Inactive Catalog
    @DefTransaction()
    async inActiveCatalog(id: string) {
        const check = await this.catalogServiceRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogServiceRepo.update(id, { status: NSCatalog.EStatus.INACTIVE });
        return updatedCatalog;
    }
}