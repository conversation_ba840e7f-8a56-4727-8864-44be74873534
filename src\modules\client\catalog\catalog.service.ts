import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSCatalog } from '~/common/enums';
import { CatalogRepo } from '~/domains/primary';
import { CreateCatalogDto, ListCatalogDto, UpdateCatalogDto } from './dto/catalog.dto';

@Injectable()
export class CatalogService {
    constructor(@InjectRepo(CatalogRepo) private catalogRepo: CatalogRepo) {}

    async listCatalog(params: ListCatalogDto) {
        const where: any = {};
        if (params.name) {
            where.name = ILike(`%${params.name}%`);
        }

        if (params.status) {
            where.status = params.status;
        }
        if (params.createdFrom && params.createdTo) {
            where.createdDate = Between(params.createdFrom, params.createdTo);
        }
        return this.catalogRepo.findPaginationByTenant(
            {
                where,
                order: {
                    createdDate: 'DESC',
                },
            },
            {
                pageIndex: params.pageIndex,
                pageSize: params.pageSize,
            },
        );
    }

    async detailCatalog(id: string) {
        const check = await this.catalogRepo.findOneByTenant({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        return check;
    }

    @DefTransaction()
    async createCatalog(catalog: CreateCatalogDto) {
        return this.catalogRepo.insert({ ...catalog });
    }

    @DefTransaction()
    async updateCatalog(catalog: UpdateCatalogDto) {
        const { id, ...catalogData } = catalog;
        const check = await this.detailCatalog(id);
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogRepo.update(id, { ...catalogData, id });
        return updatedCatalog;
    }

    //Active Catalog
    @DefTransaction()
    async activeCatalog(id: string) {
        const check = await this.catalogRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogRepo.update(id, {
            status: NSCatalog.EStatus.ACTIVE,
            id,
        });
        return updatedCatalog;
    }

    //Inactive Catalog
    @DefTransaction()
    async inActiveCatalog(id: string) {
        const check = await this.catalogRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException('Catalog not found');
        }
        const updatedCatalog = await this.catalogRepo.update(id, {
            status: NSCatalog.EStatus.INACTIVE,
            id,
        });
        return updatedCatalog;
    }

    // Select Box
    async selectBox(pageSize: number) {
        const data = await this.catalogRepo.findPaginationByTenant(
            {
                where: { status: NSCatalog.EStatus.ACTIVE },
            },
            {
                pageIndex: 1,
                pageSize,
            },
        );
        return data;
    }
}
