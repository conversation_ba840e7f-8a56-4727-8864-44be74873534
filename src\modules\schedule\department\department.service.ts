import { Injectable } from "@nestjs/common";
import { DefTransaction, InjectRepo } from "ape-nestjs-typeorm3-kit";
import { DepartmentRepo } from "~/domains/primary";
import { apeAuthApiConnector } from "~/connectors/api.connector";

@Injectable()
export class DepartmentService {
    constructor(
        @InjectRepo(DepartmentRepo) private departmentRepo: DepartmentRepo,
    ) { }

    @DefTransaction()
    async compareWithAuth() {
        const [departmentHRM, departmentAuth] = await Promise.all([
            this.departmentRepo.find(),
            apeAuthApiConnector
                .get('/api/public/crm/department/find-all', { pageSize: -1, pageIndex: 1 })
                .then(res => res.data),
        ]);

        // Xử lý tạo mới
        const departmentToCreate = departmentAuth.filter(
            auth => !departmentHRM.some(hrm => hrm.departmentAuthId === auth.id),
        );
        for (const auth of departmentToCreate) {
            await this.departmentRepo.save({ ...auth, departmentAuthId: auth.id });
        }

        // Xử lý xóa
        const departmentToDelete = departmentHRM.filter(
            hrm => !departmentAuth.some(auth => auth.id === hrm.departmentAuthId),
        );
        for (const hrm of departmentToDelete) {
            await this.departmentRepo.delete(hrm.id);
        }

        // Xử lý cập nhật nếu thuộc tính thay đổi
        for (const auth of departmentAuth) {
            const department = departmentHRM.find(hrm => hrm.departmentAuthId === auth.id);
            if (department) {
                let needsUpdate = false;
                if (department.code !== auth.code) {
                    department.code = auth.code;
                    needsUpdate = true;
                }
                if (department.name !== auth.name) {
                    department.name = auth.name;
                    needsUpdate = true;
                }
                if (department.status !== auth.status) {
                    department.status = auth.status;
                    needsUpdate = true;
                }
                if (department.description !== auth.description) {
                    department.description = auth.description;
                    needsUpdate = true;
                }

                if (department.parentId !== auth.parentId) {
                    department.parentId = auth.parentId;
                    needsUpdate = true;
                }
                // Thêm các trường khác nếu cần so sánh (ví dụ: description, status, etc.)
                if (needsUpdate) {
                    const { id, ...updateData } = department;
                    await this.departmentRepo.update(id, updateData);
                }
            }
        }
    }
}