import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { In } from 'typeorm';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import { NSMember } from '~/common/enums';
import { apeAuthApiConnector } from '~/connectors';
import { MemberAuthProviderRepo, MemberEntity, MemberRepo, RoleRepo } from '~/domains/primary';
import { PERMISSION_CODES } from '../@guards';
import { clientSessionContext } from '../client-session.context';
import { UploadService } from '../upload/upload.service';
import { ChangePasswordReq, UpdateUserInfoReq } from './dto';
@Injectable()
export class MemberAuthService {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
        @InjectRepo(MemberAuthProviderRepo) private memberAuthProviderRepo: MemberAuthProviderRepo,
        @InjectRepo(RoleRepo) private roleRepo: RoleRepo,
        private readonly uploadService: UploadService,
    ) {}

    private async generateRefreshToken(memberId: string) {
        const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
        const newRefreshToken = await this.jwtService.signAsync(
            { sub: memberId },
            {
                secret: JWT_REFRESH_TOKEN_SECRET,
                expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
            },
        );

        return newRefreshToken;
    }

    private clearPrivateMemberData(member: MemberEntity) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = member;
        return rest;
    }

    private async makeAuthResponse(member: MemberEntity) {
        const pipeMember = this.clearPrivateMemberData(member);
        const payload = {
            sub: member.id,
            tenantId: member.tenantId,
            ssoAccountId: member.ssoAccountId,
            ...pipeMember,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.generateRefreshToken(member.id),
            tokenType: 'Bearer',
            ...pipeMember,
        };
    }

    async apeLogin(profile: {
        username: string;
        providerId: string;
        fullName?: string;
        avatar?: string;
        tenantId?: string;
        type?: NSMember.EType;
        ssoAccountId?: string;
    }) {
        const { username, providerId, fullName, avatar, ssoAccountId } = profile;
        let member = await this.memberRepo.findOne({
            where: {
                username,
            },
        });
        if (!member) {
            member = await this.memberRepo.save({
                username,
                fullName,
                avatar,
                tenantId: profile.tenantId,
                status: NSMember.EStatus.ACTIVE,
                type: profile.type,
                ssoAccountId,
            });

            await this.memberAuthProviderRepo.save({
                memberId: member.id,
                provider: NSMember.EAuthProviderType.APE,
                providerId,
            });
            return this.makeAuthResponse(member);
        }

        const provider = await this.memberAuthProviderRepo.findOne({
            where: {
                provider: NSMember.EAuthProviderType.APE,
                providerId,
            },
        });
        if (!provider) {
            await this.memberAuthProviderRepo.save({
                memberId: member.id,
                provider: NSMember.EAuthProviderType.APE,
                providerId,
            });
            return this.makeAuthResponse(member);
        }
        const rs = await this.makeAuthResponse(member);
        return { ...rs, tenantId: profile.tenantId };
    }

    async getUserInfo({ id }: { id: string }) {
        try {
            const { APPLICATION_CODE } = configEnv();

            // 1) Lấy member
            const member = await this.memberRepo.findOne({ where: { id } });
            if (!member) {
                throw new UnauthorizedException('Không tìm thấy người dùng');
            }

            // 2) Lấy danh sách features khả dụng của tenant theo application
            const userAuth = await apeAuthApiConnector.post(
                `/api/public/crm/member/get-tenant-role`,
                {
                    tenantId: member.tenantId,
                    applicationCode: APPLICATION_CODE,
                },
            );
            const features: string[] = Array.isArray(userAuth?.features) ? userAuth.features : [];
            const featureSet = new Set(features.map(f => (f || '').toUpperCase().trim()));

            // 3) Lấy roles theo roleIds của member
            const roles = (
                member.roleIds?.length
                    ? await this.roleRepo.find({ where: { id: In(member.roleIds) } })
                    : []
            ) as Array<any>; // role có trường permissionCodes: string[]

            // 3.1) Nếu member là TENANT_MASTER thì trả về tất cả permission theo feature
            if (member.type === NSMember.EType.TENANT_MASTER) {
                const features = userAuth.features.map(f => (f || '').toUpperCase().trim());
                const permissions = Object.entries(PERMISSION_CODES)
                    .filter(([key]) => features.includes(key)) // so khớp theo tên nhóm cha
                    .flatMap(([, group]) => Object.values(group)); // gom toàn bộ quyền trong nhóm
                const uniquePermissions = [...new Set(permissions)];

                return {
                    ...this.clearPrivateMemberData(member),
                    roles: [], // Sẽ không có role
                    permissionCodes: uniquePermissions, // Lấy full quyền tính năng theo gói
                    features: features, // Tính năng trong gói
                };
            }

            // Helper: lấy prefix (trước dấu chấm) hoặc toàn bộ nếu không có dấu chấm
            const getPrefix = (perm: string) => {
                const safe = (perm || '').trim();
                const dotIdx = safe.indexOf('.');
                return (dotIdx >= 0 ? safe.slice(0, dotIdx) : safe).toUpperCase();
            };

            // 4) Lọc permissionCodes theo featureSet cho từng role
            const rolesWithFilteredPerms = roles.map(role => {
                const originalPerms: string[] = Array.isArray(role.permissionCodes)
                    ? role.permissionCodes
                    : [];
                const filteredPerms = originalPerms.filter(perm => featureSet.has(getPrefix(perm)));
                return {
                    ...role,
                    permissionCodes: filteredPerms,
                };
            });

            // 5) (Tuỳ chọn) Gộp toàn bộ permission sau khi đã lọc — tiện cho client
            const permissionCodes = Array.from(
                new Set(rolesWithFilteredPerms.flatMap(r => r.permissionCodes as string[])),
            );

            // 6) Trả về dữ liệu
            return {
                ...this.clearPrivateMemberData(member),
                //features,
                roles: rolesWithFilteredPerms,
                //permissionCodes,
            };
        } catch (error) {
            throw error;
        }
    }

    async updateUserInfo(file: Express.Multer.File, data: UpdateUserInfoReq) {
        try {
            const member = await this.memberRepo.findOne({
                where: { username: data.username },
            });
            if (!member) {
                throw new UnauthorizedException('Không tìm thấy người dùng');
            }
            let uploadFile: any = null;
            if (file && file.size > 0) {
                try {
                    uploadFile = await this.uploadService.uploadSingle(file);
                } catch (error) {
                    throw new BusinessException('Lỗi không upload được file ');
                }
            }

            await this.memberRepo.update(member.id, {
                ...data,
                avatar: uploadFile?.fileUrl,
            });
            return this.getUserInfo({ id: member.id });
        } catch (error) {
            throw error;
        }
    }

    async changePassword(data: ChangePasswordReq) {
        try {
            const { ssoAccountId } = clientSessionContext;
            console.log('======>>>>ssoAccountId', clientSessionContext);
            if (!ssoAccountId) {
                throw new BusinessException('Không tìm thấy thông tin người dùng');
            }

            await apeAuthApiConnector.post(`/api/public/crm/member/change-password`, {
                id: ssoAccountId,
                oldPassword: data.oldPassword,
                newPassword: data.newPassword,
            });
        } catch (error) {
            throw new BusinessException(
                error.response?.data?.message || 'Cập nhật mật khẩu thất bại',
            );
        }
    }
}
