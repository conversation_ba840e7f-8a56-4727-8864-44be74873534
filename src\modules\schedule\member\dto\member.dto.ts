import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { NSMember } from '~/common/enums';

export class CreateMemberDto {
    @ApiProperty({ example: 'tenant123', description: 'ID của tenant' })
    @IsNotEmpty({ message: 'Tenant ID không được để trống' })
    @IsString({ message: 'Tenant ID phải là chuỗi' })
    tenantId: string;

    @ApiProperty({ example: '<EMAIL>', description: 'Tên đăng nhập của thành viên' })
    @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
    @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
    username: string;

    @ApiPropertyOptional({ description: 'Họ và tên đầy đủ của thành viên' })
    @IsOptional()
    @IsString({ message: 'Họ và tên phải là chuỗi' })
    fullName?: string;

    @ApiPropertyOptional({ description: 'URL ảnh đại diện của thành viên' })
    @IsOptional()
    @IsString({ message: 'Avatar phải là chuỗi' })
    avatar?: string;

    @ApiPropertyOptional({ description: 'ID phòng ban của thành viên' })
    @IsOptional()
    @IsString({ message: 'ID phòng ban phải là chuỗi' })
    departmentId?: string;

    @ApiPropertyOptional({ description: 'ID chức vụ của thành viên' })
    @IsOptional()
    @IsString({ message: 'ID chức vụ phải là chuỗi' })
    positionId?: string;

    @ApiProperty({ enum: NSMember.EStatus, description: 'Trạng thái của thành viên' })
    @IsNotEmpty({ message: 'Trạng thái không được để trống' })
    @IsEnum(NSMember.EStatus, { message: 'Trạng thái không hợp lệ' })
    status: NSMember.EStatus;
}

export class UpdateMemberDto {
    @ApiProperty({ example: 'member123', description: 'ID của thành viên' })
    @IsNotEmpty({ message: 'Member ID không được để trống' })
    id: string;

    @ApiPropertyOptional({ description: 'Họ và tên đầy đủ của thành viên' })
    @IsOptional()
    @IsString({ message: 'Họ và tên phải là chuỗi' })
    fullName?: string;

    @ApiPropertyOptional({
        example: '<EMAIL>',
        description: 'Tên đăng nhập của thành viên',
    })
    @IsOptional()
    @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
    username?: string;

    @ApiPropertyOptional({ description: 'URL ảnh đại diện của thành viên' })
    @IsOptional()
    @IsString({ message: 'Avatar phải là chuỗi' })
    avatar?: string;

    @ApiPropertyOptional({ description: 'ID phòng ban của thành viên' })
    @IsOptional()
    @IsString({ message: 'ID phòng ban phải là chuỗi' })
    departmentId?: string;

    @ApiPropertyOptional({ description: 'ID chức vụ của thành viên' })
    @IsOptional()
    @IsString({ message: 'ID chức vụ phải là chuỗi' })
    positionId?: string;

    @ApiPropertyOptional({ enum: NSMember.EStatus, description: 'Trạng thái của thành viên' })
    @IsOptional()
    @IsEnum(NSMember.EStatus, { message: 'Trạng thái không hợp lệ' })
    status?: NSMember.EStatus;

    @ApiPropertyOptional({ description: 'ID vai trò của thành viên' })
    @IsOptional()
    roleIds?: string[];
}

export class SetActiveMemberDto {
    @ApiProperty({ description: 'Tên đăng nhập của thành viên' })
    @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
    @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
    username: string;

    @ApiProperty({ description: 'Trạng thái của thành viên' })
    @IsNotEmpty({ message: 'Trạng thái không được để trống' })
    @IsEnum(NSMember.EStatus, { message: 'Trạng thái không hợp lệ' })
    status: NSMember.EStatus;
}
