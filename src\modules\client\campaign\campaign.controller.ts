import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { NSCampaign } from '~/common/enums/campaign.enum';
import { PERMISSION_CODES, RequirePermissions } from '../@guards';
import { PermissionGuard } from '../@guards/permission/permission.guard';
import { CampaignService } from './campaign.service';
import { CreateCampaignDto, ListCampaignDto, UpdateCampaignDto } from './dto/campaign.dto';

@UseGuards(PermissionGuard)
@ApiTags('Campaign')
@DefController('campaign')
export class CampaignController {
    constructor(private readonly campaignService: CampaignService) {}

    @DefGet('list')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.VIEW])
    @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách chiến dịch' })
    async list(@Query() query: ListCampaignDto) {
        return this.campaignService.listCampaign(query);
    }

    @DefPost('create')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.CREATE])
    @ApiOperation({ summary: 'Tạo mới chiến dịch' })
    async create(@Body() body: CreateCampaignDto) {
        return this.campaignService.createCampaign(body);
    }

    @DefPost('update')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.UPDATE])
    @ApiOperation({ summary: 'Cập nhật chiến dịch' })
    @ApiParam({ name: 'id', description: 'ID của chiến dịch' })
    async update(@Param('id') id: string, @Body() body: UpdateCampaignDto) {
        body.id = id;
        return this.campaignService.updateCampaign(body);
    }

    @DefGet('detail')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.VIEW])
    @ApiOperation({ summary: 'Lấy chi tiết chiến dịch' })
    @ApiParam({ name: 'id', description: 'ID của chiến dịch' })
    async detail(@Query('id') id: string) {
        return this.campaignService.detailCampaign(id);
    }

    @DefPost('update-status')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.UPDATE])
    @ApiOperation({ summary: 'Cập nhật trạng thái chiến dịch' })
    @ApiParam({ name: 'id', description: 'ID của chiến dịch' })
    @ApiQuery({
        name: 'status',
        enum: NSCampaign.EStatus,
        description: 'Trạng thái mới của chiến dịch',
    })
    async updateStatus(@Body() body: { id: string; status: NSCampaign.EStatus }) {
        return this.campaignService.updateStatus(body.id, body.status);
    }

    @DefPost('send-now')
    @RequirePermissions([PERMISSION_CODES.SETTING_CAMPAIGN.UPDATE])
    @ApiOperation({ summary: 'Gửi chiến dịch ngay lập tức' })
    async sendNow(@Body() body: { id: string }) {
        return this.campaignService.sendNow(body.id);
    }
}
