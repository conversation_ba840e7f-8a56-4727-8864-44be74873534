import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSMedia } from '~/common/enums';

export class ListMediaDto extends PageRequest {
    @ApiPropertyOptional({
        description: 'Filter theo loại media',
        enum: NSMedia.EType,
        example: NSMedia.EType.IMAGE,
    })
    @IsOptional()
    @IsEnum(NSMedia.EType)
    type?: NSMedia.EType;
}

export class MediaItemResponse {
    @ApiProperty({ description: 'ID của media item' })
    id: string;

    @ApiProperty({ description: 'URL của file' })
    url: string;

    @ApiPropertyOptional({ description: 'Tên file' })
    fileName?: string;

    @ApiPropertyOptional({ description: 'Content type (MIME type)' })
    contentType?: string;

    @ApiProperty({ description: 'Loại media', enum: NSMedia.EType })
    mediaType: NSMedia.EType;

    @ApiPropertyOptional({ description: '<PERSON>ích thước file (bytes)' })
    size?: number;

    @ApiProperty({ description: 'Ngày tạo' })
    createdAt: Date;

    @ApiPropertyOptional({ description: 'Nguồn của media (entity name)' })
    entity?: string;
}

export class UploadMediaResponseDto {
    @ApiProperty({ description: 'ID của media' })
    id: string;

    @ApiProperty({ description: 'URL của file' })
    url: string;

    @ApiPropertyOptional({ description: 'Tên file' })
    fileName?: string;

    @ApiPropertyOptional({ description: 'Content type (MIME type)' })
    contentType?: string;

    @ApiProperty({ description: 'Loại media', enum: NSMedia.EType })
    mediaType: NSMedia.EType;

    @ApiPropertyOptional({ description: 'Kích thước file (bytes)' })
    size?: number;
}

