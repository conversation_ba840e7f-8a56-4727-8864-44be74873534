import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSQuote } from '~/common/enums';

export class QuoteItemsDto {
    @ApiPropertyOptional({ description: 'ID sản phẩm' })
    @IsOptional()
    @IsString()
    catalogItemId?: string;

    @ApiPropertyOptional({ description: 'Mã sản phẩm' })
    @IsOptional()
    @IsString()
    code?: string;

    @ApiPropertyOptional({ description: 'Tên sản phẩm' })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ description: 'Loại sản phẩm' })
    @IsString()
    @IsNotEmpty()
    type: string;

    @ApiPropertyOptional({ description: 'Mô tả' })
    @IsOptional()
    description?: any;

    @ApiProperty({ description: 'Đơn vị' })
    @IsString()
    @IsNotEmpty()
    unit: string;

    @ApiProperty({ description: 'Đơn vị tiền tệ' })
    @IsString()
    @IsNotEmpty()
    currency: string;

    @ApiProperty({ description: 'Đơn giá' })
    @IsNumber()
    @Type(() => Number)
    unitPrice: number;

    @ApiProperty({ description: 'Thuế %' })
    @IsNumber()
    @Type(() => Number)
    vat: number;

    @ApiProperty({ description: 'Số lượng' })
    @IsNumber()
    @Type(() => Number)
    quantity: number;

    @ApiProperty({ description: 'Tổng tiền trước VAT' })
    @IsNumber()
    @Type(() => Number)
    totalBeforeVat: number;

    @ApiProperty({ description: 'Tổng tiền sau VAT' })
    @IsNumber()
    @Type(() => Number)
    totalAfterVat: number;
}

export class QuoteItemDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class CreateQuoteDto {
    @ApiProperty({ description: 'ID khách hàng' })
    @IsString()
    @IsNotEmpty()
    customerId: string;

    @ApiProperty({ description: 'ID nhân viên tạo' })
    @IsString()
    @IsNotEmpty()
    memberId: string;

    @ApiPropertyOptional({ description: 'Số báo giá' })
    @IsOptional()
    @IsString()
    quotationNumber?: string;

    @ApiPropertyOptional({ description: 'ID địa chỉ khách hàng' })
    @IsOptional()
    @IsString()
    customerAddressId?: string;

    @ApiPropertyOptional({ description: 'ID người liên hệ khách hàng' })
    @IsOptional()
    @IsString()
    customerContactId?: string;

    @ApiPropertyOptional({ description: 'Ngày báo giá', example: '2024-01-15' })
    @IsOptional()
    @IsString()
    quotationDate?: string;

    @ApiPropertyOptional({ description: 'Tên khách hàng' })
    @IsOptional()
    @IsString()
    customerName?: string;

    @ApiPropertyOptional({ description: 'Địa chỉ khách hàng' })
    @IsOptional()
    @IsString()
    customerAddress?: string;

    @ApiPropertyOptional({ description: 'Điện thoại khách hàng' })
    @IsOptional()
    @IsString()
    customerPhone?: string;

    @ApiPropertyOptional({ description: 'Mã số thuế khách hàng' })
    @IsOptional()
    @IsString()
    customerTaxCode?: string;

    @ApiPropertyOptional({ description: 'Ngày giao hàng' })
    @IsOptional()
    @IsString()
    deliveryDate?: string;

    @ApiPropertyOptional({ description: 'Địa chỉ giao hàng' })
    @IsOptional()
    @IsString()
    deliveryLocation?: string;

    @ApiPropertyOptional({ description: 'Số ngày hiệu lực' })
    @IsOptional()
    @Type(() => Number)
    validityDays?: number;

    @ApiPropertyOptional({ description: 'Thời hạn thanh toán' })
    @IsOptional()
    @IsString()
    dueDate?: string;

    @ApiPropertyOptional({ description: 'Ghi chú' })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiPropertyOptional({ description: 'Tổng giá trị báo giá' })
    @IsOptional()
    @Type(() => Number)
    totalAmount?: number;

    @ApiPropertyOptional({ description: 'Tệp đính kèm' })
    @IsOptional()
    @IsArray()
    @Type(() => String)
    documentsUrls?: string[];

    @ApiPropertyOptional({ description: 'Hình ảnh' })
    @IsOptional()
    @IsArray()
    @Type(() => String)
    imagesUrls?: string[];

    @ApiProperty({ description: 'Danh sách sản phẩm báo giá' })
    @IsNotEmpty()
    quotationProducts: QuoteItemsDto[];
}

export class ListQuoteDto extends PageRequest {
    @ApiPropertyOptional({ example: 'QT-2024-001', description: 'Số báo giá' })
    @IsString()
    @IsOptional()
    quotationNumber?: string;

    @ApiPropertyOptional({ example: 'a1b2c3d4-...', description: 'ID khách hàng' })
    @IsString()
    @IsOptional()
    customerId?: string;

    @ApiPropertyOptional({ example: 'Nguyễn Văn A', description: 'Tên khách hàng' })
    @IsString()
    @IsOptional()
    customerName?: string;

    @ApiPropertyOptional({ enum: NSQuote.EStatus, example: NSQuote.EStatus.NEW })
    @IsOptional()
    @IsEnum(NSQuote.EStatus)
    status?: NSQuote.EStatus;

    @ApiPropertyOptional({ example: '2024-01-01' })
    @IsOptional()
    quotationFrom?: string;

    @ApiPropertyOptional({ example: '2024-12-31' })
    @IsOptional()
    quotationTo?: string;

    @ApiPropertyOptional({ example: '2024-01-01' })
    @IsOptional()
    createdFrom?: string;

    @ApiPropertyOptional({ example: '2024-12-31' })
    @IsOptional()
    createdTo?: string;

    @ApiPropertyOptional({ example: '2024-01-01' })
    @IsOptional()
    expiryFrom?: string;

    @ApiPropertyOptional({ example: '2024-12-31' })
    @IsOptional()
    expiryTo?: string;

    @ApiPropertyOptional({ example: 'a1b2c3d4-...', description: 'ID nhân viên' })
    @IsString()
    @IsOptional()
    createdBy?: string;

    @ApiPropertyOptional({ example: '1000000', description: 'Giá trị báo giá từ' })
    @IsNumber()
    @IsOptional()
    @Type(() => Number)
    priceFrom?: number = 0;

    @ApiPropertyOptional({ example: '5000000', description: 'Giá trị báo giá đến' })
    @IsNumber()
    @IsOptional()
    @Type(() => Number)
    priceTo?: number;
}

export class UpdateQuoteDto extends PartialType(CreateQuoteDto) {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;
}

export class UpdateStatusDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ enum: NSQuote.EStatus, example: NSQuote.EStatus.NEW })
    @IsEnum(NSQuote.EStatus)
    status: NSQuote.EStatus;
}

export class SendQuoteEmailDto {
    @ApiProperty({ example: 'a1b2c3d4-...' })
    @IsString()
    @IsNotEmpty()
    id: string;

    @ApiProperty({ example: '<EMAIL>' })
    @IsString()
    @IsNotEmpty()
    emailFrom: string;
}
